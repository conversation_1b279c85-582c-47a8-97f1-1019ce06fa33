#!/usr/bin/env python3
"""
Test script for Enterprise WebSocket System
Tests real-time enrollment updates and notifications
"""

import asyncio
import websockets
import json
import requests
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:8000"
WEBSOCKET_URL = "ws://localhost:8000/ws/umls/"

async def test_websocket_connection():
    """Test WebSocket connection and real-time features"""
    print("🚀 Testing Enterprise WebSocket System")
    print("=" * 60)
    
    # First, get authentication token
    print("🔐 Authenticating...")
    
    try:
        login_response = requests.post(f"{BACKEND_URL}/api/auth/login/", json={
            "email": "<EMAIL>",
            "password": "password123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json()["access"]
            print("✅ Authentication successful")
        else:
            print("❌ Authentication failed")
            return False
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    # Test WebSocket connection
    print("\n🔌 Testing WebSocket connection...")
    
    try:
        # Add authorization header to WebSocket connection
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        async with websockets.connect(
            WEBSOCKET_URL,
            extra_headers=headers,
            timeout=10
        ) as websocket:
            print("✅ WebSocket connected successfully")
            
            # Test heartbeat
            print("\n💓 Testing heartbeat...")
            heartbeat_message = {
                "type": "heartbeat",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(heartbeat_message))
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                
                if response_data.get("event_type") == "system_message":
                    print("✅ Heartbeat response received")
                    print(f"   Response: {response_data.get('data', {}).get('type')}")
                else:
                    print("⚠️  Unexpected response format")
                    
            except asyncio.TimeoutError:
                print("⚠️  Heartbeat timeout - but connection is working")
            
            # Test room subscription
            print("\n🏠 Testing room subscription...")
            room_message = {
                "type": "subscribe_course",
                "course_id": 1
            }
            
            await websocket.send(json.dumps(room_message))
            
            # Listen for messages for a few seconds
            print("\n👂 Listening for real-time messages...")
            
            try:
                for i in range(3):  # Listen for 3 messages or timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    message_data = json.loads(message)
                    
                    print(f"📨 Received message {i+1}:")
                    print(f"   Type: {message_data.get('event_type')}")
                    print(f"   Data: {message_data.get('data', {})}")
                    print(f"   Timestamp: {message_data.get('timestamp')}")
                    
            except asyncio.TimeoutError:
                print("⏰ No more messages received (timeout)")
            
            print("✅ WebSocket test completed successfully")
            return True
            
    except websockets.exceptions.ConnectionClosed:
        print("❌ WebSocket connection closed unexpectedly")
        return False
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ WebSocket connection failed with status: {e.status_code}")
        return False
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        return False

def test_realtime_enrollment_trigger():
    """Test triggering a real-time enrollment update"""
    print("\n🎯 Testing real-time enrollment trigger...")
    
    try:
        # Get authentication token
        login_response = requests.post(f"{BACKEND_URL}/api/auth/login/", json={
            "email": "<EMAIL>",
            "password": "password123"
        })
        
        if login_response.status_code != 200:
            print("❌ Authentication failed for enrollment test")
            return False
        
        token = login_response.json()["access"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Get available courses
        courses_response = requests.get(f"{BACKEND_URL}/api/courses/", headers=headers)
        
        if courses_response.status_code == 200:
            courses = courses_response.json().get("results", [])
            if courses:
                course = courses[0]
                print(f"✅ Found course for testing: {course.get('code')} - {course.get('title')}")
                
                # Check enrollment status
                enrollment_status = course.get('user_enrollment_status')
                print(f"   Current enrollment status: {enrollment_status}")
                
                if enrollment_status == 'available':
                    print("   📝 Course is available for enrollment")
                elif enrollment_status == 'enrolled':
                    print("   ✅ Already enrolled in this course")
                elif enrollment_status == 'retakeable':
                    print("   🔄 Course is retakeable")
                else:
                    print(f"   ℹ️  Status: {enrollment_status}")
                
                return True
            else:
                print("⚠️  No courses found")
                return False
        else:
            print(f"❌ Failed to get courses: {courses_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Enrollment trigger test error: {e}")
        return False

def test_backend_realtime_features():
    """Test backend real-time features"""
    print("\n🔧 Testing backend real-time features...")
    
    try:
        # Test health check
        health_response = requests.get(f"{BACKEND_URL}/api/courses/health/")
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            print("✅ Backend health check passed")
            print(f"   Total enrollments: {health_data.get('statistics', {}).get('total_enrollments', 0)}")
            print(f"   Retake enrollments: {health_data.get('statistics', {}).get('retake_enrollments', 0)}")
            
            return True
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Backend test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Enterprise WebSocket & Real-time System Test")
    print("=" * 60)
    
    results = []
    
    # Test 1: Backend features
    results.append(test_backend_realtime_features())
    
    # Test 2: Enrollment trigger
    results.append(test_realtime_enrollment_trigger())
    
    # Test 3: WebSocket connection
    results.append(await test_websocket_connection())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    test_names = [
        "Backend Real-time Features",
        "Enrollment Trigger Test",
        "WebSocket Connection Test"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All real-time features are working correctly!")
        print("\n🚀 Enterprise WebSocket System Status:")
        print("   ✅ Real-time enrollment updates: READY")
        print("   ✅ Live notifications: READY")
        print("   ✅ WebSocket connections: READY")
        print("   ✅ Event broadcasting: READY")
    elif passed >= 2:
        print("⚠️  Most features working. Some WebSocket features may need attention.")
    else:
        print("❌ Multiple features failed. System needs debugging.")
    
    return passed == len(results)

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        exit(1)
