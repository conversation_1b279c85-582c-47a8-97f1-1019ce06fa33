#!/usr/bin/env python3
"""
Frontend Issues Testing & Validation
Tests the fixes for critical frontend integration issues
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5173"

def test_backend_health():
    """Test backend health and basic connectivity"""
    print("🔍 Testing Backend Health...")
    
    try:
        # Test health endpoint
        response = requests.get(f"{BACKEND_URL}/api/courses/health/", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Backend Health: {health_data['status']}")
            print(f"✅ Total Enrollments: {health_data['statistics']['total_enrollments']}")
            print(f"✅ Retake Enrollments: {health_data['statistics']['retake_enrollments']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Backend health test failed: {e}")
        return False

def test_authentication_flow():
    """Test the complete authentication flow"""
    print("\n🔐 Testing Authentication Flow...")
    
    try:
        # Test student login
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{BACKEND_URL}/api/auth/login/", 
                               json=login_data, 
                               timeout=10)
        
        if response.status_code == 200:
            auth_data = response.json()
            print("✅ Student authentication successful")
            print(f"✅ Access token received: {auth_data['access'][:20]}...")
            
            # Test authenticated API call
            headers = {"Authorization": f"Bearer {auth_data['access']}"}
            profile_response = requests.get(f"{BACKEND_URL}/api/users/profile/",
                                          headers=headers,
                                          timeout=10)
            
            if profile_response.status_code == 200:
                profile_data = profile_response.json()
                print(f"✅ Profile access successful: {profile_data.get('user', {}).get('email', 'No email found')}")
                return True, auth_data['access']
            else:
                print(f"❌ Profile access failed: {profile_response.status_code}")
                print(f"Response: {profile_response.text}")
                return False, None
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False, None

def test_departments_endpoint():
    """Test the departments endpoint that was failing"""
    print("\n🏢 Testing Departments Endpoint...")
    
    try:
        # First authenticate
        auth_success, token = test_authentication_flow()
        if not auth_success:
            print("❌ Cannot test departments - authentication failed")
            return False
        
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BACKEND_URL}/api/courses/departments/", 
                              headers=headers, 
                              timeout=10)
        
        if response.status_code == 200:
            departments = response.json()
            print(f"✅ Departments endpoint working: {len(departments.get('results', []))} departments")
            return True
        else:
            print(f"❌ Departments endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Departments test failed: {e}")
        return False

def test_courses_endpoint():
    """Test the courses endpoint with authentication"""
    print("\n📚 Testing Courses Endpoint...")
    
    try:
        # First authenticate
        auth_success, token = test_authentication_flow()
        if not auth_success:
            print("❌ Cannot test courses - authentication failed")
            return False
        
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BACKEND_URL}/api/courses/", 
                              headers=headers, 
                              timeout=10)
        
        if response.status_code == 200:
            courses = response.json()
            print(f"✅ Courses endpoint working: {len(courses.get('results', []))} courses")
            
            # Test first course details
            if courses.get('results'):
                first_course = courses['results'][0]
                print(f"✅ Sample course: {first_course.get('code')} - {first_course.get('title')}")
                print(f"✅ Enrollment status: {first_course.get('user_enrollment_status')}")
            
            return True
        else:
            print(f"❌ Courses endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Courses test failed: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration"""
    print("\n🌐 Testing CORS Configuration...")
    
    try:
        # Test preflight request
        headers = {
            'Origin': 'http://localhost:5173',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'authorization,content-type'
        }
        
        response = requests.options(f"{BACKEND_URL}/api/auth/login/", 
                                  headers=headers, 
                                  timeout=10)
        
        if response.status_code == 200:
            print("✅ CORS preflight successful")
            
            # Check CORS headers
            cors_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
            }
            
            print(f"✅ CORS Headers: {cors_headers}")
            return True
        else:
            print(f"❌ CORS preflight failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ CORS test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 Frontend Issues Testing & Validation")
    print("=" * 50)
    
    results = []
    
    # Test 1: Backend Health
    results.append(("Backend Health", test_backend_health()))
    
    # Test 2: Authentication Flow
    auth_result, _ = test_authentication_flow()
    results.append(("Authentication Flow", auth_result))
    
    # Test 3: Departments Endpoint
    results.append(("Departments Endpoint", test_departments_endpoint()))
    
    # Test 4: Courses Endpoint
    results.append(("Courses Endpoint", test_courses_endpoint()))
    
    # Test 5: CORS Configuration
    results.append(("CORS Configuration", test_cors_configuration()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Frontend Issues Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All frontend integration issues have been resolved!")
        print("\n✅ Frontend should now work properly with:")
        print("   - Successful authentication")
        print("   - Working API endpoints")
        print("   - Proper CORS configuration")
        print("   - Fixed department serialization")
    elif passed >= len(results) * 0.8:
        print("⚠️  Most issues resolved. Some minor issues may remain.")
    else:
        print("❌ Multiple issues still need attention.")
    
    return passed == len(results)

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        exit(1)
