#!/usr/bin/env python3
"""
Test script to verify the enrollment system fixes are working correctly
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000/api"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing enrollment system health check...")
    
    try:
        response = requests.get(f"{BASE_URL}/courses/health/")
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check passed!")
            print(f"   Status: {data['status']}")
            print(f"   Total courses: {data['statistics']['total_courses']}")
            print(f"   Total enrollments: {data['statistics']['total_enrollments']}")
            print(f"   Retake enrollments: {data['statistics']['retake_enrollments']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_course_list():
    """Test course listing endpoint"""
    print("\n🔍 Testing course listing...")
    
    try:
        response = requests.get(f"{BASE_URL}/courses/")
        if response.status_code == 200:
            data = response.json()
            courses = data.get('results', [])
            print(f"✅ Course listing works! Found {len(courses)} courses")
            
            # Check if courses have the new enrollment status fields
            if courses:
                first_course = courses[0]
                has_new_fields = any(field in first_course for field in [
                    'user_enrollment_status', 
                    'user_can_enroll', 
                    'user_enrollment_message',
                    'user_enrollment_details'
                ])
                
                if has_new_fields:
                    print("✅ New enrollment status fields are present")
                else:
                    print("⚠️  New enrollment status fields not found (may need authentication)")
                
                return True
        else:
            print(f"❌ Course listing failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Course listing error: {e}")
        return False

def test_authentication():
    """Test authentication with existing user"""
    print("\n🔍 Testing authentication...")
    
    try:
        # Try to login with a known user
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('access')
            if token:
                print("✅ Authentication successful!")
                return token
            else:
                print("❌ No access token received")
                return None
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_authenticated_course_details(token):
    """Test course details with authentication"""
    print("\n🔍 Testing authenticated course details...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Get course list first
        response = requests.get(f"{BASE_URL}/courses/", headers=headers)
        if response.status_code != 200:
            print(f"❌ Failed to get course list: {response.status_code}")
            return False
        
        courses = response.json().get('results', [])
        if not courses:
            print("❌ No courses found")
            return False
        
        # Test first course details
        course_id = courses[0]['id']
        response = requests.get(f"{BASE_URL}/courses/{course_id}/", headers=headers)
        
        if response.status_code == 200:
            course = response.json()
            print("✅ Authenticated course details work!")
            
            # Check for our new enrollment fields
            enrollment_fields = [
                'user_enrollment_status',
                'user_can_enroll', 
                'user_enrollment_message',
                'user_enrollment_details',
                'user_current_attempt'
            ]
            
            found_fields = []
            for field in enrollment_fields:
                if field in course:
                    found_fields.append(field)
            
            print(f"   Found enrollment fields: {found_fields}")
            
            if 'user_enrollment_status' in course:
                status = course['user_enrollment_status']
                message = course.get('user_enrollment_message', 'N/A')
                can_enroll = course.get('user_can_enroll', False)
                
                print(f"   Enrollment Status: {status}")
                print(f"   Can Enroll: {can_enroll}")
                print(f"   Message: {message}")
                
                # Check if we have enrollment details
                if 'user_enrollment_details' in course:
                    details = course['user_enrollment_details']
                    if details:
                        print(f"   Has History: {details.get('has_enrollment_history', False)}")
                        print(f"   Total Attempts: {details.get('total_attempts', 0)}")
                        print(f"   Retake Eligible: {details.get('is_retake_eligible', False)}")
            
            return True
        else:
            print(f"❌ Course details failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Course details error: {e}")
        return False

def test_enrollment_analytics(token):
    """Test the new enrollment analytics endpoint"""
    print("\n🔍 Testing enrollment analytics...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Get a course ID first
        response = requests.get(f"{BASE_URL}/courses/", headers=headers)
        if response.status_code != 200:
            print("❌ Failed to get courses for analytics test")
            return False
        
        courses = response.json().get('results', [])
        if not courses:
            print("❌ No courses found for analytics test")
            return False
        
        course_id = courses[0]['id']
        
        # Test analytics endpoint
        response = requests.get(f"{BASE_URL}/courses/{course_id}/analytics/", headers=headers)
        
        if response.status_code == 200:
            analytics = response.json()
            print("✅ Enrollment analytics work!")
            
            stats = analytics.get('enrollment_statistics', {})
            retake_stats = analytics.get('retake_statistics', {})
            
            print(f"   Total Enrollments: {stats.get('total_enrollments', 0)}")
            print(f"   Unique Students: {stats.get('unique_students', 0)}")
            print(f"   Students with Retakes: {retake_stats.get('students_with_retakes', 0)}")
            print(f"   Success Rate: {analytics.get('success_rate', 0):.1f}%")
            
            return True
        else:
            print(f"❌ Analytics failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Analytics error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing UMLS Enrollment System Fixes")
    print("=" * 50)
    
    results = []
    
    # Test 1: Health Check
    results.append(test_health_check())
    
    # Test 2: Course List
    results.append(test_course_list())
    
    # Test 3: Authentication
    token = test_authentication()
    if token:
        results.append(True)
        
        # Test 4: Authenticated Course Details
        results.append(test_authenticated_course_details(token))
        
        # Test 5: Enrollment Analytics
        results.append(test_enrollment_analytics(token))
    else:
        results.extend([False, False, False])
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    test_names = [
        "Health Check",
        "Course Listing", 
        "Authentication",
        "Course Details (Authenticated)",
        "Enrollment Analytics"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Enrollment system is working correctly.")
    elif passed >= 3:
        print("⚠️  Most tests passed. Some advanced features may need attention.")
    else:
        print("❌ Multiple tests failed. System needs debugging.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
