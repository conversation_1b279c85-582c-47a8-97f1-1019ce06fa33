import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../store';
import { addNotification } from '../store/slices/uiSlice';
import { BookOpen, Users, Calendar, Clock, Search, Filter, Plus, Edit, Eye, MapPin, Download } from 'lucide-react';
import CourseModal from '../components/courses/CourseModal';
import CourseDetailModal from '../components/courses/CourseDetailModal';
import ExportModal from '../components/common/ExportModal';
import { useApiWithAuth } from '../hooks/useApiWithAuth';

interface Course {
  id: string;
  title: string;
  title_ar?: string;
  code: string;
  description: string;
  description_ar?: string;
  instructor: string;
  instructor_name?: string;
  department: string;
  department_name?: string;
  credit_hours: number;
  semester: string;
  year: number;
  max_students: number;
  enrolled_students_count: number;
  is_active: boolean;
  start_date: string;
  end_date: string;
  prerequisites?: string[];
  prerequisites_names?: string[];
  // User-specific enrollment fields
  user_enrollment_status?: 'enrolled' | 'available' | 'completed' | 'waitlisted' | 'retakeable' | 'retake_limit_exceeded' | 'not_available';
  user_can_enroll?: boolean;
  user_enrollment_message?: string;
  user_enrollment_history?: any[];
  user_current_attempt?: number;
  user_enrollment_details?: any;
}

// Helper functions for enrollment status
const getEnrollmentStatusColor = (status: string) => {
  switch (status) {
    case 'enrolled':
      return 'bg-green-100 text-green-800';
    case 'waitlisted':
      return 'bg-yellow-100 text-yellow-800';
    case 'completed':
      return 'bg-blue-100 text-blue-800';
    case 'available':
      return 'bg-gray-100 text-gray-800';
    case 'retakeable':
      return 'bg-orange-100 text-orange-800';
    case 'retake_limit_exceeded':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getEnrollmentStatusText = (status: string) => {
  switch (status) {
    case 'enrolled':
      return 'Enrolled';
    case 'waitlisted':
      return 'Waitlisted';
    case 'completed':
      return 'Completed';
    case 'available':
      return 'Available';
    case 'retakeable':
      return 'Retakeable';
    case 'retake_limit_exceeded':
      return 'Retake Limit Exceeded';
    default:
      return 'Unknown';
  }
};

const CoursesPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const apiService = useApiWithAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDepartment, setFilterDepartment] = useState('all');
  const [activeTab, setActiveTab] = useState<'all' | 'enrolled' | 'available' | 'completed'>('all');
  const [showCourseModal, setShowCourseModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showExportModal, setShowExportModal] = useState(false);
  const [enrollmentLoading, setEnrollmentLoading] = useState<string | null>(null);

  // Role-based permissions
  const canManageCourses = user?.role === 'admin' || user?.role === 'superadmin';
  const isTeacher = user?.role === 'teacher';

  // Fetch courses from API
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch real courses data from API
        const response = await apiService.getCourses();
        setCourses(response.results || response || []);
      } catch (error) {
        console.error('Error fetching courses:', error);
        setError('Failed to load courses. Please check your connection and try again.');
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [apiService]);

  // Handle navigation from departments
  useEffect(() => {
    if (location.state?.filterDepartment) {
      setFilterDepartment(location.state.filterDepartment);
      setSearchTerm(''); // Clear search when filtering by department
    }
  }, [location.state]);

  // Handle course enrollment
  const handleEnrollment = async (courseId: string, action: 'enroll' | 'unenroll') => {
    try {
      setEnrollmentLoading(courseId);

      if (action === 'enroll') {
        await apiService.enrollInCourse(courseId);
        dispatch(addNotification({
          type: 'success',
          message: t('courses.enrollmentSuccess', 'Successfully enrolled in course')
        }));
      } else {
        // Find the enrollment to withdraw from
        const course = courses.find(c => c.id === courseId);
        if (course?.user_enrollment_history?.length) {
          const activeEnrollment = course.user_enrollment_history.find((e: any) => e.is_active);
          if (activeEnrollment) {
            await apiService.withdrawFromCourse(activeEnrollment.id);
            dispatch(addNotification({
              type: 'success',
              message: t('courses.unenrollmentSuccess', 'Successfully withdrawn from course')
            }));
          }
        }
      }

      // Refresh courses data
      const response = await apiService.getCourses();
      setCourses(response.results || response || []);

    } catch (error) {
      console.error('Enrollment error:', error);
      dispatch(addNotification({
        type: 'error',
        message: t('courses.enrollmentError', 'Failed to update enrollment')
      }));
    } finally {
      setEnrollmentLoading(null);
    }
  };

  // Filter courses based on active tab and other filters
  const filteredCourses = courses.filter(course => {
    const courseName = course.title || '';
    const courseCode = course.code || '';
    const instructorName = course.instructor_name || course.instructor || '';
    const courseDescription = course.description || '';
    const courseDepartment = course.department_name || course.department || '';
    const courseStatus = course.is_active ? 'active' : 'inactive';

    const matchesSearch =
      courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      courseCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      instructorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      courseDescription.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === 'all' || courseStatus === filterStatus;
    const matchesDepartment = filterDepartment === 'all' || courseDepartment === filterDepartment;

    // Filter by enrollment status for students
    let matchesTab = true;
    if (user?.role === 'student') {
      switch (activeTab) {
        case 'enrolled':
          matchesTab = course.user_enrollment_status === 'enrolled' || course.user_enrollment_status === 'waitlisted';
          break;
        case 'available':
          matchesTab = course.user_enrollment_status === 'available' || course.user_enrollment_status === 'retakeable';
          break;
        case 'completed':
          matchesTab = course.user_enrollment_status === 'completed';
          break;
        case 'all':
        default:
          matchesTab = true;
          break;
      }
    }

    return matchesSearch && matchesFilter && matchesDepartment && matchesTab;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };



  const handleAddCourse = () => {
    setEditingCourse(null);
    setShowCourseModal(true);
  };

  const handleEditCourse = (course: Course) => {
    setEditingCourse(course);
    setShowCourseModal(true);
  };

  const handleSaveCourse = async (courseData: Course) => {
    try {
      if (editingCourse) {
        // Update existing course via API
        const updatedCourse = await apiService.updateCourse(editingCourse.id, courseData);
        setCourses(prev => prev.map(c => c.id === editingCourse.id ? updatedCourse : c));
        dispatch(addNotification({
          type: 'success',
          title: t('courses.updateSuccess', 'Course Updated'),
          message: t('courses.courseUpdated', 'Course has been updated successfully')
        }));
      } else {
        // Add new course via API
        const newCourse = await apiService.createCourse(courseData);
        setCourses(prev => [...prev, newCourse]);
        dispatch(addNotification({
          type: 'success',
          title: t('courses.createSuccess', 'Course Created'),
          message: t('courses.courseCreated', 'Course has been created successfully')
        }));
      }
      setShowCourseModal(false);
      setEditingCourse(null);
    } catch (error) {
      console.error('Error saving course:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('courses.saveError', 'Save Failed'),
        message: t('courses.saveErrorMessage', 'Failed to save course data')
      }));
    }
  };

  const handleViewCourse = (course: Course) => {
    navigate(`/app/courses/${course.id}`);
  };

  const handleCloseModal = () => {
    setShowCourseModal(false);
    setEditingCourse(null);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedCourse(null);
  };

  const handleEnterCourse = (course: Course) => {
    dispatch(addNotification({
      type: 'success',
      title: t('courses.enterCourse', 'Enter Course'),
      message: t('courses.enterCourseMessage', `Entering ${course.name} course interface`)
    }));
    // Navigate to course interface
    navigate(`/app/course/${course.id}`);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('navigation.courses')}
            </h1>
            <p className="text-gray-600 mt-2">
              {user?.role === 'student' 
                ? t('courses.studentDescription', 'View your enrolled courses and progress')
                : user?.role === 'teacher'
                ? t('courses.teacherDescription', 'Manage your courses and students')
                : t('courses.adminDescription', 'Manage all university courses')
              }
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowExportModal(true)}
              className="btn-secondary flex items-center space-x-2"
              title={t('courses.exportAll', 'Export all courses')}
            >
              <Download className="w-4 h-4" />
              <span>{t('courses.export', 'Export')}</span>
            </button>
            {(canManageCourses || isTeacher) && (
              <button
                onClick={handleAddCourse}
                className="btn-primary flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>{t('courses.addCourse', 'Add Course')}</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card">
        {location.state?.departmentName && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>{t('courses.filteringBy', 'Filtering by department')}:</strong> {location.state.departmentName}
              <button
                onClick={() => {
                  setFilterDepartment('all');
                  window.history.replaceState({}, document.title);
                }}
                className="ml-2 text-blue-600 hover:text-blue-800 underline text-xs"
              >
                {t('common.clearFilter', 'Clear filter')}
              </button>
            </p>
          </div>
        )}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={t('courses.searchPlaceholder', 'Search courses, codes, or instructors...')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="form-input"
            >
              <option value="all">{t('common.all', 'All')}</option>
              <option value="active">{t('courses.active', 'Active')}</option>
              <option value="inactive">{t('courses.inactive', 'Inactive')}</option>
              <option value="completed">{t('courses.completed', 'Completed')}</option>
            </select>
          </div>
          <div>
            <select
              value={filterDepartment}
              onChange={(e) => setFilterDepartment(e.target.value)}
              className="form-input"
            >
              <option value="all">{t('courses.allDepartments', 'All Departments')}</option>
              <option value="Computer Science">{t('departments.computerScience', 'Computer Science')}</option>
              <option value="Mathematics">{t('departments.mathematics', 'Mathematics')}</option>
              <option value="Physics">{t('departments.physics', 'Physics')}</option>
              <option value="Chemistry">{t('departments.chemistry', 'Chemistry')}</option>
              <option value="Business Administration">{t('departments.businessAdmin', 'Business Administration')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Enrollment Status Tabs (for students only) */}
      {user?.role === 'student' && (
        <div className="glass-card">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {[
              { key: 'all', label: t('courses.allCourses', 'All Courses'), count: courses.length },
              { key: 'enrolled', label: t('courses.enrolled', 'Enrolled'), count: courses.filter(c => c.user_enrollment_status === 'enrolled' || c.user_enrollment_status === 'waitlisted').length },
              { key: 'available', label: t('courses.available', 'Available'), count: courses.filter(c => c.user_enrollment_status === 'available' || c.user_enrollment_status === 'retakeable').length },
              { key: 'completed', label: t('courses.completed', 'Completed'), count: courses.filter(c => c.user_enrollment_status === 'completed').length }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab.key
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="glass-card text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading', 'Loading courses...')}</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="glass-card text-center py-8">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary"
          >
            {t('common.retry', 'Retry')}
          </button>
        </div>
      )}

      {/* Courses Grid */}
      {!loading && !error && (
        <>
          {filteredCourses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCourses.map((course) => (
                <div key={course.id} className="glass-card hover:scale-105 transition-transform duration-200 cursor-pointer">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                        <BookOpen className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{course.code}</h3>
                        <div className="flex items-center space-x-2">
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(course.is_active ? 'active' : 'inactive')}`}>
                            {course.is_active ? t('courses.active', 'Active') : t('courses.inactive', 'Inactive')}
                          </span>
                          {user?.role === 'student' && course.user_enrollment_status && (
                            <span className={`text-xs px-2 py-1 rounded-full ${getEnrollmentStatusColor(course.user_enrollment_status)}`}>
                              {getEnrollmentStatusText(course.user_enrollment_status)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    {user?.preferredLanguage === 'ar' && course.title_ar ? course.title_ar : (course.title || course.name)}
                  </h4>

                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {course.description || 'No description available'}
                  </p>

                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>{course.instructor_name || course.instructor || 'TBA'}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>{course.enrolled_students_count || 0}/{course.max_students || 0} {t('courses.students', 'students')}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>{course.semester || 'TBA'} {course.year || ''}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>{course.location || 'TBA'}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4" />
                      <span>{course.credit_hours || 0} {t('courses.credits', 'credits')}</span>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">
                        {course.department_name || course.department || 'General'}
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleViewCourse(course)}
                          className="btn-secondary text-sm py-1 px-3 flex items-center space-x-1"
                        >
                          <Eye className="w-3 h-3" />
                          <span>{t('common.view', 'View')}</span>
                        </button>

                        {user?.role === 'student' && course.is_active && (
                          <>
                            {course.user_enrollment_status === 'enrolled' || course.user_enrollment_status === 'waitlisted' ? (
                              <>
                                <button
                                  onClick={() => handleEnterCourse(course)}
                                  className="btn-primary text-sm py-1 px-3"
                                >
                                  {t('courses.enter', 'Enter')}
                                </button>
                                <button
                                  onClick={() => handleEnrollment(course.id, 'unenroll')}
                                  disabled={enrollmentLoading === course.id}
                                  className="btn-secondary text-sm py-1 px-3 text-red-600 hover:bg-red-50"
                                >
                                  {enrollmentLoading === course.id ? '...' : t('courses.withdraw', 'Withdraw')}
                                </button>
                              </>
                            ) : (course.user_enrollment_status === 'available' || course.user_enrollment_status === 'retakeable') ? (
                              <button
                                onClick={() => handleEnrollment(course.id, 'enroll')}
                                disabled={enrollmentLoading === course.id}
                                className="btn-primary text-sm py-1 px-3"
                              >
                                {enrollmentLoading === course.id ? '...' : t('courses.enroll', 'Enroll')}
                              </button>
                            ) : course.user_enrollment_status === 'completed' ? (
                              <button
                                onClick={() => handleEnterCourse(course)}
                                className="btn-secondary text-sm py-1 px-3"
                              >
                                {t('courses.review', 'Review')}
                              </button>
                            ) : null}
                          </>
                        )}

                        {(canManageCourses || isTeacher) && (
                          <button
                            onClick={() => handleEditCourse(course)}
                            className="btn-primary text-sm py-1 px-3 flex items-center space-x-1"
                          >
                            <Edit className="w-3 h-3" />
                            <span>{t('common.edit', 'Edit')}</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="glass-card text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('courses.noCourses', 'No courses found')}
              </h3>
              <p className="text-gray-600">
                {searchTerm
                  ? t('courses.noSearchResults', 'Try adjusting your search terms')
                  : t('courses.noCoursesDescription', 'No courses available at the moment')
                }
              </p>
            </div>
          )}
        </>
      )}

      {/* Course Modal */}
      <CourseModal
        isOpen={showCourseModal}
        onClose={handleCloseModal}
        course={editingCourse}
        onSave={handleSaveCourse}
      />

      {/* Course Detail Modal */}
      <CourseDetailModal
        isOpen={showDetailModal}
        onClose={handleCloseDetailModal}
        course={selectedCourse}
        onEdit={handleEditCourse}
      />

      {/* Export Modal */}
      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        dataType="courses"
        filters={{
          status: filterStatus !== 'all' ? filterStatus : undefined,
          department: filterDepartment !== 'all' ? filterDepartment : undefined,
          search: searchTerm || undefined
        }}
        title={t('courses.exportCourses', 'Export Courses')}
      />
    </div>
  );
};

export default CoursesPage;
