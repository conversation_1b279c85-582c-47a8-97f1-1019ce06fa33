import React from 'react';

interface Course {
  id: string;
  code: string;
  name: string;
  nameAr?: string;
  description: string;
  instructor: string;
  instructorEmail: string;
  credits: number;
  maxStudents: number;
  enrolledStudents: number;
  waitlistCount: number;
  schedule: {
    days: string[];
    startTime: string;
    endTime: string;
    room: string;
    building: string;
  };
  prerequisites: string[];
  semester: string;
  department: string;
  status: 'open' | 'closed' | 'waitlist' | 'cancelled';
  enrollmentStatus?: 'enrolled' | 'waitlisted' | 'not_enrolled' | 'dropped';
  rating?: number;
  difficulty?: number;
}

const EnrollmentPage: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Course Enrollment - SIMPLE TEST</h1>
      <p>This is a simple test to see if the component loads.</p>
    </div>
  );
};
    const fetchCourses = async () => {
      try {
        console.log('🔄 Starting to fetch courses...');
        console.log('Token available:', !!token);
        setLoading(true);

        if (token) {
          apiService.setToken(token);
          console.log('📡 Making API call to getCourses...');
          const response = await apiService.getCourses();
          console.log('✅ API response received:', response);

          // Transform courses to include user-specific enrollment information
          const transformedCourses = (response.results || response || []).map((course: any) => ({
            ...course,
            id: course.id.toString(),
            name: course.title,
            nameAr: course.title_ar,
            department: course.department_name,
            instructor: course.instructor_name,
            enrolledStudents: course.enrolled_students_count,
            maxStudents: course.max_students,
            // User-specific enrollment data from backend
            enrollmentStatus: course.user_enrollment_status || 'not_enrolled',
            canEnroll: course.user_can_enroll,
            enrollmentMessage: course.user_enrollment_message,
            enrollmentHistory: course.user_enrollment_history || [],
            currentAttempt: course.user_current_attempt || 1,
          }));

          console.log('🔄 Transformed courses:', transformedCourses.length, 'courses');
          setCourses(transformedCourses);
        } else {
          console.log('❌ No token available');
        }
      } catch (error) {
        console.error('❌ Error fetching courses:', error);
        dispatch(addNotification({
          type: 'error',
          message: t('courses.fetchError', 'Failed to load courses')
        }));
        setCourses([]);
      } finally {
        console.log('✅ Setting loading to false');
        setLoading(false);
      }
    };

    fetchCourses();
  }, [token, dispatch, t]);

  const filteredCourses = courses.filter(course => {
    const matchesSearch = 
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (course.nameAr && course.nameAr.includes(searchTerm));

    const matchesDepartment = departmentFilter === 'all' || course.department === departmentFilter;
    const matchesStatus = statusFilter === 'all' || course.enrollmentStatus === statusFilter;

    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const handleEnroll = async (courseId: string) => {
    try {
      setEnrolling(courseId);

      if (token) {
        apiService.setToken(token);
        const response = await apiService.enrollInCourse(courseId);

        // Refresh courses data from backend to get updated enrollment status
        const coursesResponse = await apiService.getCourses();
        const transformedCourses = (coursesResponse.results || coursesResponse || []).map((course: any) => ({
          ...course,
          id: course.id.toString(),
          name: course.title,
          nameAr: course.title_ar,
          department: course.department_name,
          instructor: course.instructor_name,
          enrolledStudents: course.enrolled_students_count,
          maxStudents: course.max_students,
          // User-specific enrollment data from backend
          enrollmentStatus: course.user_enrollment_status || 'not_enrolled',
          canEnroll: course.user_can_enroll,
          enrollmentMessage: course.user_enrollment_message,
          enrollmentHistory: course.user_enrollment_history || [],
          currentAttempt: course.user_current_attempt || 1,
        }));

        setCourses(transformedCourses);

        dispatch(addNotification({
          type: 'success',
          title: response.type === 'waitlist'
            ? t('enrollment.waitlistSuccess', 'Added to Waitlist')
            : t('enrollment.success', 'Enrollment Successful'),
          message: response.message || t('enrollment.successMessage', 'You have been successfully enrolled in the course')
        }));
      } else {
        // Fallback to mock behavior
        await new Promise(resolve => setTimeout(resolve, 1500));

        setCourses(prev => prev.map(course =>
          course.id === courseId
            ? {
                ...course,
                enrollmentStatus: course.status === 'waitlist' ? 'waitlisted' : 'enrolled',
                enrolledStudents: course.status === 'open' ? course.enrolledStudents + 1 : course.enrolledStudents,
                waitlistCount: course.status === 'waitlist' ? course.waitlistCount + 1 : course.waitlistCount
              }
            : course
        ));

        dispatch(addNotification({
          type: 'success',
          title: t('enrollment.success', 'Enrollment Successful'),
          message: t('enrollment.successMessage', 'You have been successfully enrolled in the course')
        }));
      }

    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'Failed to enroll in the course';

      dispatch(addNotification({
        type: 'error',
        title: t('enrollment.error', 'Enrollment Failed'),
        message: errorMessage
      }));
    } finally {
      setEnrolling(null);
    }
  };

  const handleUnenroll = async (courseId: string) => {
    try {
      setEnrolling(courseId);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update local state
      setCourses(prev => prev.map(course => 
        course.id === courseId 
          ? { 
              ...course, 
              enrollmentStatus: 'not_enrolled',
              enrolledStudents: Math.max(0, course.enrolledStudents - 1)
            }
          : course
      ));

      dispatch(addNotification({
        type: 'success',
        title: t('enrollment.unenrollSuccess', 'Unenrollment Successful'),
        message: t('enrollment.unenrollSuccessMessage', 'You have been successfully unenrolled from the course')
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: t('enrollment.unenrollError', 'Unenrollment Failed'),
        message: t('enrollment.unenrollErrorMessage', 'Failed to unenroll from course. Please try again.')
      }));
    } finally {
      setEnrolling(null);
    }
  };

  const handleViewCourse = (course: Course) => {
    setSelectedCourse(course);
    setShowCourseDetail(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800';
      case 'waitlist': return 'bg-yellow-100 text-yellow-800';
      case 'closed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEnrollmentStatusColor = (status: string) => {
    switch (status) {
      case 'enrolled': return 'bg-green-100 text-green-800';
      case 'waitlisted': return 'bg-yellow-100 text-yellow-800';
      case 'not_enrolled': return 'bg-gray-100 text-gray-800';
      case 'dropped': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'waitlist': return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'closed': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'cancelled': return <XCircle className="w-4 h-4 text-gray-600" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  // Temporary: Always show content to test if component loads
  // if (loading) {
  //   return (
  //     <div className="flex items-center justify-center h-64">
  //       <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
  //     </div>
  //   );
  // }

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Course Enrollment - DEBUG MODE</h1>
        <p>Loading: {loading.toString()}</p>
        <p>Token available: {!!token ? 'Yes' : 'No'}</p>
        <p>Courses count: {courses.length}</p>
        <p>User: {user?.email || 'Not logged in'}</p>
        <div className="mt-4">
          <button
            onClick={() => setLoading(false)}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Force Stop Loading
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('enrollment.title', 'Course Enrollment')}
            </h1>
            <p className="text-gray-600 mt-2">
              {t('enrollment.description', 'Browse and enroll in available courses for the upcoming semester')}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">
              {t('enrollment.semester', 'Semester')}: <span className="font-medium">Spring 2025</span>
            </p>
            <p className="text-sm text-gray-600">
              {t('enrollment.enrollmentPeriod', 'Enrollment Period')}: Dec 15 - Jan 15
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={t('enrollment.searchPlaceholder', 'Search courses...')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10"
            />
          </div>
          <div>
            <select
              value={departmentFilter}
              onChange={(e) => setDepartmentFilter(e.target.value)}
              className="form-input"
            >
              <option value="all">{t('enrollment.allDepartments', 'All Departments')}</option>
              <option value="Computer Science">{t('departments.computerScience', 'Computer Science')}</option>
              <option value="Mathematics">{t('departments.mathematics', 'Mathematics')}</option>
              <option value="Physics">{t('departments.physics', 'Physics')}</option>
              <option value="Chemistry">{t('departments.chemistry', 'Chemistry')}</option>
            </select>
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-input"
            >
              <option value="all">{t('enrollment.allStatuses', 'All Statuses')}</option>
              <option value="enrolled">{t('enrollment.enrolled', 'Enrolled')}</option>
              <option value="not_enrolled">{t('enrollment.notEnrolled', 'Not Enrolled')}</option>
              <option value="waitlisted">{t('enrollment.waitlisted', 'Waitlisted')}</option>
            </select>
          </div>
          <div className="flex items-end">
            <button className="w-full btn-secondary flex items-center justify-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>{t('common.filter', 'Filter')}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Enrollment Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('enrollment.totalCourses', 'Total Courses')}</p>
              <p className="text-2xl font-bold text-blue-600">{courses.length}</p>
            </div>
            <BookOpen className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="glass-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('enrollment.enrolled', 'Enrolled')}</p>
              <p className="text-2xl font-bold text-green-600">
                {courses.filter(c => c.enrollmentStatus === 'enrolled').length}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="glass-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('enrollment.waitlisted', 'Waitlisted')}</p>
              <p className="text-2xl font-bold text-yellow-600">
                {courses.filter(c => c.enrollmentStatus === 'waitlisted').length}
              </p>
            </div>
            <AlertCircle className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        <div className="glass-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{t('enrollment.totalCredits', 'Total Credits')}</p>
              <p className="text-2xl font-bold text-purple-600">
                {courses
                  .filter(c => c.enrollmentStatus === 'enrolled')
                  .reduce((sum, c) => sum + c.credits, 0)
                }
              </p>
            </div>
            <Star className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Courses List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            {t('enrollment.availableCourses', 'Available Courses')}
          </h2>
          <p className="text-sm text-gray-600">
            {t('enrollment.showing', 'Showing')} {filteredCourses.length} {t('enrollment.of', 'of')} {courses.length} {t('enrollment.courses', 'courses')}
          </p>
        </div>

        <div className="space-y-4">
          {filteredCourses.map((course) => (
            <div key={course.id} className="glass-card hover:scale-[1.01] transition-transform duration-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {user?.preferredLanguage === 'ar' && course.nameAr ? course.nameAr : course.name}
                        </h3>
                        <span className="text-sm font-medium text-gray-600">{course.code}</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(course.status)}`}>
                          {getStatusIcon(course.status)}
                          <span className="ml-1">{t(`enrollment.${course.status}`, course.status)}</span>
                        </span>
                        {course.enrollmentStatus && (
                          <span className={`text-xs px-2 py-1 rounded-full ${getEnrollmentStatusColor(course.enrollmentStatus)}`}>
                            {t(`enrollment.${course.enrollmentStatus}`, course.enrollmentStatus)}
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 mb-3">{course.description}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>{course.instructor}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>{course.schedule.days.join(', ')}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4" />
                      <span>{course.schedule.startTime} - {course.schedule.endTime}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>{course.schedule.room}, {course.schedule.building}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{t('enrollment.credits', 'Credits')}: {course.credits}</span>
                      <span>{t('enrollment.enrolled', 'Enrolled')}: {course.enrolledStudents}/{course.maxStudents}</span>
                      {course.waitlistCount > 0 && (
                        <span>{t('enrollment.waitlist', 'Waitlist')}: {course.waitlistCount}</span>
                      )}
                      {course.rating && (
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <span>{course.rating.toFixed(1)}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewCourse(course)}
                        className="btn-secondary text-sm flex items-center space-x-1"
                      >
                        <Eye className="w-4 h-4" />
                        <span>{t('common.view', 'View')}</span>
                      </button>

                      {course.enrollmentStatus === 'enrolled' ? (
                        <button
                          onClick={() => handleUnenroll(course.id)}
                          disabled={enrolling === course.id}
                          className="btn-secondary text-sm flex items-center space-x-1"
                        >
                          {enrolling === course.id ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                          ) : (
                            <Minus className="w-4 h-4" />
                          )}
                          <span>
                            {enrolling === course.id 
                              ? t('enrollment.processing', 'Processing...') 
                              : t('enrollment.unenroll', 'Unenroll')
                            }
                          </span>
                        </button>
                      ) : course.enrollmentStatus === 'waitlisted' ? (
                        <button
                          onClick={() => handleUnenroll(course.id)}
                          disabled={enrolling === course.id}
                          className="btn-secondary text-sm flex items-center space-x-1"
                        >
                          {enrolling === course.id ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                          ) : (
                            <Minus className="w-4 h-4" />
                          )}
                          <span>
                            {enrolling === course.id 
                              ? t('enrollment.processing', 'Processing...') 
                              : t('enrollment.leaveWaitlist', 'Leave Waitlist')
                            }
                          </span>
                        </button>
                      ) : course.status !== 'closed' && course.status !== 'cancelled' ? (
                        <button
                          onClick={() => handleEnroll(course.id)}
                          disabled={enrolling === course.id}
                          className={`text-sm flex items-center space-x-1 ${
                            course.status === 'waitlist'
                              ? 'btn-secondary'
                              : 'btn-primary'
                          }`}
                        >
                          {enrolling === course.id ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          ) : (
                            <Plus className="w-4 h-4" />
                          )}
                          <span>
                            {enrolling === course.id 
                              ? t('enrollment.processing', 'Processing...') 
                              : course.status === 'waitlist' 
                                ? t('enrollment.joinWaitlist', 'Join Waitlist')
                                : t('enrollment.enroll', 'Enroll')
                            }
                          </span>
                        </button>
                      ) : (
                        <span className="text-sm text-gray-500 px-3 py-2">
                          {t('enrollment.notAvailable', 'Not Available')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {filteredCourses.length === 0 && (
        <div className="glass-card text-center py-12">
          <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('enrollment.noCourses', 'No courses found')}
          </h3>
          <p className="text-gray-600">
            {searchTerm 
              ? t('enrollment.noSearchResults', 'Try adjusting your search terms')
              : t('enrollment.noCoursesDescription', 'No courses match your current filters')
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default EnrollmentPage;
