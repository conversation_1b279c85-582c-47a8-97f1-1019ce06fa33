import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store';
import { Home, ArrowLeft, BookOpen, User, BarChart3, MessageSquare, Calendar } from 'lucide-react';

const NotFoundPage: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);

  // Suggest common routes based on authentication status
  const getSuggestedRoutes = () => {
    if (!isAuthenticated) {
      return [
        { path: '/', label: t('navigation.home', 'Home'), icon: Home },
        { path: '/login', label: t('navigation.login', 'Login'), icon: User },
      ];
    }

    // Authenticated user suggestions
    const baseRoutes = [
      { path: '/app/dashboard', label: t('navigation.dashboard', 'Dashboard'), icon: Home },
      { path: '/app/courses', label: t('navigation.courses', 'Courses'), icon: BookOpen },
      { path: '/app/profile', label: t('navigation.profile', 'Profile'), icon: User },
    ];

    // Add role-specific suggestions
    if (user?.role === 'student') {
      baseRoutes.push(
        { path: '/app/assessments', label: t('navigation.assessments', 'Assessments'), icon: BarChart3 },
        { path: '/app/grades', label: t('navigation.grades', 'Grades'), icon: BarChart3 }
      );
    } else if (user?.role === 'teacher') {
      baseRoutes.push(
        { path: '/app/assessments', label: t('navigation.assessments', 'Assessments'), icon: BarChart3 },
        { path: '/app/grades', label: t('navigation.grades', 'Grade Management'), icon: BarChart3 }
      );
    }

    baseRoutes.push(
      { path: '/app/messaging', label: t('navigation.messaging', 'Messaging'), icon: MessageSquare }
    );

    return baseRoutes;
  };

  const suggestedRoutes = getSuggestedRoutes();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="text-center">
        <div className="glass-card max-w-lg mx-auto">
          {/* 404 Illustration */}
          <div className="mb-8">
            <div className="text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-600">
              404
            </div>
            <div className="mt-4">
              <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Home className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {t('errors.pageNotFound', 'Page Not Found')}
            </h1>

            <p className="text-gray-600">
              {t('errors.pageNotFoundMessage', 'The page you are looking for does not exist or has been moved.')}
            </p>

            {/* Show the attempted URL */}
            <div className="bg-gray-100 rounded-lg p-3 text-sm text-gray-700">
              <strong>{t('errors.attemptedUrl', 'Attempted URL')}:</strong> {location.pathname}
            </div>

            {/* Helpful note about route structure */}
            {isAuthenticated && !location.pathname.startsWith('/app/') && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-700">
                <strong>{t('errors.routeHint', 'Hint')}:</strong> {t('errors.protectedRoutesMessage', 'Protected routes start with /app/ (e.g., /app/courses instead of /courses)')}
              </div>
            )}

            {/* Suggested Routes */}
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('errors.suggestedPages', 'Try these pages instead:')}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {suggestedRoutes.map((route) => (
                  <Link
                    key={route.path}
                    to={route.path}
                    className="flex items-center space-x-2 p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <route.icon className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-900">{route.label}</span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center mt-6">
              <Link
                to={isAuthenticated ? "/app/dashboard" : "/"}
                className="btn-primary flex items-center justify-center space-x-2"
              >
                <Home className="w-4 h-4" />
                <span>{isAuthenticated ? t('navigation.dashboard') : t('navigation.home')}</span>
              </Link>

              <button
                onClick={() => window.history.back()}
                className="btn-secondary flex items-center justify-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>{t('common.back', 'Go Back')}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Additional Help */}
        <div className="mt-8 text-sm text-gray-600">
          <p>
            {t('errors.needHelp', 'Need help?')}{' '}
            <Link to="/contact" className="text-blue-600 hover:text-blue-800 underline">
              {t('errors.contactSupport', 'Contact Support')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
