import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../store';
import { addNotification } from '../store/slices/uiSlice';
import { apiService } from '../services/api';

interface Course {
  id: string;
  code: string;
  title: string;
  instructor_name: string;
  department_name: string;
  enrolled_students_count: number;
  max_students: number;
  user_enrollment_status?: string;
  user_can_enroll?: boolean;
  user_enrollment_message?: string;
}

const EnrollmentPage: React.FC = () => {
  const { t } = useTranslation();
  const { user, token } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState<string | null>(null);

  // Fetch courses from API
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        console.log('🔄 Starting to fetch courses...');
        console.log('Token available:', !!token);
        setLoading(true);
        
        if (token) {
          apiService.setToken(token);
          console.log('📡 Making API call to getCourses...');
          const response = await apiService.getCourses();
          console.log('✅ API response received:', response);
          
          const coursesData = response.results || response || [];
          console.log('🔄 Courses data:', coursesData.length, 'courses');
          setCourses(coursesData);
        } else {
          console.log('❌ No token available');
        }
      } catch (error) {
        console.error('❌ Error fetching courses:', error);
        dispatch(addNotification({
          type: 'error',
          message: t('courses.fetchError', 'Failed to load courses')
        }));
        setCourses([]);
      } finally {
        console.log('✅ Setting loading to false');
        setLoading(false);
      }
    };

    fetchCourses();
  }, [token, dispatch, t]);

  // Handle enrollment
  const handleEnroll = async (courseId: string) => {
    try {
      setEnrolling(courseId);
      
      if (token) {
        apiService.setToken(token);
        const response = await apiService.enrollInCourse(courseId);
        
        // Refresh courses data
        const coursesResponse = await apiService.getCourses();
        const coursesData = coursesResponse.results || coursesResponse || [];
        setCourses(coursesData);

        dispatch(addNotification({
          type: 'success',
          message: response.message || 'Successfully enrolled in course'
        }));
      }
    } catch (error: any) {
      console.error('Enrollment error:', error);
      dispatch(addNotification({
        type: 'error',
        message: error.response?.data?.error || 'Failed to enroll in course'
      }));
    } finally {
      setEnrolling(null);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Course Enrollment - DEBUG MODE</h1>
        <p>Loading: {loading.toString()}</p>
        <p>Token available: {!!token ? 'Yes' : 'No'}</p>
        <p>Courses count: {courses.length}</p>
        <p>User: {user?.email || 'Not logged in'}</p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mt-4"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Course Enrollment</h1>
      
      <div className="mb-4">
        <p className="text-gray-600">
          Found {courses.length} courses available for enrollment
        </p>
      </div>

      {courses.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No courses found</p>
        </div>
      ) : (
        <div className="grid gap-4">
          {courses.map((course) => (
            <div key={course.id} className="border rounded-lg p-4 bg-white shadow-sm">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {course.code} - {course.title}
                  </h3>
                  <p className="text-gray-600 mt-1">
                    Instructor: {course.instructor_name}
                  </p>
                  <p className="text-gray-600">
                    Department: {course.department_name}
                  </p>
                  <p className="text-gray-600">
                    Enrolled: {course.enrolled_students_count}/{course.max_students}
                  </p>
                  
                  {course.user_enrollment_status && (
                    <div className="mt-2">
                      <span className={`inline-block px-2 py-1 rounded text-sm ${
                        course.user_enrollment_status === 'enrolled' 
                          ? 'bg-green-100 text-green-800'
                          : course.user_enrollment_status === 'waitlisted'
                          ? 'bg-yellow-100 text-yellow-800'
                          : course.user_enrollment_status === 'completed'
                          ? 'bg-blue-100 text-blue-800'
                          : course.user_enrollment_status === 'failed'
                          ? 'bg-red-100 text-red-800'
                          : course.user_enrollment_status === 'retakeable'
                          ? 'bg-orange-100 text-orange-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        Status: {course.user_enrollment_status}
                      </span>
                    </div>
                  )}
                  
                  {course.user_enrollment_message && (
                    <p className="text-sm text-gray-600 mt-1">
                      {course.user_enrollment_message}
                    </p>
                  )}
                </div>
                
                <div className="ml-4">
                  {course.user_enrollment_status === 'enrolled' || 
                   course.user_enrollment_status === 'waitlisted' ? (
                    <span className="text-sm text-gray-500">
                      {course.user_enrollment_status === 'enrolled' ? 'Enrolled' : 'Waitlisted'}
                    </span>
                  ) : course.user_can_enroll ? (
                    <button
                      onClick={() => handleEnroll(course.id)}
                      disabled={enrolling === course.id}
                      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
                    >
                      {enrolling === course.id ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Enrolling...</span>
                        </div>
                      ) : course.user_enrollment_status === 'retakeable' ? (
                        'Retake'
                      ) : (
                        'Enroll'
                      )}
                    </button>
                  ) : (
                    <span className="text-sm text-gray-500">
                      Not Available
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EnrollmentPage;
