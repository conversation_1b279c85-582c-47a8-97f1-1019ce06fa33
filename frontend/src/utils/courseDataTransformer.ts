/**
 * Course Data Transformer
 * Standardizes course enrollment data across all frontend components
 * Ensures consistent handling of enrollment status and course information
 */

export type EnrollmentStatus = 
  | 'available' 
  | 'enrolled' 
  | 'waitlisted' 
  | 'completed' 
  | 'failed' 
  | 'retakeable' 
  | 'retake_limit_exceeded'
  | 'not_available';

export interface EnrollmentAttempt {
  id: number;
  status: string;
  attempt_number: number;
  is_retake: boolean;
  enrolled_at: string;
  completed_at?: string;
  dropped_at?: string;
  final_grade?: number;
  letter_grade?: string;
  is_active: boolean;
}

export interface StandardizedCourse {
  id: string;
  title: string;
  titleAr?: string;
  code: string;
  description?: string;
  descriptionAr?: string;
  department: string;
  departmentName: string;
  instructor: string;
  instructorName: string;
  creditHours: number;
  semester: string;
  year: number;
  maxStudents: number;
  enrolledStudents: number;
  waitlistedStudents?: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  isPublished?: boolean;
  
  // Enrollment-specific fields
  enrollmentStatus: EnrollmentStatus;
  canEnroll: boolean;
  enrollmentMessage: string;
  enrollmentHistory: EnrollmentAttempt[];
  currentAttempt: number;
  isRetake: boolean;
  
  // Display helpers
  statusColor: string;
  statusIcon: string;
  actionButtonText: string;
  actionButtonColor: string;
}

/**
 * Transform raw API course data to standardized format
 */
export const transformCourseData = (apiCourse: any): StandardizedCourse => {
  // Determine enrollment status with fallback logic
  const enrollmentStatus: EnrollmentStatus = 
    apiCourse.user_enrollment_status || 
    apiCourse.enrollment_status || 
    'available';

  // Calculate display properties based on status
  const { statusColor, statusIcon, actionButtonText, actionButtonColor } = 
    getStatusDisplayProperties(enrollmentStatus);

  return {
    id: apiCourse.id?.toString() || '',
    title: apiCourse.title || '',
    titleAr: apiCourse.title_ar || '',
    code: apiCourse.code || '',
    description: apiCourse.description || '',
    descriptionAr: apiCourse.description_ar || '',
    department: apiCourse.department?.toString() || '',
    departmentName: apiCourse.department_name || '',
    instructor: apiCourse.instructor?.toString() || '',
    instructorName: apiCourse.instructor_name || '',
    creditHours: apiCourse.credit_hours || 0,
    semester: apiCourse.semester || '',
    year: apiCourse.year || new Date().getFullYear(),
    maxStudents: apiCourse.max_students || 0,
    enrolledStudents: apiCourse.enrolled_students_count || 0,
    waitlistedStudents: apiCourse.waitlisted_students_count || 0,
    startDate: apiCourse.start_date || '',
    endDate: apiCourse.end_date || '',
    isActive: apiCourse.is_active ?? true,
    isPublished: apiCourse.is_published ?? true,
    
    // Enrollment fields
    enrollmentStatus,
    canEnroll: apiCourse.user_can_enroll ?? false,
    enrollmentMessage: apiCourse.user_enrollment_message || '',
    enrollmentHistory: apiCourse.user_enrollment_history || [],
    currentAttempt: apiCourse.user_current_attempt || 1,
    isRetake: (apiCourse.user_current_attempt || 1) > 1,
    
    // Display properties
    statusColor,
    statusIcon,
    actionButtonText,
    actionButtonColor,
  };
};

/**
 * Get display properties for enrollment status
 */
export const getStatusDisplayProperties = (status: EnrollmentStatus) => {
  switch (status) {
    case 'enrolled':
      return {
        statusColor: 'bg-green-100 text-green-800 border-green-200',
        statusIcon: '✓',
        actionButtonText: 'View Course',
        actionButtonColor: 'btn-secondary'
      };
    
    case 'waitlisted':
      return {
        statusColor: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        statusIcon: '⏳',
        actionButtonText: 'On Waitlist',
        actionButtonColor: 'btn-warning'
      };
    
    case 'completed':
      return {
        statusColor: 'bg-blue-100 text-blue-800 border-blue-200',
        statusIcon: '🏆',
        actionButtonText: 'Completed',
        actionButtonColor: 'btn-success'
      };
    
    case 'failed':
      return {
        statusColor: 'bg-red-100 text-red-800 border-red-200',
        statusIcon: '❌',
        actionButtonText: 'Failed',
        actionButtonColor: 'btn-error'
      };
    
    case 'retakeable':
      return {
        statusColor: 'bg-orange-100 text-orange-800 border-orange-200',
        statusIcon: '🔄',
        actionButtonText: 'Retake',
        actionButtonColor: 'btn-warning'
      };
    
    case 'retake_limit_exceeded':
      return {
        statusColor: 'bg-gray-100 text-gray-800 border-gray-200',
        statusIcon: '🚫',
        actionButtonText: 'Limit Exceeded',
        actionButtonColor: 'btn-disabled'
      };
    
    case 'available':
      return {
        statusColor: 'bg-green-100 text-green-800 border-green-200',
        statusIcon: '📚',
        actionButtonText: 'Enroll',
        actionButtonColor: 'btn-primary'
      };
    
    case 'not_available':
    default:
      return {
        statusColor: 'bg-gray-100 text-gray-800 border-gray-200',
        statusIcon: '🔒',
        actionButtonText: 'Not Available',
        actionButtonColor: 'btn-disabled'
      };
  }
};

/**
 * Transform array of courses
 */
export const transformCoursesArray = (apiCourses: any[]): StandardizedCourse[] => {
  if (!Array.isArray(apiCourses)) {
    console.warn('transformCoursesArray: Expected array, got:', typeof apiCourses);
    return [];
  }
  
  return apiCourses.map(transformCourseData);
};

/**
 * Filter courses by enrollment status
 */
export const filterCoursesByStatus = (
  courses: StandardizedCourse[], 
  status: EnrollmentStatus | 'all'
): StandardizedCourse[] => {
  if (status === 'all') {
    return courses;
  }
  
  return courses.filter(course => course.enrollmentStatus === status);
};

/**
 * Get course counts by status
 */
export const getCourseStatusCounts = (courses: StandardizedCourse[]) => {
  const counts = {
    all: courses.length,
    available: 0,
    enrolled: 0,
    waitlisted: 0,
    completed: 0,
    failed: 0,
    retakeable: 0,
  };
  
  courses.forEach(course => {
    switch (course.enrollmentStatus) {
      case 'available':
        counts.available++;
        break;
      case 'enrolled':
        counts.enrolled++;
        break;
      case 'waitlisted':
        counts.waitlisted++;
        break;
      case 'completed':
        counts.completed++;
        break;
      case 'failed':
        counts.failed++;
        break;
      case 'retakeable':
        counts.retakeable++;
        break;
    }
  });
  
  return counts;
};

/**
 * Check if course enrollment action is allowed
 */
export const canPerformEnrollmentAction = (course: StandardizedCourse): boolean => {
  return course.canEnroll && 
         course.enrollmentStatus !== 'enrolled' && 
         course.enrollmentStatus !== 'waitlisted' &&
         course.enrollmentStatus !== 'completed' &&
         course.enrollmentStatus !== 'retake_limit_exceeded';
};

/**
 * Get enrollment action type
 */
export const getEnrollmentActionType = (course: StandardizedCourse): 'enroll' | 'retake' | 'waitlist' | 'none' => {
  if (!canPerformEnrollmentAction(course)) {
    return 'none';
  }
  
  if (course.enrollmentStatus === 'retakeable') {
    return 'retake';
  }
  
  if (course.enrollmentMessage?.toLowerCase().includes('waitlist')) {
    return 'waitlist';
  }
  
  return 'enroll';
};
