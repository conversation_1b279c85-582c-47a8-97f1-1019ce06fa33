import axios from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired, try to refresh
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken
          });
          
          const newToken = response.data.access;
          localStorage.setItem('access_token', newToken);
          
          // Retry original request
          error.config.headers.Authorization = `Bearer ${newToken}`;
          return apiClient.request(error.config);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Learning Journey API
export const learningJourneyAPI = {
  // Get student's learning journey data
  getLearningJourney: async (studentId) => {
    try {
      const response = await apiClient.get(`/analytics/student-journey/${studentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching learning journey:', error);
      throw error;
    }
  },

  // Get learning progress
  getLearningProgress: async (studentId) => {
    try {
      const response = await apiClient.get(`/analytics/learning-progress/${studentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching learning progress:', error);
      throw error;
    }
  },

  // Get personalized recommendations
  getRecommendations: async (studentId, type = 'all') => {
    try {
      const response = await apiClient.get(`/analytics/recommendations/${studentId}/`, {
        params: { type }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      throw error;
    }
  },

  // Update academic plan
  updateAcademicPlan: async (studentId, planData) => {
    try {
      const response = await apiClient.post(`/analytics/academic-plan/${studentId}/`, planData);
      return response.data;
    } catch (error) {
      console.error('Error updating academic plan:', error);
      throw error;
    }
  }
};

// Course Enrollment API
export const courseEnrollmentAPI = {
  // Get available courses with learning context
  getAvailableCourses: async (studentId, filters = {}) => {
    try {
      const response = await apiClient.get('/courses/', {
        params: {
          student_id: studentId,
          include_learning_context: true,
          ...filters
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching courses:', error);
      throw error;
    }
  },

  // Get course details with learning outcomes
  getCourseDetails: async (courseId, studentId) => {
    try {
      const response = await apiClient.get(`/courses/${courseId}/`, {
        params: { student_id: studentId, include_learning_outcomes: true }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching course details:', error);
      throw error;
    }
  },

  // Enroll in course with learning context
  enrollInCourse: async (courseId, studentId, enrollmentData = {}) => {
    try {
      const response = await apiClient.post(`/courses/${courseId}/enroll/`, {
        student_id: studentId,
        ...enrollmentData
      });
      return response.data;
    } catch (error) {
      console.error('Error enrolling in course:', error);
      throw error;
    }
  },

  // Get enrollment history
  getEnrollmentHistory: async (studentId) => {
    try {
      const response = await apiClient.get(`/courses/enrollments/`, {
        params: { student_id: studentId, include_history: true }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching enrollment history:', error);
      throw error;
    }
  },

  // Check prerequisites
  checkPrerequisites: async (courseId, studentId) => {
    try {
      const response = await apiClient.get(`/courses/${courseId}/prerequisites-check/`, {
        params: { student_id: studentId }
      });
      return response.data;
    } catch (error) {
      console.error('Error checking prerequisites:', error);
      throw error;
    }
  }
};

// Learning Analytics API
export const learningAnalyticsAPI = {
  // Get comprehensive analytics
  getAnalytics: async (studentId, timeRange = 'semester') => {
    try {
      const response = await apiClient.get(`/analytics/comprehensive/${studentId}/`, {
        params: { time_range: timeRange }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching analytics:', error);
      throw error;
    }
  },

  // Get performance metrics
  getPerformanceMetrics: async (studentId, timeRange = 'semester') => {
    try {
      const response = await apiClient.get(`/analytics/performance/${studentId}/`, {
        params: { time_range: timeRange }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      throw error;
    }
  },

  // Get learning patterns
  getLearningPatterns: async (studentId) => {
    try {
      const response = await apiClient.get(`/analytics/learning-patterns/${studentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching learning patterns:', error);
      throw error;
    }
  },

  // Track learning activity
  trackActivity: async (studentId, activityData) => {
    try {
      const response = await apiClient.post(`/analytics/track-activity/${studentId}/`, activityData);
      return response.data;
    } catch (error) {
      console.error('Error tracking activity:', error);
      throw error;
    }
  },

  // Get dashboard metrics
  getDashboardMetrics: async (studentId) => {
    try {
      const response = await apiClient.get(`/analytics/dashboard-metrics/`, {
        params: { student_id: studentId }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      throw error;
    }
  }
};

// Student Data API
export const studentDataAPI = {
  // Get student profile with academic context
  getStudentProfile: async (studentId) => {
    try {
      const response = await apiClient.get(`/users/${studentId}/`, {
        params: { include_academic_context: true }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching student profile:', error);
      throw error;
    }
  },

  // Get academic records
  getAcademicRecords: async (studentId) => {
    try {
      const response = await apiClient.get(`/grades/student/${studentId}/`, {
        params: { include_transcript: true }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching academic records:', error);
      throw error;
    }
  },

  // Get current enrollments
  getCurrentEnrollments: async (studentId) => {
    try {
      const response = await apiClient.get(`/courses/enrollments/`, {
        params: { 
          student_id: studentId, 
          status: 'enrolled',
          include_course_details: true 
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching current enrollments:', error);
      throw error;
    }
  },

  // Get schedule
  getSchedule: async (studentId, timeRange = 'current') => {
    try {
      const response = await apiClient.get(`/schedules/personal/`, {
        params: { 
          student_id: studentId,
          time_range: timeRange
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching schedule:', error);
      throw error;
    }
  }
};

// Notifications API
export const notificationsAPI = {
  // Get smart notifications
  getSmartNotifications: async (studentId, limit = 10) => {
    try {
      const response = await apiClient.get(`/notifications/`, {
        params: { 
          recipient_id: studentId,
          limit,
          include_smart_insights: true
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  },

  // Mark notification as read
  markAsRead: async (notificationId) => {
    try {
      const response = await apiClient.patch(`/notifications/${notificationId}/`, {
        is_read: true
      });
      return response.data;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }
};

// Academic Planning API
export const academicPlanningAPI = {
  // Create academic plan
  createAcademicPlan: async (studentId, planData) => {
    try {
      const response = await apiClient.post(`/analytics/academic-plans/`, {
        student_id: studentId,
        ...planData
      });
      return response.data;
    } catch (error) {
      console.error('Error creating academic plan:', error);
      throw error;
    }
  },

  // Get degree requirements
  getDegreeRequirements: async (studentId) => {
    try {
      const response = await apiClient.get(`/analytics/degree-requirements/${studentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching degree requirements:', error);
      throw error;
    }
  },

  // Get graduation timeline
  getGraduationTimeline: async (studentId) => {
    try {
      const response = await apiClient.get(`/analytics/graduation-timeline/${studentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching graduation timeline:', error);
      throw error;
    }
  },

  // Simulate course plan
  simulateCoursePlan: async (studentId, courseSelections) => {
    try {
      const response = await apiClient.post(`/analytics/simulate-plan/${studentId}/`, {
        course_selections: courseSelections
      });
      return response.data;
    } catch (error) {
      console.error('Error simulating course plan:', error);
      throw error;
    }
  }
};

// Export all APIs
export default {
  learningJourneyAPI,
  courseEnrollmentAPI,
  learningAnalyticsAPI,
  studentDataAPI,
  notificationsAPI,
  academicPlanningAPI
};
