// Comprehensive Arabic translations for Learning Experience components

export const translations = {
  en: {
    // Learning Journey Dashboard
    learningJourney: {
      title: "Your Learning Journey",
      subtitle: "Track your academic progress, discover learning opportunities, and plan your path to success",
      creditsEarned: "Credits Earned",
      coursesCompleted: "Courses Completed",
      currentSemester: "Current Semester",
      gpaProgress: "GPA Progress",
      academicPath: "Your Academic Path",
      nextSteps: "Next Steps",
      recommendations: "Personalized Recommendations",
      readyForNext: "Ready for Your Next Step?",
      planNextSemester: "Plan Next Semester",
      browseCourses: "Browse and select courses",
      exploreCourses: "Explore Courses",
      discoverOpportunities: "Discover new learning opportunities",
      setGoals: "Set Goals",
      defineObjectives: "Define your academic objectives"
    },

    // Course Enrollment
    courseEnrollment: {
      title: "Discover Your Next Learning Adventure",
      subtitle: "Explore courses that align with your goals, build on your knowledge, and advance your academic journey",
      credits: "Credits",
      enrolled: "Enrolled",
      rating: "Rating",
      duration: "Duration",
      prerequisites: "Prerequisites",
      whatYouLearn: "What You'll Learn",
      learnMore: "Learn More",
      prerequisitesRequired: "Prerequisites Required",
      courseFull: "Course Full",
      enrollNow: "Enroll Now",
      enrollmentSuccessful: "Enrollment Successful!",
      enrollmentMessage: "You're now enrolled in {courseName}. Check your dashboard for next steps.",
      continueExploring: "Continue Exploring",
      scheduleLocation: "Schedule & Location",
      courseImpact: "Course Impact",
      fitsYourDegree: "Fits your {program} degree",
      advancesPath: "Advances your learning path",
      studentRating: "{rating}/5 student rating",
      prerequisitesCheck: "Prerequisites Check",
      spotsRemaining: "{spots} spots remaining",
      enrollmentDeadline: "Enrollment deadline: {date}"
    },

    // Academic Planning Wizard
    academicPlanning: {
      title: "Academic Planning Wizard",
      defineGoals: "Define Your Goals",
      goalsDescription: "What do you want to achieve in your academic journey?",
      setTimeline: "Set Your Timeline",
      timelineDescription: "When do you plan to complete your degree?",
      learningPreferences: "Learning Preferences",
      preferencesDescription: "How do you learn best?",
      courseSelection: "Course Selection",
      selectionDescription: "Choose courses that align with your goals",
      reviewPlan: "Review Your Plan",
      reviewDescription: "Confirm your academic roadmap",
      
      // Goals
      careerPrep: "Career Preparation",
      careerPrepDesc: "Prepare for a specific career path",
      graduateSchool: "Graduate School",
      graduateSchoolDesc: "Prepare for advanced studies",
      skillDevelopment: "Skill Development",
      skillDevelopmentDesc: "Develop specific technical skills",
      research: "Research Experience",
      researchDesc: "Gain research and analytical skills",
      entrepreneurship: "Entrepreneurship",
      entrepreneurshipDesc: "Start your own business",
      personalGrowth: "Personal Growth",
      personalGrowthDesc: "Broaden knowledge and perspectives",
      
      // Timeline
      twoYears: "2 Years",
      acceleratedPath: "Accelerated path",
      fourYears: "4 Years",
      standardTimeline: "Standard timeline",
      fiveYears: "5 Years",
      flexiblePace: "Flexible pace",
      sixPlusYears: "6+ Years",
      partTimeStudy: "Part-time study",
      recommended: "Recommended",
      
      // Learning Styles
      visualLearner: "Visual Learner",
      visualDesc: "Learn best with diagrams and visual aids",
      auditoryLearner: "Auditory Learner",
      auditoryDesc: "Learn best through listening and discussion",
      handsOnLearner: "Hands-on Learner",
      handsOnDesc: "Learn best through practice and experience",
      readingWriting: "Reading/Writing",
      readingWritingDesc: "Learn best through text and written materials",
      
      // Schedule Preferences
      morningClasses: "Morning Classes",
      afternoonClasses: "Afternoon Classes",
      eveningClasses: "Evening Classes",
      flexibleSchedule: "Flexible Schedule",
      
      // Course Load
      lightLoad: "Light Load (12-13 credits)",
      lightLoadDesc: "More time for each course",
      standardLoad: "Standard Load (15-16 credits)",
      standardLoadDesc: "Balanced approach",
      heavyLoad: "Heavy Load (18+ credits)",
      heavyLoadDesc: "Accelerated progress",
      
      // Review
      yourGoals: "Your Goals",
      timeline: "Timeline",
      learningStyle: "Learning Style",
      nextStepsTitle: "Next Steps",
      meetAdvisor: "Meet with academic advisor",
      registerCourses: "Register for recommended courses",
      setupSchedule: "Set up study schedule",
      joinGroups: "Join relevant student groups",
      planReady: "Your Personalized Academic Plan is Ready!",
      planReadyDesc: "Based on your goals and preferences, we've created a customized learning path to help you succeed.",
      startJourney: "Start Your Journey"
    },

    // Learning Analytics
    analytics: {
      title: "Your Learning Analytics",
      subtitle: "Insights to help you succeed in your academic journey",
      thisWeek: "This Week",
      thisMonth: "This Month",
      thisSemester: "This Semester",
      thisYear: "This Year",
      currentGPA: "Current GPA",
      studyHours: "Study Hours/Week",
      attendanceRate: "Attendance Rate",
      assignmentRate: "Assignment Rate",
      degreeProgress: "Degree Progress",
      creditsCompleted: "Credits Completed",
      coursesCompleted: "Courses Completed",
      inProgress: "In Progress",
      projectedGraduation: "Projected Graduation",
      yourStrengths: "Your Strengths",
      growthOpportunities: "Growth Opportunities",
      learningPatterns: "Your Learning Patterns",
      peakStudyTime: "Peak Study Time",
      learningStyle: "Learning Style",
      bestDay: "Best Day",
      sessionLength: "Session Length",
      personalizedRecommendations: "Personalized Recommendations",
      highPriority: "HIGH PRIORITY",
      mediumPriority: "MEDIUM PRIORITY",
      lowPriority: "LOW PRIORITY",
      actNow: "Act Now",
      course: "COURSE",
      skill: "SKILL",
      studyHabit: "STUDY HABIT"
    },

    // Common UI Elements
    common: {
      loading: "Loading...",
      error: "Error",
      success: "Success",
      cancel: "Cancel",
      save: "Save",
      edit: "Edit",
      delete: "Delete",
      confirm: "Confirm",
      back: "Back",
      next: "Next",
      previous: "Previous",
      close: "Close",
      viewMore: "View More",
      showLess: "Show Less",
      search: "Search",
      filter: "Filter",
      sort: "Sort",
      refresh: "Refresh",
      welcome: "Welcome",
      dashboard: "Dashboard",
      courses: "Courses",
      planning: "Planning",
      analytics: "Analytics",
      profile: "Profile",
      settings: "Settings",
      help: "Help",
      logout: "Logout"
    }
  },

  ar: {
    // Learning Journey Dashboard
    learningJourney: {
      title: "رحلتك التعليمية",
      subtitle: "تتبع تقدمك الأكاديمي، واكتشف الفرص التعليمية، وخطط لطريقك نحو النجاح",
      creditsEarned: "الساعات المكتسبة",
      coursesCompleted: "المقررات المكتملة",
      currentSemester: "الفصل الحالي",
      gpaProgress: "تقدم المعدل التراكمي",
      academicPath: "مسارك الأكاديمي",
      nextSteps: "الخطوات التالية",
      recommendations: "التوصيات الشخصية",
      readyForNext: "مستعد للخطوة التالية؟",
      planNextSemester: "خطط للفصل القادم",
      browseCourses: "تصفح واختر المقررات",
      exploreCourses: "استكشف المقررات",
      discoverOpportunities: "اكتشف فرص تعليمية جديدة",
      setGoals: "حدد الأهداف",
      defineObjectives: "حدد أهدافك الأكاديمية"
    },

    // Course Enrollment
    courseEnrollment: {
      title: "اكتشف مغامرتك التعليمية القادمة",
      subtitle: "استكشف المقررات التي تتماشى مع أهدافك، وتبني على معرفتك، وتقدم رحلتك الأكاديمية",
      credits: "ساعات",
      enrolled: "مسجل",
      rating: "التقييم",
      duration: "المدة",
      prerequisites: "المتطلبات السابقة",
      whatYouLearn: "ما ستتعلمه",
      learnMore: "تعلم المزيد",
      prerequisitesRequired: "المتطلبات السابقة مطلوبة",
      courseFull: "المقرر مكتمل",
      enrollNow: "سجل الآن",
      enrollmentSuccessful: "تم التسجيل بنجاح!",
      enrollmentMessage: "أنت الآن مسجل في {courseName}. تحقق من لوحة التحكم للخطوات التالية.",
      continueExploring: "تابع الاستكشاف",
      scheduleLocation: "الجدول والموقع",
      courseImpact: "تأثير المقرر",
      fitsYourDegree: "يناسب درجة {program} الخاصة بك",
      advancesPath: "يقدم مسارك التعليمي",
      studentRating: "تقييم الطلاب {rating}/5",
      prerequisitesCheck: "فحص المتطلبات السابقة",
      spotsRemaining: "{spots} مقاعد متبقية",
      enrollmentDeadline: "موعد انتهاء التسجيل: {date}"
    },

    // Academic Planning Wizard
    academicPlanning: {
      title: "معالج التخطيط الأكاديمي",
      defineGoals: "حدد أهدافك",
      goalsDescription: "ما الذي تريد تحقيقه في رحلتك الأكاديمية؟",
      setTimeline: "حدد الجدول الزمني",
      timelineDescription: "متى تخطط لإكمال درجتك؟",
      learningPreferences: "تفضيلات التعلم",
      preferencesDescription: "كيف تتعلم بشكل أفضل؟",
      courseSelection: "اختيار المقررات",
      selectionDescription: "اختر المقررات التي تتماشى مع أهدافك",
      reviewPlan: "راجع خطتك",
      reviewDescription: "أكد خارطة طريقك الأكاديمية",
      
      // Goals
      careerPrep: "الإعداد المهني",
      careerPrepDesc: "الاستعداد لمسار مهني محدد",
      graduateSchool: "الدراسات العليا",
      graduateSchoolDesc: "الاستعداد للدراسات المتقدمة",
      skillDevelopment: "تطوير المهارات",
      skillDevelopmentDesc: "تطوير مهارات تقنية محددة",
      research: "الخبرة البحثية",
      researchDesc: "اكتساب مهارات البحث والتحليل",
      entrepreneurship: "ريادة الأعمال",
      entrepreneurshipDesc: "بدء عملك الخاص",
      personalGrowth: "النمو الشخصي",
      personalGrowthDesc: "توسيع المعرفة والآفاق",
      
      // Timeline
      twoYears: "سنتان",
      acceleratedPath: "مسار مسرع",
      fourYears: "أربع سنوات",
      standardTimeline: "الجدول الزمني القياسي",
      fiveYears: "خمس سنوات",
      flexiblePace: "وتيرة مرنة",
      sixPlusYears: "6 سنوات أو أكثر",
      partTimeStudy: "دراسة بدوام جزئي",
      recommended: "موصى به",
      
      // Learning Styles
      visualLearner: "متعلم بصري",
      visualDesc: "يتعلم بشكل أفضل مع الرسوم البيانية والوسائل البصرية",
      auditoryLearner: "متعلم سمعي",
      auditoryDesc: "يتعلم بشكل أفضل من خلال الاستماع والمناقشة",
      handsOnLearner: "متعلم عملي",
      handsOnDesc: "يتعلم بشكل أفضل من خلال الممارسة والخبرة",
      readingWriting: "القراءة/الكتابة",
      readingWritingDesc: "يتعلم بشكل أفضل من خلال النصوص والمواد المكتوبة",
      
      // Schedule Preferences
      morningClasses: "فصول صباحية",
      afternoonClasses: "فصول بعد الظهر",
      eveningClasses: "فصول مسائية",
      flexibleSchedule: "جدول مرن",
      
      // Course Load
      lightLoad: "حمولة خفيفة (12-13 ساعة)",
      lightLoadDesc: "وقت أكثر لكل مقرر",
      standardLoad: "حمولة قياسية (15-16 ساعة)",
      standardLoadDesc: "نهج متوازن",
      heavyLoad: "حمولة ثقيلة (18+ ساعة)",
      heavyLoadDesc: "تقدم مسرع",
      
      // Review
      yourGoals: "أهدافك",
      timeline: "الجدول الزمني",
      learningStyle: "أسلوب التعلم",
      nextStepsTitle: "الخطوات التالية",
      meetAdvisor: "لقاء مع المرشد الأكاديمي",
      registerCourses: "التسجيل في المقررات الموصى بها",
      setupSchedule: "إعداد جدول الدراسة",
      joinGroups: "الانضمام إلى المجموعات الطلابية ذات الصلة",
      planReady: "خطتك الأكاديمية الشخصية جاهزة!",
      planReadyDesc: "بناءً على أهدافك وتفضيلاتك، أنشأنا مسارًا تعليميًا مخصصًا لمساعدتك على النجاح.",
      startJourney: "ابدأ رحلتك"
    },

    // Learning Analytics
    analytics: {
      title: "تحليلات التعلم الخاصة بك",
      subtitle: "رؤى لمساعدتك على النجاح في رحلتك الأكاديمية",
      thisWeek: "هذا الأسبوع",
      thisMonth: "هذا الشهر",
      thisSemester: "هذا الفصل",
      thisYear: "هذا العام",
      currentGPA: "المعدل التراكمي الحالي",
      studyHours: "ساعات الدراسة/الأسبوع",
      attendanceRate: "معدل الحضور",
      assignmentRate: "معدل التكليفات",
      degreeProgress: "تقدم الدرجة",
      creditsCompleted: "الساعات المكتملة",
      coursesCompleted: "المقررات المكتملة",
      inProgress: "قيد التقدم",
      projectedGraduation: "التخرج المتوقع",
      yourStrengths: "نقاط قوتك",
      growthOpportunities: "فرص النمو",
      learningPatterns: "أنماط التعلم الخاصة بك",
      peakStudyTime: "وقت الدراسة الأمثل",
      learningStyle: "أسلوب التعلم",
      bestDay: "أفضل يوم",
      sessionLength: "مدة الجلسة",
      personalizedRecommendations: "التوصيات الشخصية",
      highPriority: "أولوية عالية",
      mediumPriority: "أولوية متوسطة",
      lowPriority: "أولوية منخفضة",
      actNow: "تصرف الآن",
      course: "مقرر",
      skill: "مهارة",
      studyHabit: "عادة دراسية"
    },

    // Common UI Elements
    common: {
      loading: "جاري التحميل...",
      error: "خطأ",
      success: "نجح",
      cancel: "إلغاء",
      save: "حفظ",
      edit: "تعديل",
      delete: "حذف",
      confirm: "تأكيد",
      back: "رجوع",
      next: "التالي",
      previous: "السابق",
      close: "إغلاق",
      viewMore: "عرض المزيد",
      showLess: "عرض أقل",
      search: "بحث",
      filter: "تصفية",
      sort: "ترتيب",
      refresh: "تحديث",
      welcome: "مرحباً",
      dashboard: "لوحة التحكم",
      courses: "المقررات",
      planning: "التخطيط",
      analytics: "التحليلات",
      profile: "الملف الشخصي",
      settings: "الإعدادات",
      help: "مساعدة",
      logout: "تسجيل الخروج"
    }
  }
};

export default translations;
