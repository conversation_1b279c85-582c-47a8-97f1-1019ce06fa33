import React, { createContext, useContext, useState, useEffect } from 'react';
import translations from './translations';

// Create i18n context
const I18nContext = createContext();

// Language direction mapping
const LANGUAGE_DIRECTIONS = {
  en: 'ltr',
  ar: 'rtl'
};

// Default language
const DEFAULT_LANGUAGE = 'en';

// Get stored language or default
const getStoredLanguage = () => {
  try {
    return localStorage.getItem('preferred_language') || DEFAULT_LANGUAGE;
  } catch (error) {
    return DEFAULT_LANGUAGE;
  }
};

// Store language preference
const storeLanguage = (language) => {
  try {
    localStorage.setItem('preferred_language', language);
  } catch (error) {
    console.warn('Could not store language preference:', error);
  }
};

// I18n Provider Component
export const I18nProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(getStoredLanguage);
  const [isRTL, setIsRTL] = useState(LANGUAGE_DIRECTIONS[getStoredLanguage()] === 'rtl');

  // Update document direction and language when language changes
  useEffect(() => {
    const direction = LANGUAGE_DIRECTIONS[currentLanguage] || 'ltr';
    const isRightToLeft = direction === 'rtl';
    
    // Update document attributes
    document.documentElement.dir = direction;
    document.documentElement.lang = currentLanguage;
    
    // Update body class for RTL styling
    if (isRightToLeft) {
      document.body.classList.add('rtl');
      document.body.classList.remove('ltr');
    } else {
      document.body.classList.add('ltr');
      document.body.classList.remove('rtl');
    }
    
    setIsRTL(isRightToLeft);
    storeLanguage(currentLanguage);
  }, [currentLanguage]);

  // Change language function
  const changeLanguage = (newLanguage) => {
    if (translations[newLanguage]) {
      setCurrentLanguage(newLanguage);
    } else {
      console.warn(`Language '${newLanguage}' not supported`);
    }
  };

  // Get translation function
  const t = (key, params = {}) => {
    try {
      // Split the key by dots to navigate nested objects
      const keys = key.split('.');
      let translation = translations[currentLanguage];
      
      // Navigate through the nested structure
      for (const k of keys) {
        if (translation && typeof translation === 'object' && k in translation) {
          translation = translation[k];
        } else {
          // Fallback to English if key not found in current language
          translation = translations[DEFAULT_LANGUAGE];
          for (const fallbackKey of keys) {
            if (translation && typeof translation === 'object' && fallbackKey in translation) {
              translation = translation[fallbackKey];
            } else {
              console.warn(`Translation key '${key}' not found in ${currentLanguage} or ${DEFAULT_LANGUAGE}`);
              return key; // Return the key itself as fallback
            }
          }
          break;
        }
      }
      
      // If translation is still an object, return the key
      if (typeof translation === 'object') {
        console.warn(`Translation key '${key}' points to an object, not a string`);
        return key;
      }
      
      // Replace parameters in the translation
      if (typeof translation === 'string' && Object.keys(params).length > 0) {
        return translation.replace(/\{(\w+)\}/g, (match, paramKey) => {
          return params[paramKey] !== undefined ? params[paramKey] : match;
        });
      }
      
      return translation || key;
    } catch (error) {
      console.error('Translation error:', error);
      return key;
    }
  };

  // Get available languages
  const getAvailableLanguages = () => {
    return Object.keys(translations).map(lang => ({
      code: lang,
      name: lang === 'ar' ? 'العربية' : 'English',
      direction: LANGUAGE_DIRECTIONS[lang] || 'ltr'
    }));
  };

  // Format number based on locale
  const formatNumber = (number, options = {}) => {
    try {
      const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
      return new Intl.NumberFormat(locale, options).format(number);
    } catch (error) {
      return number.toString();
    }
  };

  // Format date based on locale
  const formatDate = (date, options = {}) => {
    try {
      const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
      const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...options
      };
      return new Intl.DateTimeFormat(locale, defaultOptions).format(new Date(date));
    } catch (error) {
      return date.toString();
    }
  };

  // Get text direction class
  const getDirectionClass = (additionalClasses = '') => {
    const directionClass = isRTL ? 'rtl' : 'ltr';
    return additionalClasses ? `${directionClass} ${additionalClasses}` : directionClass;
  };

  // Get flex direction for RTL
  const getFlexDirection = (defaultDirection = 'row') => {
    if (!isRTL) return defaultDirection;
    
    switch (defaultDirection) {
      case 'row':
        return 'row-reverse';
      case 'row-reverse':
        return 'row';
      default:
        return defaultDirection;
    }
  };

  // Get margin/padding direction
  const getSpacingDirection = (property, value) => {
    if (!isRTL) return { [property]: value };
    
    const spacingMap = {
      'marginLeft': 'marginRight',
      'marginRight': 'marginLeft',
      'paddingLeft': 'paddingRight',
      'paddingRight': 'paddingLeft',
      'left': 'right',
      'right': 'left'
    };
    
    const mappedProperty = spacingMap[property] || property;
    return { [mappedProperty]: value };
  };

  const contextValue = {
    currentLanguage,
    isRTL,
    changeLanguage,
    t,
    getAvailableLanguages,
    formatNumber,
    formatDate,
    getDirectionClass,
    getFlexDirection,
    getSpacingDirection,
    direction: LANGUAGE_DIRECTIONS[currentLanguage] || 'ltr'
  };

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

// Custom hook to use i18n
export const useI18n = () => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

// HOC for components that need i18n
export const withI18n = (Component) => {
  return function I18nComponent(props) {
    const i18n = useI18n();
    return <Component {...props} i18n={i18n} />;
  };
};

// Language selector component
export const LanguageSelector = ({ className = '' }) => {
  const { currentLanguage, changeLanguage, getAvailableLanguages, isRTL } = useI18n();
  const languages = getAvailableLanguages();

  return (
    <div className={`language-selector ${className}`}>
      <select
        value={currentLanguage}
        onChange={(e) => changeLanguage(e.target.value)}
        className={`px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
          isRTL ? 'text-right' : 'text-left'
        }`}
        dir={isRTL ? 'rtl' : 'ltr'}
      >
        {languages.map((lang) => (
          <option key={lang.code} value={lang.code}>
            {lang.name}
          </option>
        ))}
      </select>
    </div>
  );
};

export default I18nContext;
