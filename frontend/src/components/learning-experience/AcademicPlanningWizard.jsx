import React, { useState, useEffect } from 'react';
import { 
  ChevronLeft, 
  ChevronRight, 
  CheckCircle, 
  Target, 
  Calendar, 
  BookOpen,
  TrendingUp,
  Award,
  Lightbulb,
  AlertCircle,
  Users,
  Clock
} from 'lucide-react';

const AcademicPlanningWizard = ({ student, onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [planningData, setPlanningData] = useState({
    goals: [],
    timeline: '',
    preferences: {},
    selectedCourses: [],
    careerPath: ''
  });

  const steps = [
    {
      id: 'goals',
      title: 'Define Your Goals',
      description: 'What do you want to achieve in your academic journey?',
      icon: Target
    },
    {
      id: 'timeline',
      title: 'Set Your Timeline',
      description: 'When do you plan to complete your degree?',
      icon: Calendar
    },
    {
      id: 'preferences',
      title: 'Learning Preferences',
      description: 'How do you learn best?',
      icon: Lightbulb
    },
    {
      id: 'courses',
      title: 'Course Selection',
      description: 'Choose courses that align with your goals',
      icon: BookOpen
    },
    {
      id: 'review',
      title: 'Review Your Plan',
      description: 'Confirm your academic roadmap',
      icon: CheckCircle
    }
  ];

  const GoalsStep = () => {
    const goalOptions = [
      {
        id: 'career_prep',
        title: 'Career Preparation',
        description: 'Prepare for a specific career path',
        icon: '💼',
        popular: true
      },
      {
        id: 'graduate_school',
        title: 'Graduate School',
        description: 'Prepare for advanced studies',
        icon: '🎓',
        popular: false
      },
      {
        id: 'skill_development',
        title: 'Skill Development',
        description: 'Develop specific technical skills',
        icon: '🛠️',
        popular: true
      },
      {
        id: 'research',
        title: 'Research Experience',
        description: 'Gain research and analytical skills',
        icon: '🔬',
        popular: false
      },
      {
        id: 'entrepreneurship',
        title: 'Entrepreneurship',
        description: 'Start your own business',
        icon: '🚀',
        popular: false
      },
      {
        id: 'personal_growth',
        title: 'Personal Growth',
        description: 'Broaden knowledge and perspectives',
        icon: '🌱',
        popular: true
      }
    ];

    const toggleGoal = (goalId) => {
      setPlanningData(prev => ({
        ...prev,
        goals: prev.goals.includes(goalId) 
          ? prev.goals.filter(g => g !== goalId)
          : [...prev.goals, goalId]
      }));
    };

    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">What are your academic goals?</h2>
          <p className="text-gray-600">Select all that apply to create a personalized learning path</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {goalOptions.map(goal => (
            <div
              key={goal.id}
              onClick={() => toggleGoal(goal.id)}
              className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all ${
                planningData.goals.includes(goal.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
            >
              {goal.popular && (
                <span className="absolute top-2 right-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                  Popular
                </span>
              )}
              
              <div className="flex items-start space-x-4">
                <div className="text-3xl">{goal.icon}</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{goal.title}</h3>
                  <p className="text-sm text-gray-600">{goal.description}</p>
                </div>
                {planningData.goals.includes(goal.id) && (
                  <CheckCircle className="w-6 h-6 text-blue-600" />
                )}
              </div>
            </div>
          ))}
        </div>

        {planningData.goals.length > 0 && (
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">Selected Goals:</h3>
            <div className="flex flex-wrap gap-2">
              {planningData.goals.map(goalId => {
                const goal = goalOptions.find(g => g.id === goalId);
                return (
                  <span key={goalId} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                    {goal?.title}
                  </span>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  };

  const TimelineStep = () => {
    const timelineOptions = [
      { id: '2_years', label: '2 Years', description: 'Accelerated path', recommended: false },
      { id: '4_years', label: '4 Years', description: 'Standard timeline', recommended: true },
      { id: '5_years', label: '5 Years', description: 'Flexible pace', recommended: false },
      { id: '6_years', label: '6+ Years', description: 'Part-time study', recommended: false }
    ];

    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">When do you plan to graduate?</h2>
          <p className="text-gray-600">Choose a timeline that fits your lifestyle and goals</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {timelineOptions.map(option => (
            <div
              key={option.id}
              onClick={() => setPlanningData(prev => ({ ...prev, timeline: option.id }))}
              className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all ${
                planningData.timeline === option.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
            >
              {option.recommended && (
                <span className="absolute top-2 right-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  Recommended
                </span>
              )}
              
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-1">{option.label}</h3>
                <p className="text-sm text-gray-600">{option.description}</p>
                
                {planningData.timeline === option.id && (
                  <CheckCircle className="w-6 h-6 text-blue-600 mx-auto mt-3" />
                )}
              </div>
            </div>
          ))}
        </div>

        {planningData.timeline && (
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Calendar className="w-5 h-5 text-green-600" />
              <h3 className="font-semibold text-green-900">Your Timeline</h3>
            </div>
            <p className="text-green-800">
              Based on your {timelineOptions.find(o => o.id === planningData.timeline)?.label} timeline, 
              you'll need to complete approximately {planningData.timeline === '2_years' ? '18-20' : 
              planningData.timeline === '4_years' ? '15-16' : 
              planningData.timeline === '5_years' ? '12-14' : '8-10'} credit hours per semester.
            </p>
          </div>
        )}
      </div>
    );
  };

  const PreferencesStep = () => {
    const learningStyles = [
      { id: 'visual', label: 'Visual Learner', icon: '👁️', description: 'Learn best with diagrams and visual aids' },
      { id: 'auditory', label: 'Auditory Learner', icon: '👂', description: 'Learn best through listening and discussion' },
      { id: 'kinesthetic', label: 'Hands-on Learner', icon: '✋', description: 'Learn best through practice and experience' },
      { id: 'reading', label: 'Reading/Writing', icon: '📚', description: 'Learn best through text and written materials' }
    ];

    const schedulePreferences = [
      { id: 'morning', label: 'Morning Classes', time: '8:00 AM - 12:00 PM' },
      { id: 'afternoon', label: 'Afternoon Classes', time: '12:00 PM - 5:00 PM' },
      { id: 'evening', label: 'Evening Classes', time: '5:00 PM - 9:00 PM' },
      { id: 'flexible', label: 'Flexible Schedule', time: 'Any time works' }
    ];

    const updatePreference = (category, value) => {
      setPlanningData(prev => ({
        ...prev,
        preferences: {
          ...prev.preferences,
          [category]: value
        }
      }));
    };

    return (
      <div className="space-y-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">How do you learn best?</h2>
          <p className="text-gray-600">Help us recommend courses that match your learning style</p>
        </div>

        {/* Learning Style */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Learning Style</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {learningStyles.map(style => (
              <div
                key={style.id}
                onClick={() => updatePreference('learningStyle', style.id)}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  planningData.preferences.learningStyle === style.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{style.icon}</span>
                  <div>
                    <h4 className="font-medium text-gray-900">{style.label}</h4>
                    <p className="text-sm text-gray-600">{style.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Schedule Preference */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferred Class Times</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {schedulePreferences.map(pref => (
              <div
                key={pref.id}
                onClick={() => updatePreference('schedule', pref.id)}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  planningData.preferences.schedule === pref.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                <h4 className="font-medium text-gray-900">{pref.label}</h4>
                <p className="text-sm text-gray-600">{pref.time}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Course Load */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Course Load Preference</h3>
          <div className="space-y-2">
            {[
              { id: 'light', label: 'Light Load (12-13 credits)', desc: 'More time for each course' },
              { id: 'standard', label: 'Standard Load (15-16 credits)', desc: 'Balanced approach' },
              { id: 'heavy', label: 'Heavy Load (18+ credits)', desc: 'Accelerated progress' }
            ].map(load => (
              <div
                key={load.id}
                onClick={() => updatePreference('courseLoad', load.id)}
                className={`p-3 rounded-lg border cursor-pointer transition-all ${
                  planningData.preferences.courseLoad === load.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium text-gray-900">{load.label}</h4>
                    <p className="text-sm text-gray-600">{load.desc}</p>
                  </div>
                  {planningData.preferences.courseLoad === load.id && (
                    <CheckCircle className="w-5 h-5 text-blue-600" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const ReviewStep = () => {
    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Review Your Academic Plan</h2>
          <p className="text-gray-600">Confirm your personalized learning roadmap</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Goals Summary */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Your Goals
            </h3>
            <div className="space-y-2">
              {planningData.goals.map(goalId => (
                <div key={goalId} className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-blue-600" />
                  <span className="text-blue-800 capitalize">{goalId.replace('_', ' ')}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Timeline Summary */}
          <div className="bg-green-50 rounded-lg p-6">
            <h3 className="font-semibold text-green-900 mb-3 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Timeline
            </h3>
            <div className="text-green-800">
              <div className="font-medium">
                {planningData.timeline?.replace('_', ' ').toUpperCase()} graduation plan
              </div>
              <div className="text-sm mt-1">
                Recommended course load: {
                  planningData.preferences.courseLoad === 'light' ? '12-13 credits' :
                  planningData.preferences.courseLoad === 'heavy' ? '18+ credits' :
                  '15-16 credits'
                } per semester
              </div>
            </div>
          </div>

          {/* Learning Preferences */}
          <div className="bg-purple-50 rounded-lg p-6">
            <h3 className="font-semibold text-purple-900 mb-3 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2" />
              Learning Style
            </h3>
            <div className="text-purple-800">
              <div className="font-medium capitalize">
                {planningData.preferences.learningStyle?.replace('_', ' ')} learner
              </div>
              <div className="text-sm mt-1 capitalize">
                Prefers {planningData.preferences.schedule?.replace('_', ' ')} classes
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-orange-50 rounded-lg p-6">
            <h3 className="font-semibold text-orange-900 mb-3 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Next Steps
            </h3>
            <div className="space-y-2 text-sm text-orange-800">
              <div>✓ Meet with academic advisor</div>
              <div>✓ Register for recommended courses</div>
              <div>✓ Set up study schedule</div>
              <div>✓ Join relevant student groups</div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white text-center">
          <Award className="w-12 h-12 mx-auto mb-3" />
          <h3 className="text-xl font-bold mb-2">Your Personalized Academic Plan is Ready!</h3>
          <p className="mb-4">
            Based on your goals and preferences, we've created a customized learning path to help you succeed.
          </p>
          <button 
            onClick={() => onComplete(planningData)}
            className="bg-white text-blue-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Start Your Journey
          </button>
        </div>
      </div>
    );
  };

  const renderStep = () => {
    switch (steps[currentStep].id) {
      case 'goals': return <GoalsStep />;
      case 'timeline': return <TimelineStep />;
      case 'preferences': return <PreferencesStep />;
      case 'review': return <ReviewStep />;
      default: return <GoalsStep />;
    }
  };

  const canProceed = () => {
    switch (steps[currentStep].id) {
      case 'goals': return planningData.goals.length > 0;
      case 'timeline': return planningData.timeline !== '';
      case 'preferences': return Object.keys(planningData.preferences).length >= 2;
      default: return true;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                index <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                {index < currentStep ? (
                  <CheckCircle className="w-6 h-6" />
                ) : (
                  <step.icon className="w-5 h-5" />
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-16 h-1 mx-2 ${
                  index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <h2 className="text-lg font-semibold text-gray-900">
            Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
          </h2>
          <p className="text-gray-600">{steps[currentStep].description}</p>
        </div>
      </div>

      {/* Step Content */}
      <div className="mb-8">
        {renderStep()}
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
          disabled={currentStep === 0}
          className="flex items-center space-x-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="w-4 h-4" />
          <span>Previous</span>
        </button>

        <button
          onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}
          disabled={!canProceed() || currentStep === steps.length - 1}
          className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>Next</span>
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default AcademicPlanningWizard;
