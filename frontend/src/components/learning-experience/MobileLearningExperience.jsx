import React, { useState, useEffect } from 'react';
import { 
  BookOpen, 
  Target, 
  TrendingUp, 
  Award, 
  Calendar, 
  User,
  Bell,
  Menu,
  X,
  ChevronRight,
  CheckCircle,
  Clock,
  Star,
  ArrowRight,
  Home,
  Search,
  BarChart3,
  Settings
} from 'lucide-react';
import { useI18n } from '../../i18n/i18nContext';

const MobileLearningExperience = ({ student }) => {
  const { t, isRTL, getDirectionClass } = useI18n();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [learningData, setLearningData] = useState(null);

  useEffect(() => {
    // Fetch mobile-optimized learning data
    fetchMobileLearningData();
  }, [student.id]);

  const fetchMobileLearningData = async () => {
    try {
      // This would connect to your mobile-optimized API
      const mockData = {
        progress: {
          completion_percentage: 37,
          credits_earned: 45,
          credits_total: 120,
          current_gpa: 3.7,
          courses_completed: 15,
          courses_in_progress: 4
        },
        quick_stats: [
          { label: t('learningJourney.creditsEarned'), value: '45/120', icon: Award, color: 'text-green-600' },
          { label: t('analytics.currentGPA'), value: '3.7', icon: TrendingUp, color: 'text-blue-600' },
          { label: t('analytics.studyHours'), value: '24h', icon: Clock, color: 'text-purple-600' },
          { label: t('analytics.attendanceRate'), value: '96%', icon: CheckCircle, color: 'text-orange-600' }
        ],
        recent_activities: [
          { id: 1, type: 'enrollment', message: 'Enrolled in CS301 Data Structures', time: '2 hours ago' },
          { id: 2, type: 'grade', message: 'Received grade for MATH201 assignment', time: '1 day ago' },
          { id: 3, type: 'recommendation', message: 'New course recommendation available', time: '2 days ago' }
        ],
        upcoming_deadlines: [
          { id: 1, title: 'CS201 Project Submission', date: '2025-01-20', course: 'CS201' },
          { id: 2, title: 'MATH301 Midterm Exam', date: '2025-01-25', course: 'MATH301' }
        ]
      };
      setLearningData(mockData);
    } catch (error) {
      console.error('Error fetching mobile learning data:', error);
    }
  };

  const MobileHeader = () => (
    <div className={`bg-white shadow-sm border-b border-gray-200 px-4 py-3 ${getDirectionClass()}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <button 
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            className="p-2 rounded-lg hover:bg-gray-100"
          >
            {showMobileMenu ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              {t('common.welcome')}, {student.first_name}!
            </h1>
            <p className="text-sm text-gray-600">{t('learningJourney.subtitle')}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button className="p-2 rounded-lg hover:bg-gray-100 relative">
            <Bell className="w-5 h-5 text-gray-600" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          </button>
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-semibold">
              {student.first_name?.[0]}{student.last_name?.[0]}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const MobileProgressCard = () => (
    <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-4 text-white mx-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <div>
          <h2 className="text-lg font-bold">{t('learningJourney.title')}</h2>
          <p className="text-blue-100 text-sm">{t('analytics.degreeProgress')}</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold">{learningData?.progress.completion_percentage}%</div>
          <div className="text-xs text-blue-100">{t('common.complete')}</div>
        </div>
      </div>
      
      <div className="w-full bg-white bg-opacity-20 rounded-full h-2 mb-3">
        <div 
          className="bg-white h-2 rounded-full transition-all duration-300"
          style={{ width: `${learningData?.progress.completion_percentage}%` }}
        ></div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 text-center text-sm">
        <div>
          <div className="font-semibold">{learningData?.progress.credits_earned}</div>
          <div className="text-blue-100">{t('learningJourney.creditsEarned')}</div>
        </div>
        <div>
          <div className="font-semibold">{learningData?.progress.current_gpa}</div>
          <div className="text-blue-100">{t('analytics.currentGPA')}</div>
        </div>
      </div>
    </div>
  );

  const QuickStatsGrid = () => (
    <div className="px-4 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">{t('analytics.title')}</h3>
      <div className="grid grid-cols-2 gap-3">
        {learningData?.quick_stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div className="flex items-center space-x-2 mb-2">
              <stat.icon className={`w-5 h-5 ${stat.color}`} />
              <span className="text-sm font-medium text-gray-700">{stat.label}</span>
            </div>
            <div className="text-xl font-bold text-gray-900">{stat.value}</div>
          </div>
        ))}
      </div>
    </div>
  );

  const RecentActivities = () => (
    <div className="px-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900">{t('common.recent')} {t('common.activities')}</h3>
        <button className="text-blue-600 text-sm font-medium">{t('common.viewMore')}</button>
      </div>
      <div className="space-y-3">
        {learningData?.recent_activities.map((activity) => (
          <div key={activity.id} className="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
            <p className="text-gray-900 text-sm">{activity.message}</p>
            <p className="text-gray-500 text-xs mt-1">{activity.time}</p>
          </div>
        ))}
      </div>
    </div>
  );

  const UpcomingDeadlines = () => (
    <div className="px-4 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">{t('common.upcoming')} {t('common.deadlines')}</h3>
      <div className="space-y-3">
        {learningData?.upcoming_deadlines.map((deadline) => (
          <div key={deadline.id} className="bg-orange-50 border border-orange-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-orange-900">{deadline.title}</h4>
                <p className="text-sm text-orange-700">{deadline.course}</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-orange-900">{deadline.date}</p>
                <Clock className="w-4 h-4 text-orange-600 ml-auto" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const QuickActions = () => (
    <div className="px-4 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">{t('common.quickActions')}</h3>
      <div className="grid grid-cols-2 gap-3">
        <button className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 text-left">
          <BookOpen className="w-6 h-6 text-blue-600 mb-2" />
          <h4 className="font-medium text-gray-900">{t('courseEnrollment.exploreCourses')}</h4>
          <p className="text-sm text-gray-600">{t('courseEnrollment.discoverOpportunities')}</p>
        </button>
        <button className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 text-left">
          <Target className="w-6 h-6 text-green-600 mb-2" />
          <h4 className="font-medium text-gray-900">{t('academicPlanning.planNextSemester')}</h4>
          <p className="text-sm text-gray-600">{t('academicPlanning.browseCourses')}</p>
        </button>
        <button className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 text-left">
          <Calendar className="w-6 h-6 text-purple-600 mb-2" />
          <h4 className="font-medium text-gray-900">{t('common.viewSchedule')}</h4>
          <p className="text-sm text-gray-600">{t('common.checkTimetable')}</p>
        </button>
        <button className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 text-left">
          <BarChart3 className="w-6 h-6 text-orange-600 mb-2" />
          <h4 className="font-medium text-gray-900">{t('analytics.trackProgress')}</h4>
          <p className="text-sm text-gray-600">{t('analytics.monitorGrowth')}</p>
        </button>
      </div>
    </div>
  );

  const MobileBottomNav = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
      <div className="flex justify-around">
        {[
          { id: 'dashboard', icon: Home, label: t('common.dashboard') },
          { id: 'courses', icon: BookOpen, label: t('common.courses') },
          { id: 'search', icon: Search, label: t('common.search') },
          { id: 'analytics', icon: BarChart3, label: t('common.analytics') },
          { id: 'profile', icon: User, label: t('common.profile') }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
              activeTab === tab.id 
                ? 'text-blue-600 bg-blue-50' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <tab.icon className="w-5 h-5 mb-1" />
            <span className="text-xs font-medium">{tab.label}</span>
          </button>
        ))}
      </div>
    </div>
  );

  const MobileSideMenu = () => (
    showMobileMenu && (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setShowMobileMenu(false)}>
        <div className={`fixed top-0 ${isRTL ? 'right-0' : 'left-0'} h-full w-80 bg-white shadow-lg transform transition-transform`}>
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">{t('common.menu')}</h2>
              <button onClick={() => setShowMobileMenu(false)}>
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
          <div className="p-4">
            <nav className="space-y-2">
              {[
                { id: 'dashboard', icon: Home, label: t('common.dashboard') },
                { id: 'courses', icon: BookOpen, label: t('common.courses') },
                { id: 'planning', icon: Target, label: t('common.planning') },
                { id: 'analytics', icon: BarChart3, label: t('common.analytics') },
                { id: 'profile', icon: User, label: t('common.profile') },
                { id: 'settings', icon: Settings, label: t('common.settings') }
              ].map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    setActiveTab(item.id);
                    setShowMobileMenu(false);
                  }}
                  className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-100 text-left"
                >
                  <item.icon className="w-5 h-5 text-gray-600" />
                  <span className="font-medium text-gray-900">{item.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>
    )
  );

  if (!learningData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 pb-20 ${getDirectionClass()}`}>
      <MobileHeader />
      <MobileSideMenu />
      
      <div className="pt-4">
        <MobileProgressCard />
        <QuickStatsGrid />
        <RecentActivities />
        <UpcomingDeadlines />
        <QuickActions />
      </div>
      
      <MobileBottomNav />
    </div>
  );
};

export default MobileLearningExperience;
