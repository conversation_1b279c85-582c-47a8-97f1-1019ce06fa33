import React, { useState, useEffect } from 'react';
import { 
  ChevronRight, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Clock, 
  Users, 
  Star,
  BookOpen,
  Target,
  Lightbulb,
  ArrowRight,
  Calendar,
  MapPin
} from 'lucide-react';

const IntuitiveCourseEnrollment = ({ student }) => {
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [enrollmentStep, setEnrollmentStep] = useState('browse'); // browse, details, confirm, success
  const [courses, setCourses] = useState([]);
  const [prerequisites, setPrerequisites] = useState([]);
  const [learningOutcomes, setLearningOutcomes] = useState([]);

  const CourseCard = ({ course, onSelect }) => {
    const difficultyColor = {
      'Beginner': 'bg-green-100 text-green-800',
      'Intermediate': 'bg-yellow-100 text-yellow-800',
      'Advanced': 'bg-red-100 text-red-800'
    };

    const canEnroll = course.prerequisites_met && course.available_spots > 0;

    return (
      <div 
        className={`bg-white rounded-xl shadow-sm border-2 transition-all cursor-pointer hover:shadow-md ${
          canEnroll ? 'border-gray-200 hover:border-blue-300' : 'border-red-200 bg-red-50'
        }`}
        onClick={() => canEnroll && onSelect(course)}
      >
        <div className="p-6">
          {/* Course Header */}
          <div className="flex justify-between items-start mb-4">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-sm font-medium text-blue-600">{course.code}</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${difficultyColor[course.difficulty]}`}>
                  {course.difficulty}
                </span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{course.title}</h3>
              <p className="text-gray-600 text-sm line-clamp-2">{course.description}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">{course.credit_hours}</div>
              <div className="text-xs text-gray-500">Credits</div>
            </div>
          </div>

          {/* Course Metrics */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1">
                <Users className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium">{course.enrolled_count}/{course.max_students}</span>
              </div>
              <div className="text-xs text-gray-500">Enrolled</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1">
                <Star className="w-4 h-4 text-yellow-500" />
                <span className="text-sm font-medium">{course.rating}</span>
              </div>
              <div className="text-xs text-gray-500">Rating</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium">{course.duration}</span>
              </div>
              <div className="text-xs text-gray-500">Duration</div>
            </div>
          </div>

          {/* Prerequisites Status */}
          {course.prerequisites && course.prerequisites.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Prerequisites</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {course.prerequisites.map(prereq => (
                  <span 
                    key={prereq.id}
                    className={`px-2 py-1 rounded text-xs ${
                      prereq.completed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {prereq.completed && <CheckCircle className="w-3 h-3 inline mr-1" />}
                    {!prereq.completed && <AlertTriangle className="w-3 h-3 inline mr-1" />}
                    {prereq.code}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Learning Outcomes Preview */}
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <Lightbulb className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">What You'll Learn</span>
            </div>
            <ul className="text-sm text-gray-600 space-y-1">
              {course.learning_outcomes?.slice(0, 2).map((outcome, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="w-3 h-3 text-green-500 mt-1 flex-shrink-0" />
                  <span>{outcome}</span>
                </li>
              ))}
              {course.learning_outcomes?.length > 2 && (
                <li className="text-blue-600 text-xs">+{course.learning_outcomes.length - 2} more outcomes</li>
              )}
            </ul>
          </div>

          {/* Action Button */}
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {course.schedule && `${course.schedule.days} • ${course.schedule.time}`}
            </div>
            {canEnroll ? (
              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <span>Learn More</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            ) : (
              <div className="flex items-center space-x-2 text-red-600">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-sm">
                  {!course.prerequisites_met ? 'Prerequisites Required' : 'Course Full'}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const CourseDetailModal = ({ course, onClose, onEnroll }) => {
    if (!course) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            {/* Header */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-sm font-medium text-blue-600">{course.code}</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                    {course.credit_hours} Credits
                  </span>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">{course.title}</h2>
                <p className="text-gray-600">{course.description}</p>
              </div>
              <button 
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            {/* Course Information Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {/* Learning Outcomes */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
                  <Target className="w-5 h-5 mr-2" />
                  Learning Outcomes
                </h3>
                <ul className="space-y-2">
                  {course.learning_outcomes?.map((outcome, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm text-blue-800">
                      <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                      <span>{outcome}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Course Details */}
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Calendar className="w-5 h-5 mr-2" />
                    Schedule & Location
                  </h3>
                  <div className="space-y-2 text-sm text-gray-700">
                    <div>📅 {course.schedule?.days} • {course.schedule?.time}</div>
                    <div>📍 {course.location || 'Online'}</div>
                    <div>👨‍🏫 {course.instructor}</div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4">
                  <h3 className="font-semibold text-green-900 mb-3">Course Impact</h3>
                  <div className="space-y-2 text-sm text-green-800">
                    <div>🎯 Fits your {course.degree_program} degree</div>
                    <div>📈 Advances your learning path</div>
                    <div>⭐ {course.rating}/5 student rating</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Prerequisites Check */}
            {course.prerequisites && course.prerequisites.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-yellow-900 mb-3 flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  Prerequisites Check
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {course.prerequisites.map(prereq => (
                    <div key={prereq.id} className="flex items-center space-x-2">
                      {prereq.completed ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`text-sm ${prereq.completed ? 'text-green-800' : 'text-red-800'}`}>
                        {prereq.code} - {prereq.title}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Enrollment Action */}
            <div className="flex justify-between items-center pt-6 border-t">
              <div className="text-sm text-gray-600">
                {course.available_spots} spots remaining • Enrollment deadline: {course.enrollment_deadline}
              </div>
              <div className="flex space-x-3">
                <button 
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button 
                  onClick={() => onEnroll(course)}
                  disabled={!course.prerequisites_met || course.available_spots === 0}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {course.prerequisites_met ? 'Enroll Now' : 'Prerequisites Required'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleCourseSelect = (course) => {
    setSelectedCourse(course);
    setEnrollmentStep('details');
  };

  const handleEnroll = async (course) => {
    try {
      // Call enrollment API
      const response = await fetch(`/api/courses/${course.id}/enroll/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setEnrollmentStep('success');
      } else {
        // Handle enrollment error
        const error = await response.json();
        alert(error.message || 'Enrollment failed');
      }
    } catch (error) {
      console.error('Enrollment error:', error);
      alert('Enrollment failed. Please try again.');
    }
  };

  // Mock data for demonstration
  const mockCourses = [
    {
      id: 1,
      code: 'CS301',
      title: 'Data Structures and Algorithms',
      description: 'Learn fundamental data structures and algorithmic thinking essential for software development.',
      credit_hours: 3,
      difficulty: 'Intermediate',
      enrolled_count: 28,
      max_students: 35,
      available_spots: 7,
      rating: 4.7,
      duration: '16 weeks',
      prerequisites_met: true,
      prerequisites: [
        { id: 1, code: 'CS201', title: 'Programming Fundamentals', completed: true },
        { id: 2, code: 'MATH201', title: 'Discrete Mathematics', completed: true }
      ],
      learning_outcomes: [
        'Implement and analyze fundamental data structures',
        'Design efficient algorithms for common problems',
        'Understand time and space complexity analysis',
        'Apply algorithmic thinking to solve real-world problems'
      ],
      schedule: { days: 'Mon, Wed, Fri', time: '10:00 AM - 11:00 AM' },
      location: 'Computer Lab A',
      instructor: 'Dr. Sarah Johnson',
      degree_program: 'Computer Science',
      enrollment_deadline: 'Jan 15, 2025'
    },
    {
      id: 2,
      code: 'MATH301',
      title: 'Linear Algebra',
      description: 'Advanced mathematical concepts including vector spaces, matrices, and linear transformations.',
      credit_hours: 4,
      difficulty: 'Advanced',
      enrolled_count: 15,
      max_students: 25,
      available_spots: 10,
      rating: 4.3,
      duration: '16 weeks',
      prerequisites_met: false,
      prerequisites: [
        { id: 3, code: 'MATH202', title: 'Calculus II', completed: false },
        { id: 4, code: 'MATH210', title: 'Proof Techniques', completed: true }
      ],
      learning_outcomes: [
        'Master vector space operations',
        'Solve systems of linear equations',
        'Understand eigenvalues and eigenvectors'
      ],
      schedule: { days: 'Tue, Thu', time: '2:00 PM - 3:30 PM' },
      location: 'Math Building 201',
      instructor: 'Prof. Michael Chen',
      degree_program: 'Mathematics',
      enrollment_deadline: 'Jan 15, 2025'
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Discover Your Next Learning Adventure
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Explore courses that align with your goals, build on your knowledge, and advance your academic journey
        </p>
      </div>

      {/* Course Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockCourses.map(course => (
          <CourseCard 
            key={course.id} 
            course={course} 
            onSelect={handleCourseSelect}
          />
        ))}
      </div>

      {/* Course Detail Modal */}
      {enrollmentStep === 'details' && (
        <CourseDetailModal
          course={selectedCourse}
          onClose={() => {
            setSelectedCourse(null);
            setEnrollmentStep('browse');
          }}
          onEnroll={handleEnroll}
        />
      )}

      {/* Success Modal */}
      {enrollmentStep === 'success' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-md w-full p-6 text-center">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Enrollment Successful!</h2>
            <p className="text-gray-600 mb-6">
              You're now enrolled in {selectedCourse?.title}. Check your dashboard for next steps.
            </p>
            <button 
              onClick={() => {
                setEnrollmentStep('browse');
                setSelectedCourse(null);
              }}
              className="w-full px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Continue Exploring
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntuitiveCourseEnrollment;
