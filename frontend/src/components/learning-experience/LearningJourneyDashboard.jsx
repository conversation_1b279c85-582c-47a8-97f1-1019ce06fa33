import React, { useState, useEffect } from 'react';
import { 
  BookOpen, 
  Target, 
  TrendingUp, 
  Award, 
  Calendar, 
  MapPin,
  CheckCircle,
  Clock,
  AlertCircle,
  Lightbulb
} from 'lucide-react';

const LearningJourneyDashboard = ({ student }) => {
  const [learningPath, setLearningPath] = useState(null);
  const [currentSemester, setCurrentSemester] = useState(null);
  const [recommendations, setRecommendations] = useState([]);

  useEffect(() => {
    // Fetch student's learning journey data
    fetchLearningJourney();
  }, [student.id]);

  const fetchLearningJourney = async () => {
    try {
      // This would connect to your learning analytics API
      const response = await fetch(`/api/students/${student.id}/learning-journey/`);
      const data = await response.json();
      setLearningPath(data.learning_path);
      setCurrentSemester(data.current_semester);
      setRecommendations(data.recommendations);
    } catch (error) {
      console.error('Error fetching learning journey:', error);
    }
  };

  const LearningProgressCard = ({ title, progress, total, icon: Icon, color }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${color}`}>
            <Icon className="w-5 h-5 text-white" />
          </div>
          <h3 className="font-semibold text-gray-900">{title}</h3>
        </div>
        <span className="text-2xl font-bold text-gray-900">{progress}/{total}</span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${color.replace('bg-', 'bg-')}`}
          style={{ width: `${(progress / total) * 100}%` }}
        ></div>
      </div>
      
      <p className="text-sm text-gray-600">
        {Math.round((progress / total) * 100)}% Complete
      </p>
    </div>
  );

  const LearningPathStep = ({ step, isActive, isCompleted, isNext }) => (
    <div className={`relative flex items-center p-4 rounded-lg border-2 transition-all ${
      isActive ? 'border-blue-500 bg-blue-50' : 
      isCompleted ? 'border-green-500 bg-green-50' : 
      isNext ? 'border-orange-500 bg-orange-50' : 
      'border-gray-200 bg-gray-50'
    }`}>
      <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
        isCompleted ? 'bg-green-500' : 
        isActive ? 'bg-blue-500' : 
        isNext ? 'bg-orange-500' : 
        'bg-gray-300'
      }`}>
        {isCompleted ? (
          <CheckCircle className="w-6 h-6 text-white" />
        ) : (
          <span className="text-white font-semibold">{step.order}</span>
        )}
      </div>
      
      <div className="ml-4 flex-1">
        <h4 className="font-semibold text-gray-900">{step.title}</h4>
        <p className="text-sm text-gray-600">{step.description}</p>
        {step.courses && (
          <div className="mt-2 flex flex-wrap gap-1">
            {step.courses.map(course => (
              <span key={course.id} className="px-2 py-1 bg-white rounded text-xs text-gray-700 border">
                {course.code}
              </span>
            ))}
          </div>
        )}
      </div>
      
      {isNext && (
        <div className="ml-4">
          <button className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
            Start Now
          </button>
        </div>
      )}
    </div>
  );

  const RecommendationCard = ({ recommendation }) => (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
      <div className="flex items-start space-x-3">
        <Lightbulb className="w-5 h-5 text-blue-600 mt-1" />
        <div>
          <h4 className="font-semibold text-blue-900">{recommendation.title}</h4>
          <p className="text-sm text-blue-700 mt-1">{recommendation.description}</p>
          {recommendation.action && (
            <button className="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium">
              {recommendation.action} →
            </button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Your Learning Journey
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Track your academic progress, discover learning opportunities, and plan your path to success
        </p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <LearningProgressCard
          title="Credits Earned"
          progress={45}
          total={120}
          icon={Award}
          color="bg-green-500"
        />
        <LearningProgressCard
          title="Courses Completed"
          progress={15}
          total={40}
          icon={BookOpen}
          color="bg-blue-500"
        />
        <LearningProgressCard
          title="Current Semester"
          progress={3}
          total={6}
          icon={Calendar}
          color="bg-purple-500"
        />
        <LearningProgressCard
          title="GPA Progress"
          progress={3.7}
          total={4.0}
          icon={TrendingUp}
          color="bg-orange-500"
        />
      </div>

      {/* Learning Path Visualization */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <MapPin className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-900">Your Academic Path</h2>
        </div>
        
        <div className="space-y-4">
          {learningPath?.steps?.map((step, index) => (
            <LearningPathStep
              key={step.id}
              step={step}
              isActive={step.status === 'active'}
              isCompleted={step.status === 'completed'}
              isNext={step.status === 'next'}
            />
          )) || (
            // Placeholder data for demo
            <>
              <LearningPathStep
                step={{
                  id: 1,
                  order: 1,
                  title: "Foundation Courses",
                  description: "Complete your core requirements",
                  courses: [
                    { id: 1, code: "MATH101" },
                    { id: 2, code: "ENG101" }
                  ]
                }}
                isCompleted={true}
              />
              <LearningPathStep
                step={{
                  id: 2,
                  order: 2,
                  title: "Major Prerequisites",
                  description: "Build your subject expertise",
                  courses: [
                    { id: 3, code: "CS201" },
                    { id: 4, code: "MATH201" }
                  ]
                }}
                isActive={true}
              />
              <LearningPathStep
                step={{
                  id: 3,
                  order: 3,
                  title: "Specialization Track",
                  description: "Choose your area of focus",
                  courses: [
                    { id: 5, code: "CS301" },
                    { id: 6, code: "CS302" }
                  ]
                }}
                isNext={true}
              />
            </>
          )}
        </div>
      </div>

      {/* Personalized Recommendations */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Target className="w-6 h-6 text-orange-600" />
          <h2 className="text-xl font-bold text-gray-900">Personalized Recommendations</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {recommendations.length > 0 ? (
            recommendations.map(rec => (
              <RecommendationCard key={rec.id} recommendation={rec} />
            ))
          ) : (
            // Placeholder recommendations
            <>
              <RecommendationCard
                recommendation={{
                  id: 1,
                  title: "Consider Summer Courses",
                  description: "Take MATH202 this summer to stay on track for graduation",
                  action: "View Summer Schedule"
                }}
              />
              <RecommendationCard
                recommendation={{
                  id: 2,
                  title: "Join Study Groups",
                  description: "Connect with peers in CS201 to improve your understanding",
                  action: "Find Study Groups"
                }}
              />
              <RecommendationCard
                recommendation={{
                  id: 3,
                  title: "Career Planning",
                  description: "Schedule a meeting with your academic advisor",
                  action: "Book Appointment"
                }}
              />
              <RecommendationCard
                recommendation={{
                  id: 4,
                  title: "Skill Development",
                  description: "Consider adding a programming certification",
                  action: "Explore Options"
                }}
              />
            </>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <h2 className="text-xl font-bold mb-4">Ready for Your Next Step?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-4 text-left transition-all">
            <Calendar className="w-6 h-6 mb-2" />
            <h3 className="font-semibold">Plan Next Semester</h3>
            <p className="text-sm opacity-90">Browse and select courses</p>
          </button>
          <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-4 text-left transition-all">
            <BookOpen className="w-6 h-6 mb-2" />
            <h3 className="font-semibold">Explore Courses</h3>
            <p className="text-sm opacity-90">Discover new learning opportunities</p>
          </button>
          <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-4 text-left transition-all">
            <Target className="w-6 h-6 mb-2" />
            <h3 className="font-semibold">Set Goals</h3>
            <p className="text-sm opacity-90">Define your academic objectives</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default LearningJourneyDashboard;
