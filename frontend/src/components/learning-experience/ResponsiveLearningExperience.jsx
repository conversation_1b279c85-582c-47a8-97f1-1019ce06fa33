import React, { useState, useEffect } from 'react';
import LearningExperienceHub from './LearningExperienceHub';
import MobileLearningExperience from './MobileLearningExperience';
import { useI18n } from '../../i18n/i18nContext';

const ResponsiveLearningExperience = ({ student }) => {
  const { isRTL } = useI18n();
  const [isMobile, setIsMobile] = useState(false);
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      setIsMobile(width < 768); // Mobile breakpoint at 768px
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Add mobile-specific meta tags
  useEffect(() => {
    if (isMobile) {
      // Ensure proper mobile viewport
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      if (!viewportMeta) {
        viewportMeta = document.createElement('meta');
        viewportMeta.name = 'viewport';
        document.head.appendChild(viewportMeta);
      }
      viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';

      // Add mobile-specific styles
      document.body.classList.add('mobile-learning');
      
      // Prevent zoom on input focus (iOS)
      const inputs = document.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.style.fontSize = '16px';
      });
    } else {
      document.body.classList.remove('mobile-learning');
    }

    return () => {
      document.body.classList.remove('mobile-learning');
    };
  }, [isMobile]);

  // Add RTL support for mobile
  useEffect(() => {
    if (isMobile && isRTL) {
      document.body.classList.add('mobile-rtl');
    } else {
      document.body.classList.remove('mobile-rtl');
    }

    return () => {
      document.body.classList.remove('mobile-rtl');
    };
  }, [isMobile, isRTL]);

  // Device detection utilities
  const getDeviceType = () => {
    const { width } = screenSize;
    
    if (width < 480) return 'mobile-small';
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    if (width < 1440) return 'desktop';
    return 'desktop-large';
  };

  const isTablet = () => {
    return screenSize.width >= 768 && screenSize.width < 1024;
  };

  const isTouchDevice = () => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  };

  // Performance optimization for mobile
  const mobileOptimizations = {
    // Reduce animations on mobile for better performance
    reduceMotion: isMobile,
    // Lazy load images
    lazyLoad: isMobile,
    // Reduce data usage
    compressImages: isMobile,
    // Optimize for touch
    touchOptimized: isTouchDevice()
  };

  // Pass device context to components
  const deviceContext = {
    isMobile,
    isTablet: isTablet(),
    isDesktop: !isMobile && !isTablet(),
    deviceType: getDeviceType(),
    screenSize,
    optimizations: mobileOptimizations,
    touchDevice: isTouchDevice()
  };

  // Loading state for mobile
  if (isMobile && !student) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-6">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Loading Your Learning Experience</h2>
          <p className="text-gray-600">Preparing your personalized dashboard...</p>
        </div>
      </div>
    );
  }

  // Error boundary for mobile
  if (!student) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load</h2>
          <p className="text-gray-600 mb-4">
            We're having trouble loading your learning experience. Please check your connection and try again.
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Render appropriate component based on screen size
  if (isMobile) {
    return (
      <MobileLearningExperience 
        student={student} 
        deviceContext={deviceContext}
      />
    );
  }

  // Desktop/Tablet experience
  return (
    <LearningExperienceHub 
      student={student} 
      deviceContext={deviceContext}
    />
  );
};

// Higher-order component for responsive learning experience
export const withResponsiveLearning = (WrappedComponent) => {
  return function ResponsiveLearningComponent(props) {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 768);
      };

      checkMobile();
      window.addEventListener('resize', checkMobile);
      return () => window.removeEventListener('resize', checkMobile);
    }, []);

    return (
      <WrappedComponent 
        {...props} 
        isMobile={isMobile}
        deviceType={isMobile ? 'mobile' : 'desktop'}
      />
    );
  };
};

// Hook for responsive learning features
export const useResponsiveLearning = () => {
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenSize: { width: 1024, height: 768 }
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setDeviceInfo({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        screenSize: { width, height }
      });
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    return () => window.removeEventListener('resize', updateDeviceInfo);
  }, []);

  return deviceInfo;
};

// Utility functions for responsive design
export const responsiveUtils = {
  // Get appropriate grid columns based on screen size
  getGridCols: (mobile = 1, tablet = 2, desktop = 3) => {
    const width = window.innerWidth;
    if (width < 768) return mobile;
    if (width < 1024) return tablet;
    return desktop;
  },

  // Get appropriate spacing based on screen size
  getSpacing: (mobile = 4, tablet = 6, desktop = 8) => {
    const width = window.innerWidth;
    if (width < 768) return mobile;
    if (width < 1024) return tablet;
    return desktop;
  },

  // Get appropriate font size based on screen size
  getFontSize: (mobile = 'text-sm', tablet = 'text-base', desktop = 'text-lg') => {
    const width = window.innerWidth;
    if (width < 768) return mobile;
    if (width < 1024) return tablet;
    return desktop;
  },

  // Check if device supports hover
  supportsHover: () => {
    return window.matchMedia('(hover: hover)').matches;
  },

  // Check if device prefers reduced motion
  prefersReducedMotion: () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }
};

export default ResponsiveLearningExperience;
