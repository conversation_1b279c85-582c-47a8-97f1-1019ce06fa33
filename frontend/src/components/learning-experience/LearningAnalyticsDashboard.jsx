import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Award, 
  Clock, 
  BookOpen,
  Users,
  Star,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

const LearningAnalyticsDashboard = ({ student }) => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [timeRange, setTimeRange] = useState('semester'); // week, month, semester, year
  const [selectedMetric, setSelectedMetric] = useState('performance');

  useEffect(() => {
    fetchAnalyticsData();
  }, [student.id, timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      const response = await fetch(`/api/students/${student.id}/analytics/?range=${timeRange}`);
      const data = await response.json();
      setAnalyticsData(data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      // Use mock data for demonstration
      setAnalyticsData(getMockAnalyticsData());
    }
  };

  const getMockAnalyticsData = () => ({
    performance: {
      current_gpa: 3.7,
      gpa_trend: 0.2,
      course_completion_rate: 94,
      average_grade: 87,
      grade_trend: 5
    },
    engagement: {
      study_hours_per_week: 24,
      attendance_rate: 96,
      assignment_submission_rate: 98,
      discussion_participation: 85
    },
    progress: {
      credits_completed: 45,
      total_credits_required: 120,
      courses_completed: 15,
      courses_in_progress: 4,
      projected_graduation: 'Spring 2027'
    },
    strengths: [
      { area: 'Mathematics', score: 92, trend: 'up' },
      { area: 'Programming', score: 89, trend: 'up' },
      { area: 'Problem Solving', score: 85, trend: 'stable' }
    ],
    improvement_areas: [
      { area: 'Written Communication', score: 76, suggestion: 'Consider taking ENG201 Writing Workshop' },
      { area: 'Time Management', score: 72, suggestion: 'Try the Pomodoro Technique for better focus' }
    ],
    learning_patterns: {
      peak_study_time: '2:00 PM - 4:00 PM',
      preferred_learning_style: 'Visual',
      most_productive_day: 'Tuesday',
      average_session_length: '45 minutes'
    },
    recommendations: [
      {
        type: 'course',
        title: 'Advanced Data Structures',
        reason: 'Builds on your strong programming foundation',
        priority: 'high'
      },
      {
        type: 'skill',
        title: 'Technical Writing',
        reason: 'Improve communication skills for career success',
        priority: 'medium'
      },
      {
        type: 'study_habit',
        title: 'Join Study Groups',
        reason: 'Enhance collaborative learning',
        priority: 'low'
      }
    ]
  });

  const MetricCard = ({ title, value, trend, icon: Icon, color, subtitle }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-2 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div className="flex items-center space-x-1">
          {trend > 0 ? (
            <ArrowUp className="w-4 h-4 text-green-600" />
          ) : trend < 0 ? (
            <ArrowDown className="w-4 h-4 text-red-600" />
          ) : null}
          <span className={`text-sm font-medium ${
            trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-500'
          }`}>
            {trend !== 0 && `${Math.abs(trend)}${typeof trend === 'number' && trend < 1 ? '%' : ''}`}
          </span>
        </div>
      </div>
      
      <div className="mb-2">
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        <div className="text-sm text-gray-600">{title}</div>
      </div>
      
      {subtitle && (
        <div className="text-xs text-gray-500">{subtitle}</div>
      )}
    </div>
  );

  const ProgressChart = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Degree Progress</h3>
      
      <div className="space-y-4">
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Credits Completed</span>
            <span className="text-sm text-gray-600">
              {analyticsData?.progress.credits_completed}/{analyticsData?.progress.total_credits_required}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${(analyticsData?.progress.credits_completed / analyticsData?.progress.total_credits_required) * 100}%` 
              }}
            ></div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 pt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {analyticsData?.progress.courses_completed}
            </div>
            <div className="text-sm text-gray-600">Courses Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {analyticsData?.progress.courses_in_progress}
            </div>
            <div className="text-sm text-gray-600">In Progress</div>
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-3 mt-4">
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-green-900">
              Projected Graduation: {analyticsData?.progress.projected_graduation}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const StrengthsAndImprovements = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Strengths */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Star className="w-5 h-5 text-yellow-500 mr-2" />
          Your Strengths
        </h3>
        
        <div className="space-y-3">
          {analyticsData?.strengths.map((strength, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div>
                <div className="font-medium text-green-900">{strength.area}</div>
                <div className="text-sm text-green-700">Score: {strength.score}%</div>
              </div>
              <div className="flex items-center space-x-1">
                {strength.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-600" />}
                {strength.trend === 'down' && <TrendingDown className="w-4 h-4 text-red-600" />}
                {strength.trend === 'stable' && <Activity className="w-4 h-4 text-gray-600" />}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Improvement Areas */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Target className="w-5 h-5 text-blue-500 mr-2" />
          Growth Opportunities
        </h3>
        
        <div className="space-y-3">
          {analyticsData?.improvement_areas.map((area, index) => (
            <div key={index} className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="font-medium text-blue-900">{area.area}</div>
                <div className="text-sm text-blue-700">Score: {area.score}%</div>
              </div>
              <div className="text-sm text-blue-800">{area.suggestion}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const LearningPatterns = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Activity className="w-5 h-5 text-purple-500 mr-2" />
        Your Learning Patterns
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <Clock className="w-8 h-8 text-purple-600 mx-auto mb-2" />
          <div className="font-medium text-purple-900">Peak Study Time</div>
          <div className="text-sm text-purple-700">{analyticsData?.learning_patterns.peak_study_time}</div>
        </div>
        
        <div className="text-center p-4 bg-indigo-50 rounded-lg">
          <BookOpen className="w-8 h-8 text-indigo-600 mx-auto mb-2" />
          <div className="font-medium text-indigo-900">Learning Style</div>
          <div className="text-sm text-indigo-700">{analyticsData?.learning_patterns.preferred_learning_style}</div>
        </div>
        
        <div className="text-center p-4 bg-pink-50 rounded-lg">
          <Calendar className="w-8 h-8 text-pink-600 mx-auto mb-2" />
          <div className="font-medium text-pink-900">Best Day</div>
          <div className="text-sm text-pink-700">{analyticsData?.learning_patterns.most_productive_day}</div>
        </div>
        
        <div className="text-center p-4 bg-teal-50 rounded-lg">
          <Activity className="w-8 h-8 text-teal-600 mx-auto mb-2" />
          <div className="font-medium text-teal-900">Session Length</div>
          <div className="text-sm text-teal-700">{analyticsData?.learning_patterns.average_session_length}</div>
        </div>
      </div>
    </div>
  );

  const SmartRecommendations = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Lightbulb className="w-5 h-5 text-yellow-500 mr-2" />
        Personalized Recommendations
      </h3>
      
      <div className="space-y-4">
        {analyticsData?.recommendations.map((rec, index) => (
          <div key={index} className={`p-4 rounded-lg border-l-4 ${
            rec.priority === 'high' ? 'border-red-500 bg-red-50' :
            rec.priority === 'medium' ? 'border-yellow-500 bg-yellow-50' :
            'border-green-500 bg-green-50'
          }`}>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    rec.type === 'course' ? 'bg-blue-100 text-blue-800' :
                    rec.type === 'skill' ? 'bg-purple-100 text-purple-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {rec.type.replace('_', ' ').toUpperCase()}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                    rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {rec.priority.toUpperCase()} PRIORITY
                  </span>
                </div>
                <h4 className="font-medium text-gray-900">{rec.title}</h4>
                <p className="text-sm text-gray-600">{rec.reason}</p>
              </div>
              <button className="ml-4 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                Act Now
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const TimeRangeSelector = () => (
    <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6">
      {[
        { id: 'week', label: 'This Week' },
        { id: 'month', label: 'This Month' },
        { id: 'semester', label: 'This Semester' },
        { id: 'year', label: 'This Year' }
      ].map(range => (
        <button
          key={range.id}
          onClick={() => setTimeRange(range.id)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
            timeRange === range.id 
              ? 'bg-white text-blue-600 shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          {range.label}
        </button>
      ))}
    </div>
  );

  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Activity className="w-8 h-8 text-gray-400 mx-auto mb-2 animate-pulse" />
          <div className="text-gray-600">Loading your learning analytics...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Your Learning Analytics</h1>
        <p className="text-gray-600">Insights to help you succeed in your academic journey</p>
      </div>

      <TimeRangeSelector />

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Current GPA"
          value={analyticsData.performance.current_gpa}
          trend={analyticsData.performance.gpa_trend}
          icon={Award}
          color="bg-blue-500"
          subtitle="Above average"
        />
        <MetricCard
          title="Study Hours/Week"
          value={`${analyticsData.engagement.study_hours_per_week}h`}
          trend={0}
          icon={Clock}
          color="bg-green-500"
          subtitle="Consistent pace"
        />
        <MetricCard
          title="Attendance Rate"
          value={`${analyticsData.engagement.attendance_rate}%`}
          trend={2}
          icon={CheckCircle}
          color="bg-purple-500"
          subtitle="Excellent"
        />
        <MetricCard
          title="Assignment Rate"
          value={`${analyticsData.engagement.assignment_submission_rate}%`}
          trend={1}
          icon={Target}
          color="bg-orange-500"
          subtitle="On time"
        />
      </div>

      {/* Progress Chart */}
      <ProgressChart />

      {/* Learning Patterns */}
      <LearningPatterns />

      {/* Strengths and Improvements */}
      <StrengthsAndImprovements />

      {/* Smart Recommendations */}
      <SmartRecommendations />
    </div>
  );
};

export default LearningAnalyticsDashboard;
