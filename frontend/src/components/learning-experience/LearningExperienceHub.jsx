import React, { useState, useEffect } from 'react';
import { 
  BookOpen, 
  Target, 
  TrendingUp, 
  Award, 
  Calendar, 
  MapPin,
  CheckCircle,
  Clock,
  AlertCircle,
  Lightbulb,
  Users,
  Star,
  ArrowRight,
  Play,
  Pause,
  RotateCcw,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import LearningJourneyDashboard from './LearningJourneyDashboard';
import IntuitiveCourseEnrollment from './IntuitiveCourseEnrollment';
import AcademicPlanningWizard from './AcademicPlanningWizard';

const LearningExperienceHub = ({ student }) => {
  const [activeView, setActiveView] = useState('dashboard');
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [learningProgress, setLearningProgress] = useState(null);
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    // Check if student needs onboarding
    if (!student.academic_plan_completed) {
      setShowOnboarding(true);
    }
    
    // Load learning progress
    fetchLearningProgress();
    fetchNotifications();
  }, [student.id]);

  const fetchLearningProgress = async () => {
    try {
      const response = await fetch(`/api/students/${student.id}/learning-progress/`);
      const data = await response.json();
      setLearningProgress(data);
    } catch (error) {
      console.error('Error fetching learning progress:', error);
    }
  };

  const fetchNotifications = async () => {
    try {
      const response = await fetch(`/api/students/${student.id}/notifications/`);
      const data = await response.json();
      setNotifications(data.results || []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  const LearningProgressWidget = () => (
    <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white mb-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-bold">Your Learning Journey</h2>
          <p className="text-blue-100">Keep up the great progress!</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold">{learningProgress?.completion_percentage || 37}%</div>
          <div className="text-sm text-blue-100">Complete</div>
        </div>
      </div>
      
      <div className="w-full bg-white bg-opacity-20 rounded-full h-2 mb-4">
        <div 
          className="bg-white h-2 rounded-full transition-all duration-300"
          style={{ width: `${learningProgress?.completion_percentage || 37}%` }}
        ></div>
      </div>
      
      <div className="grid grid-cols-3 gap-4 text-center">
        <div>
          <div className="text-lg font-semibold">{learningProgress?.credits_earned || 45}</div>
          <div className="text-xs text-blue-100">Credits Earned</div>
        </div>
        <div>
          <div className="text-lg font-semibold">{learningProgress?.courses_completed || 15}</div>
          <div className="text-xs text-blue-100">Courses Done</div>
        </div>
        <div>
          <div className="text-lg font-semibold">{learningProgress?.gpa || '3.7'}</div>
          <div className="text-xs text-blue-100">Current GPA</div>
        </div>
      </div>
    </div>
  );

  const SmartNotifications = () => {
    const [expanded, setExpanded] = useState(false);
    
    const mockNotifications = [
      {
        id: 1,
        type: 'deadline',
        title: 'Registration Deadline Approaching',
        message: 'Spring 2025 registration closes in 3 days',
        action: 'Register Now',
        priority: 'high',
        icon: AlertCircle,
        color: 'text-red-600'
      },
      {
        id: 2,
        type: 'recommendation',
        title: 'Course Recommendation',
        message: 'CS301 Data Structures fits your learning path',
        action: 'View Course',
        priority: 'medium',
        icon: Lightbulb,
        color: 'text-blue-600'
      },
      {
        id: 3,
        type: 'achievement',
        title: 'Milestone Reached!',
        message: 'You\'ve completed 50% of your degree requirements',
        action: 'View Progress',
        priority: 'low',
        icon: Award,
        color: 'text-green-600'
      }
    ];

    const displayNotifications = expanded ? mockNotifications : mockNotifications.slice(0, 2);

    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Smart Notifications</h3>
          <button 
            onClick={() => setExpanded(!expanded)}
            className="text-gray-500 hover:text-gray-700"
          >
            {expanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
          </button>
        </div>
        
        <div className="space-y-3">
          {displayNotifications.map(notification => (
            <div key={notification.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <notification.icon className={`w-5 h-5 mt-1 ${notification.color}`} />
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{notification.title}</h4>
                <p className="text-sm text-gray-600">{notification.message}</p>
              </div>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                {notification.action}
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const QuickActions = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <button 
        onClick={() => setActiveView('courses')}
        className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 text-left hover:shadow-md transition-shadow group"
      >
        <div className="flex items-center justify-between mb-3">
          <BookOpen className="w-8 h-8 text-blue-600" />
          <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
        </div>
        <h3 className="font-semibold text-gray-900 mb-1">Browse Courses</h3>
        <p className="text-sm text-gray-600">Discover new learning opportunities</p>
      </button>

      <button 
        onClick={() => setActiveView('planning')}
        className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 text-left hover:shadow-md transition-shadow group"
      >
        <div className="flex items-center justify-between mb-3">
          <Target className="w-8 h-8 text-green-600" />
          <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-green-600 transition-colors" />
        </div>
        <h3 className="font-semibold text-gray-900 mb-1">Plan Semester</h3>
        <p className="text-sm text-gray-600">Create your academic roadmap</p>
      </button>

      <button className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 text-left hover:shadow-md transition-shadow group">
        <div className="flex items-center justify-between mb-3">
          <Calendar className="w-8 h-8 text-purple-600" />
          <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-purple-600 transition-colors" />
        </div>
        <h3 className="font-semibold text-gray-900 mb-1">View Schedule</h3>
        <p className="text-sm text-gray-600">Check your class timetable</p>
      </button>

      <button className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 text-left hover:shadow-md transition-shadow group">
        <div className="flex items-center justify-between mb-3">
          <TrendingUp className="w-8 h-8 text-orange-600" />
          <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-orange-600 transition-colors" />
        </div>
        <h3 className="font-semibold text-gray-900 mb-1">Track Progress</h3>
        <p className="text-sm text-gray-600">Monitor your academic growth</p>
      </button>
    </div>
  );

  const LearningInsights = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Learning Insights</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-blue-900">Study Time</span>
          </div>
          <div className="text-2xl font-bold text-blue-900">24h</div>
          <div className="text-sm text-blue-700">This week</div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Star className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-900">Performance</span>
          </div>
          <div className="text-2xl font-bold text-green-900">87%</div>
          <div className="text-sm text-green-700">Average score</div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="w-5 h-5 text-purple-600" />
            <span className="font-medium text-purple-900">Collaboration</span>
          </div>
          <div className="text-2xl font-bold text-purple-900">5</div>
          <div className="text-sm text-purple-700">Study groups joined</div>
        </div>
      </div>
    </div>
  );

  const NavigationTabs = () => (
    <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6">
      {[
        { id: 'dashboard', label: 'Dashboard', icon: Target },
        { id: 'courses', label: 'Courses', icon: BookOpen },
        { id: 'planning', label: 'Planning', icon: Calendar }
      ].map(tab => (
        <button
          key={tab.id}
          onClick={() => setActiveView(tab.id)}
          className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
            activeView === tab.id 
              ? 'bg-white text-blue-600 shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <tab.icon className="w-4 h-4" />
          <span className="font-medium">{tab.label}</span>
        </button>
      ))}
    </div>
  );

  const handlePlanningComplete = (planData) => {
    console.log('Academic plan completed:', planData);
    setShowOnboarding(false);
    setActiveView('dashboard');
    // Save plan to backend
  };

  const renderActiveView = () => {
    switch (activeView) {
      case 'courses':
        return <IntuitiveCourseEnrollment student={student} />;
      case 'planning':
        return <AcademicPlanningWizard student={student} onComplete={handlePlanningComplete} />;
      case 'dashboard':
      default:
        return (
          <div>
            <LearningProgressWidget />
            <SmartNotifications />
            <QuickActions />
            <LearningInsights />
            <LearningJourneyDashboard student={student} />
          </div>
        );
    }
  };

  // Show onboarding wizard for new students
  if (showOnboarding) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow-sm border-b border-gray-200 p-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold text-gray-900">Welcome to Your Learning Journey!</h1>
            <p className="text-gray-600">Let's create a personalized academic plan just for you.</p>
          </div>
        </div>
        <AcademicPlanningWizard student={student} onComplete={handlePlanningComplete} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {student.first_name}! 👋
              </h1>
              <p className="text-gray-600">Ready to continue your learning adventure?</p>
            </div>
            <div className="flex items-center space-x-4">
              <button 
                onClick={() => setShowOnboarding(true)}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Update Plan
              </button>
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                {student.first_name?.[0]}{student.last_name?.[0]}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-6">
        <NavigationTabs />
        {renderActiveView()}
      </div>

      {/* Floating Help Button */}
      <button className="fixed bottom-6 right-6 w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
        <Lightbulb className="w-6 h-6" />
      </button>
    </div>
  );
};

export default LearningExperienceHub;
