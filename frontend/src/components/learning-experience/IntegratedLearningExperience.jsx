import React, { useState, useEffect } from 'react';
import { I18nProvider, useI18n } from '../../i18n/i18nContext';
import ResponsiveLearningExperience from './ResponsiveLearningExperience';
import { learningJourneyAPI, learningAnalyticsAPI, studentDataAPI } from '../../services/learningExperienceAPI';
import '../../styles/rtl.css';

const IntegratedLearningExperience = ({ studentId, initialLanguage = 'en' }) => {
  const [student, setStudent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadStudentData();
  }, [studentId]);

  const loadStudentData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load student profile with academic context
      const studentProfile = await studentDataAPI.getStudentProfile(studentId);
      
      // Load learning progress
      const learningProgress = await learningJourneyAPI.getLearningProgress(studentId);
      
      // Load current enrollments
      const currentEnrollments = await studentDataAPI.getCurrentEnrollments(studentId);
      
      // Load academic records
      const academicRecords = await studentDataAPI.getAcademicRecords(studentId);

      // Combine all data
      const enrichedStudent = {
        ...studentProfile,
        learning_progress: learningProgress,
        current_enrollments: currentEnrollments.results || [],
        academic_records: academicRecords,
        academic_plan_completed: studentProfile.learning_profile?.academic_plan_data ? 
          Object.keys(studentProfile.learning_profile.academic_plan_data).length > 0 : false
      };

      setStudent(enrichedStudent);

      // Track analytics
      await learningAnalyticsAPI.trackActivity(studentId, {
        activity_type: 'learning_dashboard_view',
        timestamp: new Date().toISOString(),
        device_type: window.innerWidth < 768 ? 'mobile' : 'desktop'
      });

    } catch (err) {
      console.error('Error loading student data:', err);
      setError(err.message || 'Failed to load student data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingScreen />;
  }

  if (error) {
    return <ErrorScreen error={error} onRetry={loadStudentData} />;
  }

  if (!student) {
    return <ErrorScreen error="Student not found" onRetry={loadStudentData} />;
  }

  return (
    <I18nProvider>
      <LearningExperienceWrapper student={student} onDataUpdate={loadStudentData} />
    </I18nProvider>
  );
};

const LearningExperienceWrapper = ({ student, onDataUpdate }) => {
  const { t, currentLanguage } = useI18n();

  // Track language changes for analytics
  useEffect(() => {
    if (student?.id) {
      learningAnalyticsAPI.trackActivity(student.id, {
        activity_type: 'language_change',
        activity_data: { language: currentLanguage },
        timestamp: new Date().toISOString()
      });
    }
  }, [currentLanguage, student?.id]);

  return (
    <div className="learning-experience-container">
      <ResponsiveLearningExperience 
        student={student} 
        onDataUpdate={onDataUpdate}
      />
    </div>
  );
};

const LoadingScreen = () => {
  const { t, isRTL, getDirectionClass } = useI18n();

  return (
    <div className={`min-h-screen bg-gray-50 flex items-center justify-center ${getDirectionClass()}`}>
      <div className="text-center p-6 max-w-md">
        {/* Animated loading icon */}
        <div className="relative mb-6">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 bg-blue-600 rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* Loading text */}
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {t('common.loading')}
        </h2>
        <p className="text-gray-600 mb-4">
          {t('learningJourney.subtitle')}
        </p>

        {/* Progress indicators */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>{t('common.loadingProfile')}</span>
            <span>✓</span>
          </div>
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>{t('common.loadingProgress')}</span>
            <div className="animate-spin w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full"></div>
          </div>
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>{t('common.loadingRecommendations')}</span>
            <span>⏳</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const ErrorScreen = ({ error, onRetry }) => {
  const { t, getDirectionClass } = useI18n();

  return (
    <div className={`min-h-screen bg-gray-50 flex items-center justify-center p-6 ${getDirectionClass()}`}>
      <div className="text-center max-w-md">
        {/* Error icon */}
        <div className="text-red-500 text-6xl mb-4">⚠️</div>
        
        {/* Error message */}
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {t('common.error')}
        </h2>
        <p className="text-gray-600 mb-6">
          {error || t('common.genericError')}
        </p>

        {/* Action buttons */}
        <div className="space-y-3">
          <button 
            onClick={onRetry}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            {t('common.retry')}
          </button>
          
          <button 
            onClick={() => window.location.href = '/dashboard'}
            className="w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {t('common.backToDashboard')}
          </button>
        </div>

        {/* Help text */}
        <p className="text-sm text-gray-500 mt-4">
          {t('common.persistentError')} 
          <a href="/support" className="text-blue-600 hover:text-blue-800 ml-1">
            {t('common.contactSupport')}
          </a>
        </p>
      </div>
    </div>
  );
};

// Example usage component
export const LearningExperienceDemo = () => {
  // Mock student ID - in real app this would come from auth context
  const studentId = 1;

  return (
    <div className="learning-experience-demo">
      <IntegratedLearningExperience 
        studentId={studentId}
        initialLanguage="en"
      />
    </div>
  );
};

// HOC for adding learning experience to existing components
export const withLearningExperience = (WrappedComponent) => {
  return function LearningExperienceComponent(props) {
    const [learningData, setLearningData] = useState(null);

    useEffect(() => {
      if (props.student?.id) {
        loadLearningData(props.student.id);
      }
    }, [props.student?.id]);

    const loadLearningData = async (studentId) => {
      try {
        const [journey, analytics, recommendations] = await Promise.all([
          learningJourneyAPI.getLearningJourney(studentId),
          learningAnalyticsAPI.getAnalytics(studentId),
          learningJourneyAPI.getRecommendations(studentId)
        ]);

        setLearningData({
          journey,
          analytics,
          recommendations
        });
      } catch (error) {
        console.error('Error loading learning data:', error);
      }
    };

    return (
      <WrappedComponent 
        {...props} 
        learningData={learningData}
        onLearningDataUpdate={() => loadLearningData(props.student?.id)}
      />
    );
  };
};

// Context provider for learning experience
export const LearningExperienceContext = React.createContext();

export const LearningExperienceProvider = ({ children, studentId }) => {
  const [learningState, setLearningState] = useState({
    student: null,
    journey: null,
    analytics: null,
    recommendations: [],
    loading: true,
    error: null
  });

  const updateLearningState = (updates) => {
    setLearningState(prev => ({ ...prev, ...updates }));
  };

  const trackActivity = async (activityType, activityData = {}) => {
    if (studentId) {
      try {
        await learningAnalyticsAPI.trackActivity(studentId, {
          activity_type: activityType,
          activity_data: activityData,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error tracking activity:', error);
      }
    }
  };

  const contextValue = {
    ...learningState,
    updateLearningState,
    trackActivity,
    refreshData: () => {
      if (studentId) {
        // Reload all learning data
        loadStudentData();
      }
    }
  };

  return (
    <LearningExperienceContext.Provider value={contextValue}>
      {children}
    </LearningExperienceContext.Provider>
  );
};

// Hook for using learning experience context
export const useLearningExperience = () => {
  const context = React.useContext(LearningExperienceContext);
  if (!context) {
    throw new Error('useLearningExperience must be used within a LearningExperienceProvider');
  }
  return context;
};

export default IntegratedLearningExperience;
