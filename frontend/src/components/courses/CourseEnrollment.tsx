import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { addNotification } from '../../store/slices/uiSlice';
import { 
  BookOpen, 
  Users, 
  Clock, 
  MapPin, 
  User, 
  Calendar,
  CheckCircle,
  AlertCircle,
  XCircle,
  Star,
  Search,
  Filter
} from 'lucide-react';
import { apiService } from '../../services/api';
import { transformCoursesArray, type StandardizedCourse, filterCoursesByStatus, getCourseStatusCounts } from '../../utils/courseDataTransformer';

// Use the standardized course interface
type Course = StandardizedCourse;

interface CourseEnrollmentProps {
  studentId?: string;
  semester?: string;
}

const CourseEnrollment: React.FC<CourseEnrollmentProps> = ({ 
  studentId, 
  semester = 'spring_2025' 
}) => {
  const { t } = useTranslation();
  const { user, token } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [enrolling, setEnrolling] = useState<string | null>(null);

  useEffect(() => {
    fetchCourses();
  }, [semester]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      setError(null);

      if (token) {
        apiService.setToken(token);
        try {
          const response = await apiService.getCourses({ semester });
          const coursesData = response.results || response || [];

          // Use standardized course data transformer
          const transformedCourses = transformCoursesArray(coursesData);
          setCourses(transformedCourses);
          return;
        } catch (apiError) {
          console.warn('API call failed:', apiError);
        }
      }

      // Set empty courses array when API fails
      setCourses([]);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch courses');
    } finally {
      setLoading(false);
    }
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch =
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.instructorName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDepartment = departmentFilter === 'all' || course.departmentName === departmentFilter;
    const matchesStatus = statusFilter === 'all' || course.enrollmentStatus === statusFilter;

    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'waitlist':
        return 'bg-yellow-100 text-yellow-800';
      case 'closed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEnrollmentStatusColor = (status: string) => {
    switch (status) {
      case 'enrolled':
        return 'bg-green-100 text-green-800';
      case 'waitlisted':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'retakeable':
        return 'bg-orange-100 text-orange-800';
      case 'dropped':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'waitlist':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'closed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const handleEnroll = async (courseId: string) => {
    try {
      setEnrolling(courseId);

      if (token) {
        apiService.setToken(token);
        const response = await apiService.enrollInCourse(courseId);

        // Refresh courses data from backend to get updated enrollment status
        await fetchCourses();

        dispatch(addNotification({
          type: 'success',
          message: response.message || t('courses.enrollSuccess', 'Successfully enrolled in course')
        }));
      }

    } catch (err: any) {
      dispatch(addNotification({
        type: 'error',
        message: err.response?.data?.error || t('courses.enrollError', 'Failed to enroll in course')
      }));
    } finally {
      setEnrolling(null);
    }
  };

  const handleUnenroll = async (courseId: string) => {
    if (!confirm(t('courses.confirmUnenroll', 'Are you sure you want to unenroll from this course?'))) {
      return;
    }

    try {
      setEnrolling(courseId);
      
      if (token) {
        apiService.setToken(token);
        await apiService.unenrollFromCourse(courseId);
      }

      // Update local state
      setCourses(prev => prev.map(course => 
        course.id === courseId 
          ? { 
              ...course, 
              enrollmentStatus: 'not_enrolled',
              enrolledStudents: Math.max(0, course.enrolledStudents - 1)
            }
          : course
      ));

      dispatch(addNotification({
        type: 'success',
        message: t('courses.unenrollSuccess', 'Successfully unenrolled from course')
      }));
    } catch (err) {
      dispatch(addNotification({
        type: 'error',
        message: t('courses.unenrollError', 'Failed to unenroll from course')
      }));
    } finally {
      setEnrolling(null);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
      />
    ));
  };

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty <= 2) return 'text-green-600';
    if (difficulty <= 3) return 'text-yellow-600';
    if (difficulty <= 4) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="glass-card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={t('courses.searchPlaceholder', 'Search courses...')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full"
            />
          </div>
          <div>
            <select
              value={departmentFilter}
              onChange={(e) => setDepartmentFilter(e.target.value)}
              className="w-full py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">{t('courses.allDepartments', 'All Departments')}</option>
              <option value="Computer Science">{t('courses.computerScience', 'Computer Science')}</option>
              <option value="Mathematics">{t('courses.mathematics', 'Mathematics')}</option>
              <option value="Physics">{t('courses.physics', 'Physics')}</option>
              <option value="English">{t('courses.english', 'English')}</option>
            </select>
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">{t('courses.allStatuses', 'All Statuses')}</option>
              <option value="open">{t('courses.open', 'Open')}</option>
              <option value="waitlist">{t('courses.waitlist', 'Waitlist')}</option>
              <option value="closed">{t('courses.closed', 'Closed')}</option>
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              {filteredCourses.length} {t('courses.coursesFound', 'courses found')}
            </span>
          </div>
        </div>
      </div>

      {/* Courses List */}
      <div className="space-y-4">
        {filteredCourses.map((course) => (
          <div key={course.id} className="glass-card hover:scale-[1.01] transition-transform duration-200">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {course.code} - {course.title}
                      </h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${course.statusColor}`}>
                        {course.statusIcon} {t(`courses.${course.enrollmentStatus}`, course.enrollmentStatus)}
                      </span>
                      {course.isRetake && (
                        <span className="text-xs px-2 py-1 rounded-full bg-orange-100 text-orange-800">
                          {t('courses.retake', 'Retake')} ({course.currentAttempt})
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600 mb-3">{course.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(course.status)}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <User className="w-4 h-4" />
                    <span>{course.instructorName}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <BookOpen className="w-4 h-4" />
                    <span>{course.creditHours} {t('courses.credits', 'credits')}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Users className="w-4 h-4" />
                    <span>{course.enrolledStudents}/{course.maxStudents} {t('courses.enrolled', 'enrolled')}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>{course.schedule.days.join(', ')}</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>{course.schedule.startTime} - {course.schedule.endTime}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <MapPin className="w-4 h-4" />
                    <span>{course.schedule.room}, {course.schedule.building}</span>
                  </div>
                </div>

                {course.prerequisites.length > 0 && (
                  <div className="mb-4">
                    <span className="text-sm font-medium text-gray-700">
                      {t('courses.prerequisites', 'Prerequisites')}:
                    </span>
                    <span className="text-sm text-gray-600 ml-2">
                      {course.prerequisites.join(', ')}
                    </span>
                  </div>
                )}

                {(course.rating || course.difficulty) && (
                  <div className="flex items-center space-x-6 mb-4">
                    {course.rating && (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">{t('courses.rating', 'Rating')}:</span>
                        <div className="flex items-center space-x-1">
                          {renderStars(course.rating)}
                          <span className="text-sm text-gray-600 ml-1">({course.rating})</span>
                        </div>
                      </div>
                    )}
                    {course.difficulty && (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">{t('courses.difficulty', 'Difficulty')}:</span>
                        <span className={`text-sm font-medium ${getDifficultyColor(course.difficulty)}`}>
                          {course.difficulty}/5
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {course.status === 'waitlist' && course.waitlistCount > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                    <p className="text-sm text-yellow-800">
                      {t('courses.waitlistInfo', 'This course has a waitlist with {{count}} students waiting', {
                        count: course.waitlistCount
                      })}
                    </p>
                  </div>
                )}
              </div>

              <div className="ml-6 flex flex-col space-y-2">
                {course.enrollmentStatus === 'enrolled' ? (
                  <button
                    onClick={() => handleUnenroll(course.id)}
                    disabled={enrolling === course.id}
                    className="btn-secondary text-sm"
                  >
                    {enrolling === course.id ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600"></div>
                        <span>{t('courses.processing', 'Processing...')}</span>
                      </div>
                    ) : (
                      t('courses.unenroll', 'Unenroll')
                    )}
                  </button>
                ) : course.enrollmentStatus === 'waitlisted' ? (
                  <button
                    onClick={() => handleUnenroll(course.id)}
                    disabled={enrolling === course.id}
                    className="btn-secondary text-sm"
                  >
                    {enrolling === course.id ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600"></div>
                        <span>{t('courses.processing', 'Processing...')}</span>
                      </div>
                    ) : (
                      t('courses.leaveWaitlist', 'Leave Waitlist')
                    )}
                  </button>
                ) : course.canEnroll ? (
                  <button
                    onClick={() => handleEnroll(course.id)}
                    disabled={enrolling === course.id}
                    className={`text-sm ${course.actionButtonColor}`}
                  >
                    {enrolling === course.id ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                        <span>{t('courses.processing', 'Processing...')}</span>
                      </div>
                    ) : (
                      course.actionButtonText
                    )}
                  </button>
                ) : (
                  <div className="text-sm text-gray-500 px-3 py-2">
                    <span className="block">{t('courses.notAvailable', 'Not Available')}</span>
                    {course.enrollmentMessage && (
                      <span className="block text-xs text-red-500 mt-1">
                        {course.enrollmentMessage}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredCourses.length === 0 && (
        <div className="glass-card text-center py-12">
          <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('courses.noCourses', 'No courses found')}
          </h3>
          <p className="text-gray-600">
            {t('courses.noCoursesDescription', 'No courses match your current filters')}
          </p>
        </div>
      )}

      {error && (
        <div className="glass-card bg-red-50 border border-red-200 text-center py-12">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-red-900 mb-2">
            {t('common.error', 'Error')}
          </h3>
          <p className="text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchCourses}
            className="btn-primary"
          >
            {t('common.retry', 'Retry')}
          </button>
        </div>
      )}
    </div>
  );
};

export default CourseEnrollment;
