import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { apiService } from '../../services/api';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RotateCcw,
  BookOpen,
  Calendar,
  Award
} from 'lucide-react';

interface EnrollmentAttempt {
  id: number;
  attempt_number: number;
  is_retake: boolean;
  attempt_display: string;
  status: string;
  final_grade: number | null;
  letter_grade: string | null;
  enrolled_at: string;
  completed_at: string | null;
  is_active: boolean;
}

interface CourseHistory {
  course: {
    id: number;
    code: string;
    title: string;
    credit_hours: number;
  };
  attempts: EnrollmentAttempt[];
}

interface EnrollmentHistoryData {
  student: {
    id: number;
    name: string;
    student_id: string;
  };
  course_history: Record<string, CourseHistory>;
}

const EnrollmentHistory: React.FC = () => {
  const { t } = useTranslation();
  const { token } = useAppSelector((state) => state.auth);
  const [historyData, setHistoryData] = useState<EnrollmentHistoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchEnrollmentHistory();
  }, []);

  const fetchEnrollmentHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (token) {
        apiService.setToken(token);
        const data = await apiService.request('/courses/enrollments/history/');
        setHistoryData(data);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch enrollment history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'enrolled':
        return <BookOpen className="w-4 h-4 text-blue-600" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'dropped':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'enrolled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'dropped':
        return 'bg-red-100 text-red-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getGradeColor = (grade: string | null) => {
    if (!grade) return 'text-gray-500';
    
    const gradeValue = grade.charAt(0);
    switch (gradeValue) {
      case 'A':
        return 'text-green-600 font-semibold';
      case 'B':
        return 'text-blue-600 font-semibold';
      case 'C':
        return 'text-yellow-600 font-semibold';
      case 'D':
        return 'text-orange-600 font-semibold';
      case 'F':
        return 'text-red-600 font-semibold';
      default:
        return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="glass-card">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">{t('common.loading', 'Loading...')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="glass-card">
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchEnrollmentHistory}
            className="btn-primary"
          >
            {t('common.retry', 'Retry')}
          </button>
        </div>
      </div>
    );
  }

  if (!historyData || Object.keys(historyData.course_history).length === 0) {
    return (
      <div className="glass-card">
        <div className="text-center py-8">
          <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{t('enrollment.noHistory', 'No enrollment history found')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glass-card">
        <div className="flex items-center space-x-3 mb-4">
          <BookOpen className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">
            {t('enrollment.history', 'Enrollment History')}
          </h2>
        </div>
        <div className="text-sm text-gray-600">
          <p><strong>{t('student.name', 'Student')}:</strong> {historyData.student.name}</p>
          <p><strong>{t('student.id', 'Student ID')}:</strong> {historyData.student.student_id}</p>
        </div>
      </div>

      {/* Course History */}
      <div className="space-y-4">
        {Object.values(historyData.course_history).map((courseHistory) => (
          <div key={courseHistory.course.code} className="glass-card">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {courseHistory.course.code} - {courseHistory.course.title}
                </h3>
                <p className="text-sm text-gray-600">
                  {courseHistory.course.credit_hours} {t('course.creditHours', 'Credit Hours')}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">
                  {courseHistory.attempts.length} {t('enrollment.attempts', 'Attempts')}
                </span>
                {courseHistory.attempts.length > 1 && (
                  <RotateCcw className="w-4 h-4 text-orange-500" />
                )}
              </div>
            </div>

            {/* Attempts Timeline */}
            <div className="space-y-3">
              {courseHistory.attempts.map((attempt, index) => (
                <div key={attempt.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(attempt.status)}
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(attempt.status)}`}>
                      {t(`enrollment.status.${attempt.status}`, attempt.status)}
                    </span>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm font-medium">
                        {attempt.attempt_display}
                      </span>
                      {attempt.letter_grade && (
                        <span className={`text-sm font-bold ${getGradeColor(attempt.letter_grade)}`}>
                          {attempt.letter_grade}
                        </span>
                      )}
                      {attempt.final_grade && (
                        <span className="text-sm text-gray-600">
                          ({attempt.final_grade.toFixed(1)}%)
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                      <span className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{new Date(attempt.enrolled_at).toLocaleDateString()}</span>
                      </span>
                      {attempt.completed_at && (
                        <span className="flex items-center space-x-1">
                          <Award className="w-3 h-3" />
                          <span>{new Date(attempt.completed_at).toLocaleDateString()}</span>
                        </span>
                      )}
                      {attempt.is_active && (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          {t('enrollment.current', 'Current')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EnrollmentHistory;
