/* RTL (Right-to-Left) Support for Arabic Language */

/* Base RTL styles */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Typography adjustments for Arabic */
.rtl {
  font-family: 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  line-height: 1.6;
}

.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
  font-weight: 600;
  line-height: 1.4;
}

/* Flex direction adjustments */
.rtl .flex-row {
  flex-direction: row-reverse;
}

.rtl .flex-row-reverse {
  flex-direction: row;
}

/* Spacing adjustments */
.rtl .ml-1 { margin-left: 0; margin-right: 0.25rem; }
.rtl .ml-2 { margin-left: 0; margin-right: 0.5rem; }
.rtl .ml-3 { margin-left: 0; margin-right: 0.75rem; }
.rtl .ml-4 { margin-left: 0; margin-right: 1rem; }
.rtl .ml-5 { margin-left: 0; margin-right: 1.25rem; }
.rtl .ml-6 { margin-left: 0; margin-right: 1.5rem; }
.rtl .ml-8 { margin-left: 0; margin-right: 2rem; }

.rtl .mr-1 { margin-right: 0; margin-left: 0.25rem; }
.rtl .mr-2 { margin-right: 0; margin-left: 0.5rem; }
.rtl .mr-3 { margin-right: 0; margin-left: 0.75rem; }
.rtl .mr-4 { margin-right: 0; margin-left: 1rem; }
.rtl .mr-5 { margin-right: 0; margin-left: 1.25rem; }
.rtl .mr-6 { margin-right: 0; margin-left: 1.5rem; }
.rtl .mr-8 { margin-right: 0; margin-left: 2rem; }

.rtl .pl-1 { padding-left: 0; padding-right: 0.25rem; }
.rtl .pl-2 { padding-left: 0; padding-right: 0.5rem; }
.rtl .pl-3 { padding-left: 0; padding-right: 0.75rem; }
.rtl .pl-4 { padding-left: 0; padding-right: 1rem; }
.rtl .pl-5 { padding-left: 0; padding-right: 1.25rem; }
.rtl .pl-6 { padding-left: 0; padding-right: 1.5rem; }
.rtl .pl-8 { padding-left: 0; padding-right: 2rem; }

.rtl .pr-1 { padding-right: 0; padding-left: 0.25rem; }
.rtl .pr-2 { padding-right: 0; padding-left: 0.5rem; }
.rtl .pr-3 { padding-right: 0; padding-left: 0.75rem; }
.rtl .pr-4 { padding-right: 0; padding-left: 1rem; }
.rtl .pr-5 { padding-right: 0; padding-left: 1.25rem; }
.rtl .pr-6 { padding-right: 0; padding-left: 1.5rem; }
.rtl .pr-8 { padding-right: 0; padding-left: 2rem; }

/* Border radius adjustments */
.rtl .rounded-l-lg { border-radius: 0 0.5rem 0.5rem 0; }
.rtl .rounded-r-lg { border-radius: 0.5rem 0 0 0.5rem; }
.rtl .rounded-tl-lg { border-top-left-radius: 0; border-top-right-radius: 0.5rem; }
.rtl .rounded-tr-lg { border-top-right-radius: 0; border-top-left-radius: 0.5rem; }
.rtl .rounded-bl-lg { border-bottom-left-radius: 0; border-bottom-right-radius: 0.5rem; }
.rtl .rounded-br-lg { border-bottom-right-radius: 0; border-bottom-left-radius: 0.5rem; }

/* Text alignment */
.rtl .text-left { text-align: right; }
.rtl .text-right { text-align: left; }

/* Float adjustments */
.rtl .float-left { float: right; }
.rtl .float-right { float: left; }

/* Position adjustments */
.rtl .left-0 { left: auto; right: 0; }
.rtl .left-1 { left: auto; right: 0.25rem; }
.rtl .left-2 { left: auto; right: 0.5rem; }
.rtl .left-4 { left: auto; right: 1rem; }
.rtl .left-6 { left: auto; right: 1.5rem; }

.rtl .right-0 { right: auto; left: 0; }
.rtl .right-1 { right: auto; left: 0.25rem; }
.rtl .right-2 { right: auto; left: 0.5rem; }
.rtl .right-4 { right: auto; left: 1rem; }
.rtl .right-6 { right: auto; left: 1.5rem; }

/* Transform adjustments for icons */
.rtl .transform-flip {
  transform: scaleX(-1);
}

/* Specific component adjustments */

/* Navigation */
.rtl .nav-item {
  margin-left: 0;
  margin-right: 1rem;
}

.rtl .nav-item:last-child {
  margin-right: 0;
}

/* Breadcrumbs */
.rtl .breadcrumb-separator::before {
  content: "\\";
  transform: scaleX(-1);
}

/* Dropdowns */
.rtl .dropdown-menu {
  left: auto;
  right: 0;
}

/* Modals */
.rtl .modal-close {
  left: 1rem;
  right: auto;
}

/* Cards */
.rtl .card-header-action {
  left: 1rem;
  right: auto;
}

/* Forms */
.rtl .form-label {
  text-align: right;
}

.rtl .form-input {
  text-align: right;
}

.rtl .form-checkbox {
  margin-left: 0.5rem;
  margin-right: 0;
}

.rtl .form-radio {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* Tables */
.rtl .table th:first-child {
  text-align: right;
}

.rtl .table td:first-child {
  text-align: right;
}

/* Buttons with icons */
.rtl .btn-icon-left {
  flex-direction: row-reverse;
}

.rtl .btn-icon-left .icon {
  margin-left: 0.5rem;
  margin-right: 0;
}

.rtl .btn-icon-right .icon {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* Learning Experience specific styles */

/* Learning Journey Dashboard */
.rtl .learning-progress-step {
  text-align: right;
}

.rtl .learning-progress-step .step-number {
  margin-left: 1rem;
  margin-right: 0;
}

.rtl .learning-progress-step .step-content {
  text-align: right;
}

/* Course Cards */
.rtl .course-card {
  text-align: right;
}

.rtl .course-card .course-meta {
  justify-content: flex-end;
}

.rtl .course-card .course-rating {
  margin-left: 0;
  margin-right: auto;
}

/* Academic Planning Wizard */
.rtl .wizard-step {
  text-align: right;
}

.rtl .wizard-navigation {
  flex-direction: row-reverse;
}

.rtl .wizard-navigation .btn-previous {
  margin-left: auto;
  margin-right: 0;
}

.rtl .wizard-navigation .btn-next {
  margin-left: 0;
  margin-right: auto;
}

/* Analytics Dashboard */
.rtl .analytics-metric {
  text-align: right;
}

.rtl .analytics-chart {
  direction: ltr; /* Keep charts in LTR for readability */
}

.rtl .analytics-legend {
  text-align: right;
}

/* Notifications */
.rtl .notification {
  text-align: right;
}

.rtl .notification .notification-icon {
  margin-left: 1rem;
  margin-right: 0;
}

.rtl .notification .notification-action {
  margin-left: 0;
  margin-right: auto;
}

/* Progress bars */
.rtl .progress-bar {
  direction: ltr; /* Keep progress direction consistent */
}

.rtl .progress-label {
  text-align: right;
}

/* Tabs */
.rtl .tab-list {
  flex-direction: row-reverse;
}

.rtl .tab-item {
  margin-left: 0.25rem;
  margin-right: 0;
}

.rtl .tab-item:first-child {
  margin-right: 0;
}

/* Tooltips */
.rtl .tooltip {
  text-align: right;
}

/* Search and filters */
.rtl .search-input {
  text-align: right;
  padding-left: 2.5rem;
  padding-right: 1rem;
}

.rtl .search-icon {
  left: auto;
  right: 0.75rem;
}

.rtl .filter-dropdown {
  left: auto;
  right: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rtl .mobile-nav {
    text-align: right;
  }
  
  .rtl .mobile-menu-item {
    text-align: right;
    padding-right: 1rem;
    padding-left: 0;
  }
}

/* Animation adjustments */
.rtl .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

.rtl .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Print styles for RTL */
@media print {
  .rtl {
    direction: rtl;
    text-align: right;
  }
  
  .rtl .print-header {
    text-align: right;
  }
  
  .rtl .print-footer {
    text-align: right;
  }
}
