/**
 * Enterprise Redux Store Types
 * 30+ Years of State Management Excellence
 * 
 * Advanced TypeScript patterns for Redux:
 * - Strict typing with branded types
 * - Type-safe action creators
 * - Immutable state patterns
 * - Advanced selector types
 * - Middleware type safety
 */

import { Action, AnyAction } from '@reduxjs/toolkit';
import {
  UserId,
  CourseId,
  EnrollmentId,
  AsyncData,
  LoadingState,
  Maybe,
  DeepReadonly,
  TypedAction,
  UserRole,
  Permission,
  SecurityContext,
} from '@/types/enterprise';

// ============================================================================
// STORE STATE INTERFACE
// ============================================================================

export interface RootState {
  readonly auth: AuthState;
  readonly ui: UIState;
  readonly courses: CoursesState;
  readonly enrollments: EnrollmentsState;
  readonly user: UserState;
  readonly cache: CacheState;
  readonly performance: PerformanceState;
}

// ============================================================================
// AUTH STATE
// ============================================================================

export interface AuthState {
  readonly isAuthenticated: boolean;
  readonly user: Maybe<AuthenticatedUser>;
  readonly tokens: Maybe<AuthTokens>;
  readonly securityContext: Maybe<SecurityContext>;
  readonly loginState: AsyncData<AuthenticatedUser>;
  readonly logoutState: LoadingState;
  readonly refreshState: LoadingState;
  readonly lastActivity: Maybe<Date>;
  readonly sessionExpiresAt: Maybe<Date>;
}

export interface AuthenticatedUser {
  readonly id: UserId;
  readonly email: string;
  readonly firstName: string;
  readonly lastName: string;
  readonly role: UserRole;
  readonly permissions: readonly Permission[];
  readonly avatar: Maybe<string>;
  readonly isActive: boolean;
  readonly lastLogin: Maybe<Date>;
  readonly profileCompleteness: number;
}

export interface AuthTokens {
  readonly accessToken: string;
  readonly refreshToken: string;
  readonly tokenType: string;
  readonly expiresIn: number;
  readonly issuedAt: Date;
}

// ============================================================================
// UI STATE
// ============================================================================

export interface UIState {
  readonly theme: 'light' | 'dark' | 'auto';
  readonly language: 'en' | 'ar';
  readonly notifications: readonly NotificationState[];
  readonly modals: Record<string, ModalState>;
  readonly loading: Record<string, boolean>;
  readonly errors: Record<string, ErrorState>;
  readonly navigation: NavigationState;
  readonly accessibility: AccessibilityState;
}

export interface NotificationState {
  readonly id: string;
  readonly type: 'success' | 'error' | 'warning' | 'info';
  readonly title: string;
  readonly message: string;
  readonly duration: Maybe<number>;
  readonly timestamp: Date;
  readonly isRead: boolean;
  readonly actions: readonly NotificationAction[];
}

export interface NotificationAction {
  readonly label: string;
  readonly action: string;
  readonly style: 'default' | 'destructive';
}

export interface ModalState {
  readonly isOpen: boolean;
  readonly data: Maybe<Record<string, unknown>>;
  readonly canDismiss: boolean;
}

export interface ErrorState {
  readonly message: string;
  readonly code: Maybe<string>;
  readonly timestamp: Date;
  readonly context: Maybe<Record<string, unknown>>;
}

export interface NavigationState {
  readonly currentRoute: string;
  readonly previousRoute: Maybe<string>;
  readonly params: Record<string, unknown>;
  readonly canGoBack: boolean;
}

export interface AccessibilityState {
  readonly isScreenReaderEnabled: boolean;
  readonly fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  readonly highContrast: boolean;
  readonly reduceMotion: boolean;
}

// ============================================================================
// COURSES STATE
// ============================================================================

export interface CoursesState {
  readonly courses: Record<string, CourseEntity>;
  readonly coursesList: AsyncData<readonly CourseId[]>;
  readonly searchResults: AsyncData<readonly CourseId[]>;
  readonly filters: CourseFilters;
  readonly selectedCourse: Maybe<CourseId>;
  readonly courseDetails: Record<string, AsyncData<CourseDetails>>;
}

export interface CourseEntity {
  readonly id: CourseId;
  readonly title: string;
  readonly titleAr: Maybe<string>;
  readonly code: string;
  readonly description: string;
  readonly descriptionAr: Maybe<string>;
  readonly department: string;
  readonly instructor: string;
  readonly level: 'undergraduate' | 'graduate';
  readonly creditHours: number;
  readonly semester: string;
  readonly year: number;
  readonly maxStudents: number;
  readonly enrolledCount: number;
  readonly waitlistedCount: number;
  readonly isActive: boolean;
  readonly isPublished: boolean;
  readonly startDate: Date;
  readonly endDate: Date;
  readonly lastUpdated: Date;
}

export interface CourseDetails extends CourseEntity {
  readonly prerequisites: readonly CourseId[];
  readonly syllabus: Maybe<string>;
  readonly schedule: readonly ScheduleEntry[];
  readonly assignments: readonly AssignmentSummary[];
  readonly announcements: readonly AnnouncementSummary[];
  readonly enrollmentStatus: EnrollmentStatus;
}

export interface ScheduleEntry {
  readonly day: string;
  readonly startTime: string;
  readonly endTime: string;
  readonly location: string;
  readonly type: 'lecture' | 'lab' | 'tutorial';
}

export interface AssignmentSummary {
  readonly id: string;
  readonly title: string;
  readonly dueDate: Date;
  readonly maxPoints: number;
  readonly isSubmitted: boolean;
}

export interface AnnouncementSummary {
  readonly id: string;
  readonly title: string;
  readonly excerpt: string;
  readonly publishedAt: Date;
  readonly isImportant: boolean;
}

export interface CourseFilters {
  readonly department: Maybe<string>;
  readonly level: Maybe<string>;
  readonly semester: Maybe<string>;
  readonly instructor: Maybe<string>;
  readonly searchQuery: string;
  readonly enrollmentStatus: Maybe<string>;
}

// ============================================================================
// ENROLLMENTS STATE
// ============================================================================

export interface EnrollmentsState {
  readonly enrollments: Record<string, EnrollmentEntity>;
  readonly userEnrollments: AsyncData<readonly EnrollmentId[]>;
  readonly enrollmentHistory: Record<string, AsyncData<readonly EnrollmentAttempt[]>>;
  readonly enrollmentActions: Record<string, LoadingState>;
}

export interface EnrollmentEntity {
  readonly id: EnrollmentId;
  readonly studentId: UserId;
  readonly courseId: CourseId;
  readonly status: EnrollmentStatus;
  readonly enrolledAt: Date;
  readonly completedAt: Maybe<Date>;
  readonly droppedAt: Maybe<Date>;
  readonly grade: Maybe<string>;
  readonly gpaPoints: Maybe<number>;
  readonly attemptNumber: number;
  readonly isRetake: boolean;
  readonly isActive: boolean;
}

export interface EnrollmentAttempt {
  readonly id: EnrollmentId;
  readonly attemptNumber: number;
  readonly status: EnrollmentStatus;
  readonly enrolledAt: Date;
  readonly completedAt: Maybe<Date>;
  readonly grade: Maybe<string>;
  readonly isActive: boolean;
}

export type EnrollmentStatus = 
  | 'enrolled' 
  | 'completed' 
  | 'failed' 
  | 'dropped' 
  | 'waitlisted'
  | 'available'
  | 'retakeable'
  | 'prerequisites_not_met';

// ============================================================================
// USER STATE
// ============================================================================

export interface UserState {
  readonly profile: AsyncData<UserProfile>;
  readonly preferences: UserPreferences;
  readonly statistics: AsyncData<UserStatistics>;
  readonly achievements: AsyncData<readonly Achievement[]>;
}

export interface UserProfile {
  readonly id: UserId;
  readonly studentId: string;
  readonly email: string;
  readonly firstName: string;
  readonly lastName: string;
  readonly avatar: Maybe<string>;
  readonly phone: Maybe<string>;
  readonly dateOfBirth: Maybe<Date>;
  readonly address: Maybe<Address>;
  readonly emergencyContact: Maybe<EmergencyContact>;
  readonly academicInfo: AcademicInfo;
}

export interface Address {
  readonly street: string;
  readonly city: string;
  readonly state: string;
  readonly zipCode: string;
  readonly country: string;
}

export interface EmergencyContact {
  readonly name: string;
  readonly relationship: string;
  readonly phone: string;
  readonly email: Maybe<string>;
}

export interface AcademicInfo {
  readonly department: string;
  readonly major: string;
  readonly year: number;
  readonly gpa: number;
  readonly totalCredits: number;
  readonly expectedGraduation: Date;
  readonly advisor: Maybe<string>;
}

export interface UserPreferences {
  readonly theme: 'light' | 'dark' | 'auto';
  readonly language: 'en' | 'ar';
  readonly notifications: NotificationPreferences;
  readonly privacy: PrivacyPreferences;
  readonly accessibility: AccessibilityPreferences;
}

export interface NotificationPreferences {
  readonly email: boolean;
  readonly push: boolean;
  readonly sms: boolean;
  readonly announcements: boolean;
  readonly grades: boolean;
  readonly deadlines: boolean;
}

export interface PrivacyPreferences {
  readonly profileVisibility: 'public' | 'private' | 'friends';
  readonly showOnlineStatus: boolean;
  readonly allowDirectMessages: boolean;
}

export interface AccessibilityPreferences {
  readonly fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  readonly highContrast: boolean;
  readonly reduceMotion: boolean;
  readonly screenReader: boolean;
}

export interface UserStatistics {
  readonly totalCourses: number;
  readonly completedCourses: number;
  readonly currentGPA: number;
  readonly totalCredits: number;
  readonly averageGrade: string;
  readonly attendanceRate: number;
  readonly assignmentSubmissionRate: number;
}

export interface Achievement {
  readonly id: string;
  readonly title: string;
  readonly description: string;
  readonly icon: string;
  readonly earnedAt: Date;
  readonly category: string;
  readonly points: number;
}

// ============================================================================
// CACHE STATE
// ============================================================================

export interface CacheState {
  readonly entries: Record<string, CacheEntry>;
  readonly metadata: CacheMetadata;
}

export interface CacheEntry {
  readonly data: unknown;
  readonly timestamp: Date;
  readonly expiresAt: Date;
  readonly accessCount: number;
  readonly lastAccessed: Date;
}

export interface CacheMetadata {
  readonly totalEntries: number;
  readonly totalSize: number;
  readonly hitRate: number;
  readonly lastCleanup: Date;
}

// ============================================================================
// PERFORMANCE STATE
// ============================================================================

export interface PerformanceState {
  readonly metrics: PerformanceMetrics;
  readonly monitoring: MonitoringState;
}

export interface PerformanceMetrics {
  readonly renderTime: number;
  readonly apiResponseTime: number;
  readonly memoryUsage: number;
  readonly networkRequests: number;
  readonly errorRate: number;
}

export interface MonitoringState {
  readonly isEnabled: boolean;
  readonly sampleRate: number;
  readonly lastReport: Maybe<Date>;
}

// ============================================================================
// ACTION TYPES
// ============================================================================

export type AppAction = 
  | AuthAction
  | UIAction
  | CoursesAction
  | EnrollmentsAction
  | UserAction
  | CacheAction
  | PerformanceAction;

export type AuthAction = TypedAction<'auth/login', { email: string; password: string }>
  | TypedAction<'auth/loginSuccess', AuthenticatedUser>
  | TypedAction<'auth/loginFailure', string>
  | TypedAction<'auth/logout'>
  | TypedAction<'auth/refreshToken'>
  | TypedAction<'auth/updateSecurityContext', SecurityContext>;

export type UIAction = TypedAction<'ui/setTheme', 'light' | 'dark' | 'auto'>
  | TypedAction<'ui/setLanguage', 'en' | 'ar'>
  | TypedAction<'ui/addNotification', Omit<NotificationState, 'id' | 'timestamp'>>
  | TypedAction<'ui/removeNotification', string>
  | TypedAction<'ui/openModal', { modalId: string; data?: Record<string, unknown> }>
  | TypedAction<'ui/closeModal', string>;

export type CoursesAction = TypedAction<'courses/fetchCoursesStart'>
  | TypedAction<'courses/fetchCoursesSuccess', readonly CourseEntity[]>
  | TypedAction<'courses/fetchCoursesFailure', string>
  | TypedAction<'courses/selectCourse', CourseId>
  | TypedAction<'courses/updateFilters', Partial<CourseFilters>>;

export type EnrollmentsAction = TypedAction<'enrollments/enrollStart', { courseId: CourseId }>
  | TypedAction<'enrollments/enrollSuccess', EnrollmentEntity>
  | TypedAction<'enrollments/enrollFailure', { courseId: CourseId; error: string }>
  | TypedAction<'enrollments/dropCourse', { enrollmentId: EnrollmentId }>;

export type UserAction = TypedAction<'user/fetchProfileStart'>
  | TypedAction<'user/fetchProfileSuccess', UserProfile>
  | TypedAction<'user/fetchProfileFailure', string>
  | TypedAction<'user/updatePreferences', Partial<UserPreferences>>;

export type CacheAction = TypedAction<'cache/set', { key: string; data: unknown; ttl: number }>
  | TypedAction<'cache/get', string>
  | TypedAction<'cache/delete', string>
  | TypedAction<'cache/clear'>;

export type PerformanceAction = TypedAction<'performance/updateMetrics', Partial<PerformanceMetrics>>
  | TypedAction<'performance/toggleMonitoring', boolean>;
