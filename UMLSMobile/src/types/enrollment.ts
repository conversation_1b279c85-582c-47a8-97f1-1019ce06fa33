// Comprehensive TypeScript interfaces for the enrollment system

export type EnrollmentStatus = 
  | 'enrolled' 
  | 'completed' 
  | 'available' 
  | 'prerequisites_not_met' 
  | 'waitlisted' 
  | 'dropped' 
  | 'failed' 
  | 'retakeable';

export interface EnrollmentDetails {
  has_enrollment_history: boolean;
  total_attempts: number;
  current_attempt?: number;
  latest_status?: EnrollmentStatus;
  latest_grade?: string;
  is_retake_eligible: boolean;
  retake_count: number;
  latest_attempt?: number;
  enrollment_date?: string;
  completed_date?: string;
  dropped_date?: string;
  is_active: boolean;
}

export interface EnrollmentAttempt {
  id: number;
  attempt_number: number;
  is_retake: boolean;
  status: EnrollmentStatus;
  final_grade?: number;
  letter_grade?: string;
  enrolled_at: string;
  completed_at?: string;
  dropped_at?: string;
  is_active: boolean;
}

export interface Course {
  id: number;
  title: string;
  code: string;
  description: string;
  department: string;
  instructor: string;
  level: 'undergraduate' | 'graduate';
  credit_hours: number;
  semester: string;
  year: number;
  max_students: number;
  enrolled_students_count: number;
  waitlisted_students_count: number;
  start_date: string;
  end_date: string;
  is_active: boolean;
  is_published: boolean;
  
  // User-specific fields (from our enhanced serializer)
  user_enrollment_status?: EnrollmentStatus;
  user_can_enroll?: boolean;
  user_enrollment_message?: string;
  user_enrollment_history?: EnrollmentAttempt[];
  user_current_attempt?: number;
  user_enrollment_details?: EnrollmentDetails;
  
  // Legacy field for backward compatibility
  enrollment_status?: EnrollmentStatus;
}

export interface EnrollmentAnalytics {
  course: {
    id: number;
    code: string;
    title: string;
    max_students: number;
  };
  enrollment_statistics: {
    total_enrollments: number;
    unique_students: number;
    current_enrolled: number;
    status_breakdown: Record<EnrollmentStatus, number>;
  };
  retake_statistics: {
    students_with_retakes: number;
    total_retakes: number;
    average_attempts: number;
  };
  grade_distribution: Record<string, number>;
  success_rate: number;
}

export interface EnrollmentRequest {
  course_id: number;
  student_id?: number;
}

export interface EnrollmentResponse {
  id: number;
  type: 'enrollment' | 'waitlist';
  message: string;
  enrollment?: {
    id: number;
    status: EnrollmentStatus;
    attempt_number: number;
    is_retake: boolean;
    enrolled_at: string;
  };
  waitlist_position?: number;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

// API endpoint response types
export type CoursesResponse = PaginatedResponse<Course>;
export type CourseDetailResponse = Course;
export type EnrollmentAnalyticsResponse = EnrollmentAnalytics;
export type EnrollmentCreateResponse = EnrollmentResponse;
