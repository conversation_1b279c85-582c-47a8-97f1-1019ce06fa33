/**
 * Enterprise TypeScript Architecture
 * 30+ Years of Software Engineering Excellence
 * 
 * Advanced type system implementing:
 * - Strict typing with branded types
 * - Advanced generics and utility types
 * - Design patterns in TypeScript
 * - Comprehensive type safety
 * - Domain-driven design types
 */

// ============================================================================
// BRANDED TYPES FOR TYPE SAFETY
// ============================================================================

/**
 * Branded type utility for creating distinct types from primitives
 */
type Brand<T, B> = T & { readonly __brand: B };

// Domain-specific branded types
export type UserId = Brand<number, 'UserId'>;
export type CourseId = Brand<number, 'CourseId'>;
export type EnrollmentId = Brand<number, 'EnrollmentId'>;
export type DepartmentId = Brand<number, 'DepartmentId'>;
export type EmailAddress = Brand<string, 'EmailAddress'>;
export type PhoneNumber = Brand<string, 'PhoneNumber'>;
export type StudentId = Brand<string, 'StudentId'>;
export type CourseCode = Brand<string, 'CourseCode'>;
export type GradePoint = Brand<number, 'GradePoint'>;
export type CreditHours = Brand<number, 'CreditHours'>;

// Utility functions for branded types
export const createUserId = (id: number): UserId => id as UserId;
export const createCourseId = (id: number): CourseId => id as CourseId;
export const createEnrollmentId = (id: number): EnrollmentId => id as EnrollmentId;
export const createEmailAddress = (email: string): EmailAddress => email as EmailAddress;
export const createStudentId = (id: string): StudentId => id as StudentId;
export const createCourseCode = (code: string): CourseCode => code as CourseCode;

// ============================================================================
// ADVANCED UTILITY TYPES
// ============================================================================

/**
 * Deep readonly utility type
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * Optional properties utility
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Required properties utility
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Nullable utility type
 */
export type Nullable<T> = T | null;

/**
 * Maybe utility type (null or undefined)
 */
export type Maybe<T> = T | null | undefined;

/**
 * Non-empty array type
 */
export type NonEmptyArray<T> = [T, ...T[]];

/**
 * Extract function parameters
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never;

/**
 * Extract function return type
 */
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any;

// ============================================================================
// RESULT AND ERROR HANDLING TYPES
// ============================================================================

/**
 * Result type for error handling without exceptions
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

export interface Success<T> {
  readonly success: true;
  readonly data: T;
}

export interface Failure<E> {
  readonly success: false;
  readonly error: E;
}

// Result utility functions
export const success = <T>(data: T): Success<T> => ({ success: true, data });
export const failure = <E>(error: E): Failure<E> => ({ success: false, error });

/**
 * Async Result type
 */
export type AsyncResult<T, E = Error> = Promise<Result<T, E>>;

// ============================================================================
// DOMAIN ENTITY TYPES
// ============================================================================

/**
 * Base entity interface
 */
export interface BaseEntity {
  readonly id: UserId | CourseId | EnrollmentId | DepartmentId;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

/**
 * Audit trail interface
 */
export interface AuditTrail {
  readonly createdBy: UserId;
  readonly updatedBy: UserId;
  readonly version: number;
}

/**
 * Soft delete interface
 */
export interface SoftDeletable {
  readonly deletedAt: Maybe<Date>;
  readonly deletedBy: Maybe<UserId>;
  readonly isDeleted: boolean;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

/**
 * Standardized API response wrapper
 */
export interface ApiResponse<T> {
  readonly success: boolean;
  readonly data: T;
  readonly message?: string;
  readonly errors?: readonly string[];
  readonly meta?: ResponseMeta;
}

/**
 * Response metadata
 */
export interface ResponseMeta {
  readonly timestamp: string;
  readonly requestId: string;
  readonly version: string;
  readonly pagination?: PaginationMeta;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  readonly page: number;
  readonly limit: number;
  readonly total: number;
  readonly totalPages: number;
  readonly hasNext: boolean;
  readonly hasPrev: boolean;
}

/**
 * Paginated response type
 */
export interface PaginatedResponse<T> extends ApiResponse<readonly T[]> {
  readonly meta: ResponseMeta & { pagination: PaginationMeta };
}

// ============================================================================
// STATE MANAGEMENT TYPES
// ============================================================================

/**
 * Loading state enumeration
 */
export const enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
}

/**
 * Async data state
 */
export interface AsyncData<T, E = Error> {
  readonly state: LoadingState;
  readonly data: Maybe<T>;
  readonly error: Maybe<E>;
  readonly lastFetch: Maybe<Date>;
}

/**
 * Redux action with type safety
 */
export interface TypedAction<T extends string, P = void> {
  readonly type: T;
  readonly payload: P;
}

/**
 * Redux reducer type
 */
export type Reducer<S, A> = (state: S, action: A) => S;

// ============================================================================
// VALIDATION TYPES
// ============================================================================

/**
 * Validation result
 */
export interface ValidationResult {
  readonly isValid: boolean;
  readonly errors: readonly ValidationError[];
}

/**
 * Validation error
 */
export interface ValidationError {
  readonly field: string;
  readonly message: string;
  readonly code: string;
}

/**
 * Validator function type
 */
export type Validator<T> = (value: T) => ValidationResult;

// ============================================================================
// PERMISSION AND SECURITY TYPES
// ============================================================================

/**
 * User roles enumeration
 */
export const enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
  FINANCE = 'finance',
}

/**
 * Permission enumeration
 */
export const enum Permission {
  READ_COURSES = 'read:courses',
  WRITE_COURSES = 'write:courses',
  READ_ENROLLMENTS = 'read:enrollments',
  WRITE_ENROLLMENTS = 'write:enrollments',
  READ_USERS = 'read:users',
  WRITE_USERS = 'write:users',
  READ_GRADES = 'read:grades',
  WRITE_GRADES = 'write:grades',
  ADMIN_ACCESS = 'admin:access',
}

/**
 * Security context
 */
export interface SecurityContext {
  readonly userId: UserId;
  readonly role: UserRole;
  readonly permissions: readonly Permission[];
  readonly sessionId: string;
  readonly expiresAt: Date;
}

// ============================================================================
// FUNCTIONAL PROGRAMMING TYPES
// ============================================================================

/**
 * Predicate function type
 */
export type Predicate<T> = (value: T) => boolean;

/**
 * Mapper function type
 */
export type Mapper<T, U> = (value: T) => U;

/**
 * Reducer function type
 */
export type ReducerFn<T, U> = (accumulator: U, current: T) => U;

/**
 * Comparator function type
 */
export type Comparator<T> = (a: T, b: T) => number;

/**
 * Option type for functional programming
 */
export type Option<T> = Some<T> | None;

export interface Some<T> {
  readonly _tag: 'Some';
  readonly value: T;
}

export interface None {
  readonly _tag: 'None';
}

// Option utility functions
export const some = <T>(value: T): Some<T> => ({ _tag: 'Some', value });
export const none: None = { _tag: 'None' };

export const isSome = <T>(option: Option<T>): option is Some<T> => option._tag === 'Some';
export const isNone = <T>(option: Option<T>): option is None => option._tag === 'None';

// ============================================================================
// ADVANCED GENERIC CONSTRAINTS
// ============================================================================

/**
 * Constraint for objects with string keys
 */
export type StringKeyed = Record<string, unknown>;

/**
 * Constraint for serializable types
 */
export type Serializable = string | number | boolean | null | SerializableObject | SerializableArray;

interface SerializableObject {
  readonly [key: string]: Serializable;
}

interface SerializableArray extends ReadonlyArray<Serializable> {}

/**
 * Deep partial type
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Exact type utility
 */
export type Exact<T, U extends T> = T & Record<Exclude<keyof U, keyof T>, never>;

// ============================================================================
// TYPE GUARDS
// ============================================================================

/**
 * Type guard for checking if value is defined
 */
export const isDefined = <T>(value: T | undefined): value is T => value !== undefined;

/**
 * Type guard for checking if value is not null
 */
export const isNotNull = <T>(value: T | null): value is T => value !== null;

/**
 * Type guard for checking if value exists (not null or undefined)
 */
export const exists = <T>(value: T | null | undefined): value is T => value != null;

/**
 * Type guard for checking if value is a string
 */
export const isString = (value: unknown): value is string => typeof value === 'string';

/**
 * Type guard for checking if value is a number
 */
export const isNumber = (value: unknown): value is number => typeof value === 'number' && !isNaN(value);

/**
 * Type guard for checking if value is an object
 */
export const isObject = (value: unknown): value is Record<string, unknown> => 
  typeof value === 'object' && value !== null && !Array.isArray(value);
