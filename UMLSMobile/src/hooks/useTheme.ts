import { useAppSelector } from '../store';
import { lightTheme, darkTheme } from '../styles/theme';
import { Theme } from '../types';

export const useTheme = () => {
  const { theme: themeMode } = useAppSelector((state) => state.ui);
  
  const theme = themeMode === 'dark' ? darkTheme : lightTheme;
  
  return {
    theme,
    currentTheme: theme,
    isDark: themeMode === 'dark',
    isLight: themeMode === 'light',
  };
};

export default useTheme;
