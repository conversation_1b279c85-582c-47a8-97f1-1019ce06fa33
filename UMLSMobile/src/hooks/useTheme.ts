import { useAppSelector } from '../store';
import { lightTheme, darkTheme } from '../styles/theme';
import { Theme } from '../types';
import { useColorScheme } from 'react-native';

export const useTheme = () => {
  const { theme: themeMode } = useAppSelector((state) => state.ui);
  const systemColorScheme = useColorScheme();

  // Determine actual theme based on mode
  const actualTheme = themeMode === 'auto'
    ? (systemColorScheme === 'dark' ? 'dark' : 'light')
    : themeMode;

  const theme = actualTheme === 'dark' ? darkTheme : lightTheme;

  return {
    theme,
    currentTheme: theme,
    themeMode, // The selected mode (light/dark/auto)
    actualTheme, // The resolved theme (light/dark)
    isDark: actualTheme === 'dark',
    isLight: actualTheme === 'light',
    isAuto: themeMode === 'auto',
  };
};

export default useTheme;
