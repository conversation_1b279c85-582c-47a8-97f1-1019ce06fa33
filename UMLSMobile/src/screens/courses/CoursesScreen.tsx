import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
  ScrollView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { useAppSelector } from '../../store';
import { lightTheme, darkTheme, createGlassStyles } from '../../styles/theme';
import { apiService } from '../../services/api';
import BackgroundGradient from '../../components/common/BackgroundGradient';
import CourseCard from '../../components/courses/CourseCard';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2; // 2 columns with margins

interface Course {
  id: number;
  title: string;
  title_ar?: string;
  code: string;
  description?: string;
  description_ar?: string;
  credit_hours: number;
  instructor: number;
  instructor_name: string;
  department: number;
  department_name: string;
  semester: 'fall' | 'spring' | 'summer';
  year: number;
  max_students: number;
  enrolled_students_count: number;
  is_active: boolean;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  prerequisites?: number[];
  prerequisites_names?: string[];
  // Enrollment status for the current user
  enrollment_status?: 'enrolled' | 'completed' | 'available' | 'prerequisites_not_met' | 'waitlisted' | 'dropped' | 'failed' | 'retakeable';
  user_enrollment?: {
    id: number;
    enrollment_date: string;
    status: string;
    grade?: string;
    is_active: boolean;
    attempt_number?: number;
    is_retake?: boolean;
    attempt_display?: string;
    letter_grade?: string;
    retake_count?: number;
    previous_attempts?: any[];
  };
}

type TabType = 'all' | 'enrolled' | 'available' | 'completed' | 'failed';

const CoursesScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const { theme, language } = useAppSelector((state) => state.ui);
  const { user } = useAppSelector((state) => state.auth);

  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('all');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Performance optimization constants
  const ITEMS_PER_PAGE = 10;
  const INITIAL_LOAD_COUNT = 20;

  const currentTheme = theme === 'dark' ? darkTheme : lightTheme;
  const glassStyles = createGlassStyles(currentTheme);
  const styles = createStyles(currentTheme);

  const fetchCourses = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) {
        setLoading(true);
        setCurrentPage(1);
        setHasNextPage(false);
      } else {
        setLoadingMore(true);
      }

      console.log(`🌐 API Request: GET /api/courses/ (page ${page}) with proper filtering`);

      // Build API parameters based on user role and profile
      const apiParams: Record<string, string> = {
        page: page.toString(),
        page_size: ITEMS_PER_PAGE.toString(),
      };

      if (user?.role === 'student') {
        // For students, the backend already filters based on enrollment and available courses
        // We can also filter by department if the user has a major
        if (user?.profile?.major) {
          console.log(`🎓 Student major: ${user.profile.major}`);

          // Get departments to find the matching department ID
          try {
            console.log('🏛️ Fetching departments for filtering...');
            const departmentsResponse = await apiService.getDepartments();
            console.log('🏛️ Departments API Response:', departmentsResponse);

            const departments = departmentsResponse?.results || departmentsResponse || [];
            console.log(`🏛️ Found ${departments.length} departments:`, departments.map((d: any) => ({ id: d.id, name: d.name, name_ar: d.name_ar })));

            // Find department that matches the student's major
            const userMajor = user.profile.major.toLowerCase();
            console.log(`🎓 Looking for department matching major: "${userMajor}"`);

            const matchingDepartment = departments.find((dept: any) => {
              const deptName = dept.name?.toLowerCase() || '';
              const deptNameAr = dept.name_ar?.toLowerCase() || '';

              console.log(`🔍 Checking department: "${dept.name}" (${dept.name_ar}) against major: "${userMajor}"`);

              // Direct match or common aliases
              const majorAliases: Record<string, string[]> = {
                'physics': ['physics', 'فيزياء'],
                'mathematics': ['mathematics', 'math', 'رياضيات'],
                'computer science': ['computer science', 'cs', 'علوم الحاسوب', 'حاسوب'],
                'engineering': ['engineering', 'هندسة'],
                'biology': ['biology', 'أحياء'],
                'chemistry': ['chemistry', 'كيمياء'],
              };

              const aliases = majorAliases[userMajor] || [userMajor];
              const isMatch = aliases.some(alias =>
                deptName.includes(alias) ||
                deptNameAr.includes(alias) ||
                alias.includes(deptName)
              );

              console.log(`🔍 Aliases for "${userMajor}":`, aliases, `Match: ${isMatch}`);
              return isMatch;
            });

            if (matchingDepartment) {
              apiParams.department = matchingDepartment.id.toString();
              console.log(`✅ Found matching department: ${matchingDepartment.name} (ID: ${matchingDepartment.id})`);
              console.log(`🎯 Will filter courses by department ID: ${matchingDepartment.id}`);
            } else {
              console.warn(`⚠️ No department found matching major: "${userMajor}"`);
              console.log('📋 Available departments:', departments.map((d: any) => `${d.name} (${d.name_ar})`));
            }
          } catch (deptError) {
            console.error('❌ Error fetching departments for filtering:', deptError);
          }
        }
      } else if (user?.role === 'teacher') {
        // For teachers, backend already filters to show only their courses
        console.log('👨‍🏫 Teacher - backend will filter to assigned courses');
      }

      // Fetch courses with proper filtering
      console.log('🎯 Final API parameters for courses:', apiParams);
      const coursesResponse = await apiService.getCourses(apiParams);
      console.log('✅ Courses API Response: 200', coursesResponse);

      if (!coursesResponse) {
        throw new Error('No courses response received');
      }

      let newCourses = coursesResponse.results || coursesResponse || [];

      // Handle pagination metadata
      if (coursesResponse.count !== undefined) {
        setTotalCount(coursesResponse.count);
        setHasNextPage(!!coursesResponse.next);
        setCurrentPage(page);
      } else {
        // Fallback for non-paginated response
        setTotalCount(newCourses.length);
        setHasNextPage(false);
      }

      // Apply client-side filtering if backend filtering failed
      if (user?.role === 'student' && user?.profile?.major && !apiParams.department) {
        console.log('🔧 Backend filtering failed, applying client-side filtering...');
        const userMajor = user.profile.major.toLowerCase();

        const filteredCourses = newCourses.filter((course: Course) => {
          const courseDepartment = course.department_name.toLowerCase();

          // Map common major names to department names
          const majorToDepartment: Record<string, string[]> = {
            'physics': ['physics', 'فيزياء'],
            'mathematics': ['mathematics', 'math', 'رياضيات'],
            'computer science': ['computer science', 'cs', 'علوم الحاسوب', 'حاسوب'],
            'engineering': ['engineering', 'هندسة'],
            'biology': ['biology', 'أحياء'],
            'chemistry': ['chemistry', 'كيمياء'],
          };

          // Check if course department matches user's major
          const departmentAliases = majorToDepartment[userMajor] || [userMajor];
          const isMatch = departmentAliases.some(alias =>
            courseDepartment.includes(alias) ||
            courseDepartment === alias ||
            alias.includes(courseDepartment)
          );

          return isMatch;
        });

        console.log(`🎯 Client-side filtering: ${filteredCourses.length} out of ${newCourses.length} courses match major "${user.profile.major}"`);
        newCourses = filteredCourses;
      }

      // For students, fetch comprehensive enrollment data including history
      if (user?.role === 'student' && newCourses.length > 0) {
        try {
          console.log('📚 Fetching comprehensive student enrollment data...');

          // Fetch both active and inactive enrollments to get full history
          const [activeEnrollments, enrollmentHistory] = await Promise.all([
            apiService.getEnrollments(),
            apiService.getStudentEnrollmentHistory()
          ]);

          const activeEnrollmentsList = activeEnrollments?.results || activeEnrollments || [];
          const allEnrollmentsList = enrollmentHistory?.results || enrollmentHistory || [];

          console.log(`📋 Found ${activeEnrollmentsList.length} active enrollments and ${allEnrollmentsList.length} total enrollment records`);

          // Create comprehensive enrollment maps
          const enrollmentMap = new Map();
          const enrollmentHistoryMap = new Map();

          // Process all enrollment history to track retakes and failures
          allEnrollmentsList.forEach((enrollment: any) => {
            const courseId = enrollment.course;
            if (!enrollmentHistoryMap.has(courseId)) {
              enrollmentHistoryMap.set(courseId, []);
            }
            enrollmentHistoryMap.get(courseId).push({
              id: enrollment.id,
              enrollment_date: enrollment.enrolled_at,
              completion_date: enrollment.completed_at,
              status: enrollment.status || 'enrolled',
              grade: enrollment.final_grade,
              is_active: enrollment.is_active,
            });
          });

          // Process active enrollments
          activeEnrollmentsList.forEach((enrollment: any) => {
            if (enrollment.course) {
              const courseHistory = enrollmentHistoryMap.get(enrollment.course) || [];
              const retakeCount = courseHistory.filter((h: any) =>
                h.status === 'failed' || h.status === 'dropped'
              ).length;

              enrollmentMap.set(enrollment.course, {
                id: enrollment.id,
                enrollment_date: enrollment.enrolled_at,
                completion_date: enrollment.completed_at,
                status: enrollment.status || 'enrolled',
                grade: enrollment.final_grade,
                is_active: enrollment.is_active,
                attempt_number: enrollment.attempt_number || 1,
                is_retake: enrollment.is_retake || false,
                attempt_display: enrollment.attempt_display || '',
                letter_grade: enrollment.letter_grade,
                retake_count: retakeCount,
                previous_attempts: courseHistory.filter((h: any) => h.id !== enrollment.id),
              });
            }
          });

          // Add enrollment status to courses with comprehensive information
          newCourses = newCourses.map((course: Course) => {
            const enrollment = enrollmentMap.get(course.id);
            const courseHistory = enrollmentHistoryMap.get(course.id) || [];

            let enrollmentStatus: Course['enrollment_status'] = 'available';

            if (enrollment) {
              // Student has active enrollment
              switch (enrollment.status) {
                case 'enrolled':
                case 'waitlisted':
                  enrollmentStatus = enrollment.status;
                  break;
                case 'completed':
                  enrollmentStatus = 'completed';
                  break;
                case 'failed':
                  enrollmentStatus = 'failed';
                  break;
                case 'dropped':
                  enrollmentStatus = 'available'; // Can re-enroll
                  break;
                default:
                  enrollmentStatus = 'enrolled';
              }
            } else if (courseHistory.length > 0) {
              // Student has history but no active enrollment
              const lastAttempt = courseHistory[courseHistory.length - 1];
              if (lastAttempt.status === 'completed') {
                enrollmentStatus = 'completed';
              } else if (lastAttempt.status === 'failed') {
                enrollmentStatus = 'failed'; // Show as failed, can retake
              } else {
                enrollmentStatus = 'available'; // Dropped or other status
              }
            }

            return {
              ...course,
              user_enrollment: enrollment || (courseHistory.length > 0 ? {
                id: 0,
                enrollment_date: courseHistory[0]?.enrollment_date || '',
                status: courseHistory[courseHistory.length - 1]?.status || 'available',
                grade: courseHistory[courseHistory.length - 1]?.grade,
                completion_date: courseHistory[courseHistory.length - 1]?.completion_date,
                is_active: false,
                retake_count: courseHistory.filter((h: any) => h.status === 'failed' || h.status === 'dropped').length,
                previous_attempts: courseHistory,
              } : undefined),
              enrollment_status: enrollmentStatus
            };
          });

          console.log(`✅ Enhanced ${newCourses.length} courses with comprehensive enrollment data`);

          // Log enrollment statistics
          const enrollmentStats = {
            enrolled: newCourses.filter(c => c.enrollment_status === 'enrolled').length,
            completed: newCourses.filter(c => c.enrollment_status === 'completed').length,
            failed: newCourses.filter(c => c.enrollment_status === 'failed').length,
            available: newCourses.filter(c => c.enrollment_status === 'available').length,
            waitlisted: newCourses.filter(c => c.enrollment_status === 'waitlisted').length,
          };
          console.log('📊 Enrollment Statistics:', enrollmentStats);

        } catch (enrollmentError) {
          console.error('❌ Error fetching enrollment data:', enrollmentError);
          // Continue without enrollment status
          newCourses = newCourses.map((course: Course) => ({
            ...course,
            enrollment_status: 'available' as const
          }));
        }
      }

      // Update courses state with pagination support
      if (append && page > 1) {
        setCourses(prevCourses => [...prevCourses, ...newCourses]);
      } else {
        setCourses(newCourses);
      }

      console.log(`🎯 Final courses count: ${newCourses.length} (page ${page})`);

    } catch (error) {
      console.error('❌ Error fetching courses:', error);
      Alert.alert(
        t('common.error.title', 'Error'),
        t('courses.error', 'Error loading courses')
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [user, ITEMS_PER_PAGE]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchCourses(1, false);
    setRefreshing(false);
  }, [fetchCourses]);

  const loadMoreCourses = useCallback(async () => {
    if (!loadingMore && hasNextPage && !loading) {
      console.log(`📄 Loading more courses (page ${currentPage + 1})`);
      await fetchCourses(currentPage + 1, true);
    }
  }, [loadingMore, hasNextPage, loading, currentPage, fetchCourses]);

  useEffect(() => {
    if (user) {
      fetchCourses(1, false);
    }
  }, [user, fetchCourses]);

  const getCourseName = (course: Course) => {
    return language === 'ar' && course.title_ar ? course.title_ar : course.title;
  };

  const getCourseDescription = (course: Course) => {
    return language === 'ar' && course.description_ar ? course.description_ar : course.description;
  };

  const getSemesterName = (semester: string) => {
    const semesters: Record<string, { en: string; ar: string }> = {
      fall: { en: 'Fall', ar: 'الخريف' },
      spring: { en: 'Spring', ar: 'الربيع' },
      summer: { en: 'Summer', ar: 'الصيف' },
    };
    return language === 'ar' ? semesters[semester]?.ar || semester : semesters[semester]?.en || semester;
  };

  const getEnrollmentPercentage = (enrolled: number, max: number) => {
    return max > 0 ? (enrolled / max) * 100 : 0;
  };

  // Filter courses based on active tab (memoized for performance)
  const filteredCourses = useMemo(() => {
    if (user?.role !== 'student') {
      return courses; // For non-students, show all courses
    }

    switch (activeTab) {
      case 'enrolled':
        return courses.filter(course =>
          course.enrollment_status === 'enrolled' || course.enrollment_status === 'waitlisted'
        );
      case 'completed':
        return courses.filter(course => course.enrollment_status === 'completed');
      case 'failed':
        return courses.filter(course => course.enrollment_status === 'failed');
      case 'available':
        return courses.filter(course => course.enrollment_status === 'available');
      case 'all':
      default:
        return courses;
    }
  }, [courses, activeTab, user?.role]);

  // Get count for each tab (memoized for performance)
  const tabCounts = useMemo(() => {
    if (user?.role !== 'student') {
      return {
        all: courses.length,
        enrolled: 0,
        available: 0,
        completed: 0,
        failed: 0,
      };
    }

    return {
      all: courses.length,
      enrolled: courses.filter(course =>
        course.enrollment_status === 'enrolled' || course.enrollment_status === 'waitlisted'
      ).length,
      available: courses.filter(course => course.enrollment_status === 'available').length,
      completed: courses.filter(course => course.enrollment_status === 'completed').length,
      failed: courses.filter(course => course.enrollment_status === 'failed').length,
    };
  }, [courses, user?.role]);

  // Optimized course card renderer with memoization
  const renderCourseItem = useCallback(({ item }: { item: Course }) => (
    <CourseCard
      course={item}
      theme={theme}
      language={language}
      onPress={(course) => {
        console.log('📱 Course card pressed:', course.code);
        navigation.navigate('CourseDetail', { courseId: course.id });
      }}
      onEnrollmentPress={(course) => {
        console.log('📝 Enrollment pressed for:', course.code);
        // Handle enrollment logic here
      }}
    />
  ), [theme, language, navigation]);

  // Render footer with loading indicator for pagination
  const renderFooter = useCallback(() => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={currentTheme.colors.primary} />
        <Text style={[styles.footerText, { color: currentTheme.colors.textSecondary }]}>
          {t('courses.loadingMore', 'Loading more courses...')}
        </Text>
      </View>
    );
  }, [loadingMore, currentTheme.colors.primary, currentTheme.colors.textSecondary, t]);

  // Handle end reached for pagination
  const handleEndReached = useCallback(() => {
    if (hasNextPage && !loadingMore && !loading) {
      loadMoreCourses();
    }
  }, [hasNextPage, loadingMore, loading, loadMoreCourses]);

  // Render tab button
  const renderTabButton = (tabKey: TabType, label: string, icon: string, count: number) => {
    const isActive = activeTab === tabKey;

    return (
      <TouchableOpacity
        key={tabKey}
        style={[
          styles.tabButton,
          isActive && styles.activeTabButton,
          { backgroundColor: isActive ? currentTheme.colors.primary : 'transparent' }
        ]}
        onPress={() => setActiveTab(tabKey)}
        activeOpacity={0.7}
      >
        <View style={styles.tabContent}>
          <Ionicons
            name={icon as any}
            size={18}
            color={isActive ? currentTheme.colors.surface : currentTheme.colors.textSecondary}
          />
          <Text style={[
            styles.tabLabel,
            { color: isActive ? currentTheme.colors.surface : currentTheme.colors.textSecondary }
          ]}>
            {label}
          </Text>
          {count > 0 && (
            <View style={[
              styles.tabBadge,
              { backgroundColor: isActive ? currentTheme.colors.surface : currentTheme.colors.primary }
            ]}>
              <Text style={[
                styles.tabBadgeText,
                { color: isActive ? currentTheme.colors.primary : currentTheme.colors.surface }
              ]}>
                {count}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  // Render tabs section
  const renderTabs = () => {
    if (user?.role !== 'student') {
      return null; // Don't show tabs for non-students
    }

    const tabs = [
      { key: 'all' as TabType, label: t('courses.tabs.all', 'All'), icon: 'library-outline', count: tabCounts.all },
      { key: 'enrolled' as TabType, label: t('courses.tabs.enrolled', 'Enrolled'), icon: 'checkmark-circle-outline', count: tabCounts.enrolled },
      { key: 'available' as TabType, label: t('courses.tabs.available', 'Available'), icon: 'add-circle-outline', count: tabCounts.available },
      { key: 'completed' as TabType, label: t('courses.tabs.completed', 'Completed'), icon: 'trophy-outline', count: tabCounts.completed },
      { key: 'failed' as TabType, label: t('courses.tabs.failed', 'Failed'), icon: 'close-circle-outline', count: tabCounts.failed },
    ];

    return (
      <View style={styles.tabsContainer}>
        <LinearGradient
          colors={theme === 'dark'
            ? ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
            : ['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.7)']
          }
          style={styles.tabsGradient}
        >
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tabsScrollContent}
          >
            {tabs.map(tab => renderTabButton(tab.key, tab.label, tab.icon, tab.count))}
          </ScrollView>
        </LinearGradient>
      </View>
    );
  };



  if (loading) {
    return (
      <BackgroundGradient>
        <SafeAreaView style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={currentTheme.colors.primary} />
            <Text style={styles.loadingText}>
              {t('courses.loading', 'Loading courses...')}
            </Text>
          </View>
        </SafeAreaView>
      </BackgroundGradient>
    );
  }

  return (
    <BackgroundGradient>
      <SafeAreaView style={styles.container} edges={['top']}>
        {/* Tabs Section */}
        {renderTabs()}

        {/* Courses List */}
        <FlatList
          data={filteredCourses}
          renderItem={renderCourseItem}
          keyExtractor={(item) => item.id.toString()}
          numColumns={2}
          key={`${activeTab}-2`} // Force re-render when changing tabs or numColumns
          style={styles.list}
          contentContainerStyle={styles.listContent}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={currentTheme.colors.primary}
              colors={[currentTheme.colors.primary]}
            />
          }
          // Pagination support
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          removeClippedSubviews={true}
          maxToRenderPerBatch={ITEMS_PER_PAGE}
          windowSize={10}
          initialNumToRender={INITIAL_LOAD_COUNT}
          getItemLayout={(data, index) => ({
            length: 240, // Approximate item height
            offset: 240 * Math.floor(index / 2),
            index,
          })}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <LinearGradient
                colors={theme === 'dark'
                  ? ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                  : ['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.7)']
                }
                style={styles.emptyCard}
              >
                <Ionicons
                  name="library-outline"
                  size={64}
                  color={currentTheme.colors.textSecondary}
                />
                <Text style={styles.emptyTitle}>
                  {activeTab === 'enrolled'
                    ? t('courses.emptyEnrolled', 'No enrolled courses')
                    : activeTab === 'completed'
                    ? t('courses.emptyCompleted', 'No completed courses')
                    : activeTab === 'available'
                    ? t('courses.emptyAvailable', 'No available courses')
                    : user?.role === 'student'
                    ? t('courses.emptyStudent', `No ${user?.profile?.major || 'your major'} courses available`)
                    : user?.role === 'teacher'
                    ? t('courses.emptyTeacher', 'No courses assigned to you')
                    : t('courses.empty', 'No courses available')
                  }
                </Text>
                <Text style={styles.emptySubtitle}>
                  {activeTab === 'enrolled'
                    ? t('courses.emptyEnrolledDesc', 'Courses you are currently enrolled in will appear here')
                    : activeTab === 'completed'
                    ? t('courses.emptyCompletedDesc', 'Courses you have completed will appear here')
                    : activeTab === 'available'
                    ? t('courses.emptyAvailableDesc', 'Courses available for enrollment will appear here')
                    : user?.role === 'student'
                    ? t('courses.emptyStudentDesc', 'Courses for your major will appear here when available')
                    : user?.role === 'teacher'
                    ? t('courses.emptyTeacherDesc', 'Courses you are teaching will appear here')
                    : t('courses.emptyDescription', 'Courses will appear here when available')
                  }
                </Text>
              </LinearGradient>
            </View>
          }
        />
      </SafeAreaView>
    </BackgroundGradient>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  loadingText: {
    marginTop: theme.spacing.lg,
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.xl * 2,
  },
  row: {
    justifyContent: 'space-between',
    paddingHorizontal: 0,
  },

  // Pagination footer
  footerLoader: {
    paddingVertical: theme.spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    marginTop: theme.spacing.sm,
    fontSize: theme.fontSize.sm,
    textAlign: 'center',
  },

  // Course Card Styles - Grid Layout
  courseCard: {
    width: CARD_WIDTH,
    marginBottom: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  courseCardGradient: {
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
    minHeight: 220, // Fixed height for grid consistency
  },

  // Course Header - Compact
  courseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  courseCodeBadge: {
    backgroundColor: `${theme.colors.primary}25`,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    borderColor: `${theme.colors.primary}40`,
  },
  courseCodeText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '700',
    color: theme.colors.primary,
    letterSpacing: 0.3,
  },
  creditHoursText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },

  // Course Title - Compact
  courseTitle: {
    fontSize: theme.fontSize.md,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    lineHeight: theme.fontSize.md * 1.2,
    minHeight: 36, // Ensure consistent height
  },

  // Instructor Row - Compact
  instructorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  instructorName: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.xs,
    flex: 1,
  },

  // Meta Row - Department & Semester
  metaRow: {
    marginBottom: theme.spacing.sm,
  },
  departmentText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  semesterText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },

  // Enrollment Section - Compact
  enrollmentSection: {
    marginBottom: theme.spacing.sm,
  },
  enrollmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  enrollmentLabel: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: `${theme.colors.textSecondary}20`,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressPercentage: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },

  // Status Container - Bottom of card
  statusContainer: {
    marginTop: 'auto', // Push to bottom
    paddingTop: theme.spacing.xs,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  enrollmentStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    flex: 1,
    minWidth: 60,
  },
  enrollmentStatusText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
    marginLeft: 2,
  },
  retakeBadge: {
    marginLeft: theme.spacing.xs,
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: theme.borderRadius.xs,
    minWidth: 16,
    alignItems: 'center',
  },
  retakeText: {
    fontSize: theme.fontSize.xs - 1,
    fontWeight: '700',
  },
  gradeBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    marginLeft: theme.spacing.xs,
  },
  gradeText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  statusDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginRight: theme.spacing.xs,
  },
  statusText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
  },

  // Tabs Styles
  tabsContainer: {
    marginBottom: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  tabsGradient: {
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    paddingVertical: theme.spacing.xs,
  },
  tabsScrollContent: {
    paddingHorizontal: theme.spacing.sm,
    gap: theme.spacing.xs,
  },
  tabButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.xs,
  },
  activeTabButton: {
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  tabLabel: {
    fontSize: theme.fontSize.sm,
    fontWeight: '600',
  },
  tabBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  tabBadgeText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '700',
  },

  // Empty State - Full width for grid
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xl * 2,
    width: width - (theme.spacing.md * 2), // Full width minus margins
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl * 2,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.xl,
    borderWidth: 1,
    borderColor: theme.colors.border,
    width: '100%',
  },
  emptyTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: '700',
    color: theme.colors.text,
    textAlign: 'center',
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.sm,
  },
  emptySubtitle: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: theme.fontSize.md * 1.4,
  },
});

export default CoursesScreen;
