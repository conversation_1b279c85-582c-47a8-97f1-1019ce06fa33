import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  RefreshControl,
  FlatList,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { useAppSelector } from '../../store';
import { lightTheme, darkTheme, createGlassStyles } from '../../styles/theme';
import { apiService } from '../../services/api';
import BackgroundGradient from '../../components/common/BackgroundGradient';
import ThemeToggle from '../../components/common/ThemeToggle';

interface ScheduleItem {
  id: string;
  title?: string;
  title_ar?: string;
  course: {
    id: string;
    title: string;
    title_ar?: string;
    code: string;
    instructor: {
      id: string;
      first_name: string;
      last_name: string;
      first_name_ar?: string;
      last_name_ar?: string;
    };
  };
  day_of_week: string;
  time_slot: {
    id: string;
    name: string;
    name_ar?: string;
    start_time: string;
    end_time: string;
  };
  room: {
    id: string;
    name: string;
    name_ar?: string;
    code: string;
    building: string;
    floor?: string;
    capacity: number;
  };
  start_date: string;
  end_date: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const ScheduleScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation();
  const { theme, language } = useAppSelector((state) => state.ui);
  const { user } = useAppSelector((state) => state.auth);
  
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month'>('day');
  const [currentWeek, setCurrentWeek] = useState(new Date());
  
  const currentTheme = theme === 'dark' ? darkTheme : lightTheme;
  const glassStyles = createGlassStyles(currentTheme);
  const styles = createStyles(currentTheme);

  useEffect(() => {
    fetchSchedule();
  }, [selectedDate, viewMode]);

  const fetchSchedule = async () => {
    try {
      const queryParams = new URLSearchParams({
        date: selectedDate.toISOString().split('T')[0],
        view: viewMode,
      });

      const response = await apiService.request(`/schedules/?${queryParams}`, {
        method: 'GET',
      });

      const data = response as { results?: ScheduleItem[] };
      setScheduleItems(data.results || []);
    } catch (error) {
      console.error('Failed to fetch schedule:', error);
      setScheduleItems([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };



  const onRefresh = () => {
    setRefreshing(true);
    fetchSchedule();
  };



  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const isToday = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isTomorrow = (dateString: string) => {
    const date = new Date(dateString);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return date.toDateString() === tomorrow.toDateString();
  };

  const getDateLabel = (dateString: string) => {
    if (isToday(dateString)) {
      return t('schedule.today', 'Today');
    } else if (isTomorrow(dateString)) {
      return t('schedule.tomorrow', 'Tomorrow');
    } else {
      return formatDate(dateString);
    }
  };

  const handleScheduleItemPress = (item: ScheduleItem) => {
    // Navigate to course detail
    try {
      (navigation as any).navigate('CourseDetail', { courseId: item.course.id });
    } catch (error) {
      console.log('Navigation error:', error);
      // Show details in alert as fallback
      Alert.alert(
        item.course.title,
        `${t('schedule.time', 'Time')}: ${formatTime(item.time_slot.start_time)} - ${formatTime(item.time_slot.end_time)}\n${t('schedule.location', 'Location')}: ${item.room.name}, ${item.room.building}${item.room.floor ? `, Floor ${item.room.floor}` : ''}`,
        [{ text: t('common.ok', 'OK') }]
      );
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    if (viewMode === 'day') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (viewMode === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else if (viewMode === 'month') {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    }
    setSelectedDate(newDate);
  };

  // Group by day of week since schedules are recurring
  const groupedSchedule = scheduleItems.reduce((groups, item) => {
    const dayKey = item.day_of_week;
    if (!groups[dayKey]) {
      groups[dayKey] = [];
    }
    groups[dayKey].push(item);
    return groups;
  }, {} as Record<string, ScheduleItem[]>);

  // Sort items by start time within each day
  Object.keys(groupedSchedule).forEach(day => {
    groupedSchedule[day].sort((a, b) => a.time_slot.start_time.localeCompare(b.time_slot.start_time));
  });

  const renderScheduleItem = ({ item }: { item: ScheduleItem }) => (
    <TouchableOpacity
      style={[glassStyles.listItem, styles.scheduleItem]}
      onPress={() => handleScheduleItemPress(item)}
    >
      <View style={styles.scheduleItemHeader}>
        <View style={styles.scheduleItemLeft}>
          <View style={[styles.typeIcon, { backgroundColor: currentTheme.colors.primary + '20' }]}>
            <Ionicons
              name="school"
              size={20}
              color={currentTheme.colors.primary}
            />
          </View>

          <View style={styles.scheduleItemInfo}>
            <Text style={styles.scheduleItemTitle}>
              {language === 'ar' && item.course.title_ar ? item.course.title_ar : item.course.title}
            </Text>
            <Text style={styles.scheduleItemCourse}>
              {item.course.code} - {item.day_of_week}
            </Text>
            <View style={styles.scheduleItemMeta}>
              <Text style={styles.scheduleItemTime}>
                {formatTime(item.time_slot.start_time)} - {formatTime(item.time_slot.end_time)}
              </Text>
              <Text style={styles.scheduleItemLocation}>
                📍 {language === 'ar' && item.room.name_ar ? item.room.name_ar : item.room.name}, {item.room.building}
                {item.room.floor && `, ${t('schedule.floor', 'Floor')} ${item.room.floor}`}
              </Text>
            </View>
            <Text style={styles.scheduleItemInstructor}>
              {t('schedule.instructor', 'Instructor')}: {
                language === 'ar' && item.course.instructor.first_name_ar
                  ? `${item.course.instructor.first_name_ar} ${item.course.instructor.last_name_ar || ''}`
                  : `${item.course.instructor.first_name} ${item.course.instructor.last_name}`
              }
            </Text>
          </View>
        </View>

        <View style={styles.scheduleItemRight}>
          <View style={[styles.statusBadge, { backgroundColor: item.is_active ? currentTheme.colors.success + '20' : currentTheme.colors.error + '20' }]}>
            <Text style={[styles.statusText, { color: item.is_active ? currentTheme.colors.success : currentTheme.colors.error }]}>
              {item.is_active ? t('schedule.active', 'Active') : t('schedule.inactive', 'Inactive')}
            </Text>
          </View>
          <Text style={styles.attendeesText}>
            {t('schedule.capacity', 'Capacity')}: {item.room.capacity}
          </Text>
          <Ionicons name="chevron-forward" size={16} color={currentTheme.colors.textSecondary} />
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <BackgroundGradient>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={currentTheme.colors.primary} />
            <Text style={styles.loadingText}>
              {t('schedule.loading', 'Loading schedule...')}
            </Text>
          </View>
        </BackgroundGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <BackgroundGradient>
        <ScrollView 
          style={styles.scrollView} 
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {/* Header */}
          <View style={[glassStyles.header, styles.header]}>
            <View style={styles.headerContent}>
              <Text style={styles.title}>
                {t('schedule.mySchedule', 'My Schedule')}
              </Text>
              <ThemeToggle />
            </View>
            <Text style={styles.subtitle}>
              {t('schedule.description', 'Your personal class schedule and appointments')}
            </Text>
          </View>

          {/* View Mode Selector */}
          <View style={[glassStyles.card, styles.viewModeCard]}>
            <View style={styles.viewModeContainer}>
              {(['day', 'week', 'month'] as const).map((mode) => (
                <TouchableOpacity
                  key={mode}
                  style={[styles.viewModeTab, viewMode === mode && styles.activeViewModeTab]}
                  onPress={() => setViewMode(mode)}
                >
                  <Text style={[styles.viewModeText, viewMode === mode && styles.activeViewModeText]}>
                    {t(`schedule.${mode}`, mode)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Date Navigation */}
          <View style={[glassStyles.card, styles.dateNavigation]}>
            <TouchableOpacity
              style={styles.navButton}
              onPress={() => navigateDate('prev')}
            >
              <Ionicons name="chevron-back" size={20} color={currentTheme.colors.primary} />
            </TouchableOpacity>
            
            <View style={styles.dateDisplay}>
              <Text style={styles.dateText}>
                {viewMode === 'day' ? getDateLabel(selectedDate.toISOString().split('T')[0]) : 
                 viewMode === 'week' ? t('schedule.weekView', 'Week View') :
                 t('schedule.monthView', 'Month View')}
              </Text>
              <Text style={styles.dateSubtext}>
                {selectedDate.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.navButton}
              onPress={() => navigateDate('next')}
            >
              <Ionicons name="chevron-forward" size={20} color={currentTheme.colors.primary} />
            </TouchableOpacity>
          </View>

          {/* Schedule Items */}
          {Object.keys(groupedSchedule).length > 0 ? (
            Object.keys(groupedSchedule)
              .sort()
              .map((dayOfWeek) => (
                <View key={dayOfWeek} style={styles.dateGroup}>
                  <Text style={styles.dateGroupTitle}>
                    {t(`schedule.${dayOfWeek.toLowerCase()}`, dayOfWeek)}
                  </Text>

                  <FlatList
                    data={groupedSchedule[dayOfWeek]}
                    renderItem={renderScheduleItem}
                    keyExtractor={(item) => item.id}
                    scrollEnabled={false}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              ))
          ) : (
            <View style={[glassStyles.card, styles.emptyContainer]}>
              <Ionicons
                name="calendar-outline"
                size={48}
                color={currentTheme.colors.textSecondary}
              />
              <Text style={styles.emptyText}>
                {t('schedule.noSchedule', 'No schedule items')}
              </Text>
              <Text style={styles.emptySubtext}>
                {t('schedule.noScheduleDesc', 'You have no scheduled items for the selected period')}
              </Text>
            </View>
          )}
        </ScrollView>
      </BackgroundGradient>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  loadingText: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
  scrollView: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  header: {
    marginBottom: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  title: {
    fontSize: theme.fontSize.xxl,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  subtitle: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
  },
  viewModeCard: {
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.sm,
  },
  viewModeContainer: {
    flexDirection: 'row',
  },
  viewModeTab: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    borderRadius: theme.borderRadius.md,
  },
  activeViewModeTab: {
    backgroundColor: theme.colors.primary + '20',
  },
  viewModeText: {
    fontSize: theme.fontSize.sm,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    textTransform: 'capitalize',
  },
  activeViewModeText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  dateNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.lg,
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateDisplay: {
    flex: 1,
    alignItems: 'center',
  },
  dateText: {
    fontSize: theme.fontSize.lg,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  dateSubtext: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  dateGroup: {
    marginBottom: theme.spacing.xl,
  },
  dateGroupTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  scheduleItem: {
    marginBottom: theme.spacing.md,
  },
  scheduleItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  scheduleItemLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  scheduleItemInfo: {
    flex: 1,
  },
  scheduleItemTitle: {
    fontSize: theme.fontSize.md,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  scheduleItemCourse: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
  },
  scheduleItemMeta: {
    marginBottom: theme.spacing.sm,
  },
  scheduleItemTime: {
    fontSize: theme.fontSize.sm,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  scheduleItemLocation: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  scheduleItemInstructor: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  scheduleItemRight: {
    alignItems: 'flex-end',
    gap: theme.spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    fontSize: theme.fontSize.xs,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  attendeesText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textSecondary,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl * 2,
  },
  emptyText: {
    fontSize: theme.fontSize.md,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'center',
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  emptySubtext: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});

export default ScheduleScreen;
