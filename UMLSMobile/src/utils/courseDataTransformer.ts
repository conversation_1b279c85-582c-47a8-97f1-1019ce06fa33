/**
 * Mobile Course Data Transformer
 * Standardizes course enrollment data across all React Native components
 * Ensures consistent handling of enrollment status and course information
 */

export type EnrollmentStatus = 
  | 'available' 
  | 'enrolled' 
  | 'waitlisted' 
  | 'completed' 
  | 'failed' 
  | 'retakeable' 
  | 'retake_limit_exceeded'
  | 'not_available'
  | 'prerequisites_not_met';

export interface EnrollmentAttempt {
  id: number;
  status: string;
  attempt_number: number;
  is_retake: boolean;
  enrolled_at: string;
  completed_at?: string;
  dropped_at?: string;
  final_grade?: number;
  letter_grade?: string;
  is_active: boolean;
}

export interface StandardizedMobileCourse {
  id: number;
  title: string;
  title_ar?: string;
  code: string;
  description?: string;
  description_ar?: string;
  credit_hours: number;
  instructor_name: string;
  department_name: string;
  semester: 'fall' | 'spring' | 'summer';
  year: number;
  max_students: number;
  enrolled_students_count: number;
  is_active: boolean;
  
  // Enrollment-specific fields (standardized)
  enrollment_status: EnrollmentStatus;
  user_enrollment?: {
    id: number;
    enrollment_date: string;
    status: string;
    grade?: string;
    is_active: boolean;
    attempt_number?: number;
    is_retake?: boolean;
    attempt_display?: string;
    letter_grade?: string;
    retake_count?: number;
    previous_attempts?: any[];
  };
  
  // Display helpers
  statusColor: string;
  statusIcon: string;
  canEnroll: boolean;
  enrollmentMessage: string;
}

/**
 * Transform raw API course data to standardized mobile format
 */
export const transformMobileCourseData = (apiCourse: any): StandardizedMobileCourse => {
  // Determine enrollment status with fallback logic
  const enrollmentStatus: EnrollmentStatus = 
    apiCourse.user_enrollment_status || 
    apiCourse.enrollment_status || 
    'available';

  // Calculate display properties based on status
  const { statusColor, statusIcon } = getMobileStatusDisplayProperties(enrollmentStatus);

  return {
    id: apiCourse.id || 0,
    title: apiCourse.title || '',
    title_ar: apiCourse.title_ar || '',
    code: apiCourse.code || '',
    description: apiCourse.description || '',
    description_ar: apiCourse.description_ar || '',
    credit_hours: apiCourse.credit_hours || 0,
    instructor_name: apiCourse.instructor_name || '',
    department_name: apiCourse.department_name || '',
    semester: apiCourse.semester || 'fall',
    year: apiCourse.year || new Date().getFullYear(),
    max_students: apiCourse.max_students || 0,
    enrolled_students_count: apiCourse.enrolled_students_count || 0,
    is_active: apiCourse.is_active ?? true,
    
    // Enrollment fields
    enrollment_status: enrollmentStatus,
    user_enrollment: apiCourse.user_enrollment || undefined,
    
    // Display properties
    statusColor,
    statusIcon,
    canEnroll: apiCourse.user_can_enroll ?? false,
    enrollmentMessage: apiCourse.user_enrollment_message || '',
  };
};

/**
 * Get display properties for mobile enrollment status
 */
export const getMobileStatusDisplayProperties = (status: EnrollmentStatus) => {
  switch (status) {
    case 'enrolled':
      return {
        statusColor: '#10B981', // green-500
        statusIcon: 'checkmark-circle',
      };
    
    case 'waitlisted':
      return {
        statusColor: '#F59E0B', // amber-500
        statusIcon: 'time',
      };
    
    case 'completed':
      return {
        statusColor: '#3B82F6', // blue-500
        statusIcon: 'trophy',
      };
    
    case 'failed':
      return {
        statusColor: '#EF4444', // red-500
        statusIcon: 'close-circle',
      };
    
    case 'retakeable':
      return {
        statusColor: '#F97316', // orange-500
        statusIcon: 'refresh',
      };
    
    case 'retake_limit_exceeded':
      return {
        statusColor: '#6B7280', // gray-500
        statusIcon: 'ban',
      };
    
    case 'prerequisites_not_met':
      return {
        statusColor: '#DC2626', // red-600
        statusIcon: 'lock-closed',
      };
    
    case 'available':
      return {
        statusColor: '#059669', // emerald-600
        statusIcon: 'book',
      };
    
    case 'not_available':
    default:
      return {
        statusColor: '#9CA3AF', // gray-400
        statusIcon: 'lock-closed',
      };
  }
};

/**
 * Transform array of courses for mobile
 */
export const transformMobileCoursesArray = (apiCourses: any[]): StandardizedMobileCourse[] => {
  if (!Array.isArray(apiCourses)) {
    console.warn('transformMobileCoursesArray: Expected array, got:', typeof apiCourses);
    return [];
  }
  
  return apiCourses.map(transformMobileCourseData);
};

/**
 * Filter courses by enrollment status for mobile
 */
export const filterMobileCoursesByStatus = (
  courses: StandardizedMobileCourse[], 
  status: EnrollmentStatus | 'all'
): StandardizedMobileCourse[] => {
  if (status === 'all') {
    return courses;
  }
  
  return courses.filter(course => course.enrollment_status === status);
};

/**
 * Get course counts by status for mobile tabs
 */
export const getMobileCourseStatusCounts = (courses: StandardizedMobileCourse[]) => {
  const counts = {
    all: courses.length,
    enrolled: 0,
    available: 0,
    completed: 0,
    failed: 0,
  };
  
  courses.forEach(course => {
    switch (course.enrollment_status) {
      case 'enrolled':
      case 'waitlisted':
        counts.enrolled++;
        break;
      case 'available':
      case 'retakeable':
        counts.available++;
        break;
      case 'completed':
        counts.completed++;
        break;
      case 'failed':
        counts.failed++;
        break;
    }
  });
  
  return counts;
};

/**
 * Check if course enrollment action is allowed on mobile
 */
export const canPerformMobileEnrollmentAction = (course: StandardizedMobileCourse): boolean => {
  return course.canEnroll && 
         course.enrollment_status !== 'enrolled' && 
         course.enrollment_status !== 'waitlisted' &&
         course.enrollment_status !== 'completed' &&
         course.enrollment_status !== 'retake_limit_exceeded' &&
         course.enrollment_status !== 'prerequisites_not_met';
};

/**
 * Get enrollment action type for mobile
 */
export const getMobileEnrollmentActionType = (course: StandardizedMobileCourse): 'enroll' | 'retake' | 'waitlist' | 'none' => {
  if (!canPerformMobileEnrollmentAction(course)) {
    return 'none';
  }
  
  if (course.enrollment_status === 'retakeable') {
    return 'retake';
  }
  
  if (course.enrollmentMessage?.toLowerCase().includes('waitlist')) {
    return 'waitlist';
  }
  
  return 'enroll';
};

/**
 * Get localized status text for mobile
 */
export const getMobileStatusText = (status: EnrollmentStatus, t: (key: string, fallback?: string) => string): string => {
  switch (status) {
    case 'enrolled':
      return t('courses.enrolled', 'Enrolled');
    case 'waitlisted':
      return t('courses.waitlisted', 'Waitlisted');
    case 'completed':
      return t('courses.completed', 'Completed');
    case 'failed':
      return t('courses.failed', 'Failed');
    case 'retakeable':
      return t('courses.retakeable', 'Retakeable');
    case 'retake_limit_exceeded':
      return t('courses.retakeLimitExceeded', 'Retake Limit Exceeded');
    case 'prerequisites_not_met':
      return t('courses.prerequisitesNotMet', 'Prerequisites Not Met');
    case 'available':
      return t('courses.available', 'Available');
    case 'not_available':
    default:
      return t('courses.notAvailable', 'Not Available');
  }
};
