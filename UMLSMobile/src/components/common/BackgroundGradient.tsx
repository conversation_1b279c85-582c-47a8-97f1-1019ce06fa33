import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAppSelector } from '../../store';

const { width, height } = Dimensions.get('window');

interface BackgroundGradientProps {
  children?: React.ReactNode;
}

const BackgroundGradient: React.FC<BackgroundGradientProps> = ({ children }) => {
  const { theme } = useAppSelector((state) => state?.ui || { theme: 'light' });

  if (theme === 'dark') {
    return (
      <View style={styles.container}>
        {/* Dark mode background matching web */}
        <LinearGradient
          colors={['#000000', '#000000']}
          style={styles.gradient}
        />
        
        {/* Dark mode radial gradients simulation */}
        <View style={[styles.radialGradient, styles.topLeft, styles.darkTopLeft]} />
        <View style={[styles.radialGradient, styles.topRight, styles.darkTopRight]} />
        <View style={[styles.radialGradient, styles.center, styles.darkCenter]} />
        
        {children}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Light mode background matching web */}
      <LinearGradient
        colors={['#40b1b5', '#ffffff', '#ffffff']}
        locations={[0, 0.05, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      />
      
      {/* Light mode radial gradients simulation */}
      <View style={[styles.radialGradient, styles.topLeft, styles.lightTopLeft]} />
      <View style={[styles.radialGradient, styles.bottomRight, styles.lightBottomRight]} />
      
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  radialGradient: {
    position: 'absolute',
    borderRadius: width,
  },
  
  // Light mode gradients
  topLeft: {
    top: -width * 0.3,
    left: -width * 0.3,
    width: width * 0.7,
    height: width * 0.6,
  },
  bottomRight: {
    bottom: -width * 0.2,
    right: -width * 0.2,
    width: width * 0.4,
    height: width * 0.4,
  },
  center: {
    top: height * 0.4,
    left: width * 0.2,
    width: width * 0.6,
    height: width * 0.6,
  },
  
  // Light mode colors
  lightTopLeft: {
    backgroundColor: 'rgba(64, 234, 234, 0.8)',
    opacity: 0.3,
  },
  lightBottomRight: {
    backgroundColor: 'rgba(7, 232, 252, 0.8)',
    opacity: 0.2,
  },
  
  // Dark mode colors
  darkTopLeft: {
    backgroundColor: 'rgba(64, 234, 234, 0.8)',
    opacity: 0.5,
      top: -width * 0.2,
    right: -width * 0.8,
    width: width * 0.4,
    height: width * 0.4,
   
  },
  darkTopRight: {
    top: -width * 0.2,
    right: -width * 0.2,
    width: width * 0.4,
    height: width * 0.4,
    backgroundColor: 'rgba(64, 234, 234, 0.8)',
    opacity: 0.3,
  },
  darkCenter: {
     backgroundColor: 'rgba(7, 108, 108, 0.8)',
    opacity: 0.4,
  },
});

export default BackgroundGradient;
