import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../hooks/useTheme';

const { width, height } = Dimensions.get('window');

interface BackgroundGradientProps {
  children: React.ReactNode;
}

export const BackgroundGradient: React.FC<BackgroundGradientProps> = ({ children }) => {
  const { isDark: isDarkMode } = useTheme();

  return (
    <View style={styles.container}>
      {/* Main background gradient exactly matching your CSS */}
      <LinearGradient
        colors={
          isDarkMode
            ? ['#0f172a', '#1e293b', '#0f172a'] // Dark slate gradient for better glass visibility
            : ['#40b1b5', '#ffffff', '#ffffff'] // Exact CSS: linear-gradient(135deg, #40b1b5 0%, #fff 5%, #fff 100%)
        }
        locations={isDarkMode ? [0, 0.5, 1] : [0, 0.05, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }} // 135deg angle like CSS
        style={styles.mainGradient}
      />

      {/* Top left radial gradient effect - enhanced for dark mode visibility */}
      <View style={[
        styles.radialGradient,
        styles.topLeft,
        {
          backgroundColor: isDarkMode
            ? 'rgba(64, 234, 234, 0.15)' // More subtle for dark mode
            : 'rgba(64, 234, 234, 0.8)', // CSS: rgba(64, 234, 234, 0.8)
        }
      ]} />

      {/* Bottom right radial gradient effect - enhanced for dark mode visibility */}
      <View style={[
        styles.radialGradient,
        styles.bottomRight,
        {
          backgroundColor: isDarkMode
            ? 'rgba(7, 232, 252, 0.15)' // More subtle for dark mode
            : 'rgba(7, 232, 252, 0.8)', // CSS: rgba(7, 232, 252, 0.8)
        }
      ]} />

      {/* Dark mode additional effects */}
      {isDarkMode && (
        <>
          {/* Bottom left dark effect */}
          <View style={[
            styles.radialGradient,
            styles.bottomLeft,
            { backgroundColor: 'rgba(14, 14, 15, 0.2)' }
          ]} />

          {/* Center right blue effect */}
          <View style={[
            styles.radialGradient,
            styles.centerRight,
            { backgroundColor: 'rgba(15, 193, 216, 0.2)' }
          ]} />

          {/* Center purple effect */}
          <View style={[
            styles.radialGradient,
            styles.center,
            { backgroundColor: 'rgba(99, 102, 241, 0.15)' }
          ]} />
        </>
      )}

      {/* Content container */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  mainGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: width,
    height: height,
  },
  radialGradient: {
    position: 'absolute',
    borderRadius: width, // Make it circular
    opacity: 0.6,
  },
  topLeft: {
    width: width * 0.6,
    height: width * 0.6,
    top: -width * 0.3,
    left: -width * 0.3,
  },
  bottomRight: {
    width: width * 0.4,
    height: width * 0.4,
    bottom: -width * 0.2,
    right: -width * 0.2,
  },
  bottomLeft: {
    width: width * 0.8,
    height: width * 0.8,
    bottom: -width * 0.4,
    left: -width * 0.4,
  },
  centerRight: {
    width: width * 0.6,
    height: width * 0.6,
    top: height * 0.2,
    right: -width * 0.3,
  },
  center: {
    width: width * 0.5,
    height: width * 0.5,
    top: height * 0.4,
    left: width * 0.25,
  },
  content: {
    flex: 1,
    zIndex: 1,
  },
});

export default BackgroundGradient;
