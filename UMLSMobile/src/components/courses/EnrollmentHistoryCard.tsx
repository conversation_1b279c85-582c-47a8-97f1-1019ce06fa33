import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../hooks/useTheme';
import { lightTheme, darkTheme } from '../../styles/theme';

interface EnrollmentAttempt {
  id: number;
  attempt_number: number;
  is_retake: boolean;
  status: string;
  final_grade?: number;
  letter_grade?: string;
  enrolled_at: string;
  completed_at?: string;
  dropped_at?: string;
  is_active: boolean;
}

interface EnrollmentHistoryCardProps {
  attempts: EnrollmentAttempt[];
  courseCode: string;
  courseTitle: string;
}

const EnrollmentHistoryCard: React.FC<EnrollmentHistoryCardProps> = ({
  attempts,
  courseCode,
  courseTitle,
}) => {
  const { t } = useTranslation();
  const { theme: currentTheme } = useTheme();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'enrolled':
        return 'school-outline';
      case 'completed':
        return 'checkmark-circle-outline';
      case 'failed':
        return 'close-circle-outline';
      case 'dropped':
        return 'remove-circle-outline';
      case 'waitlisted':
        return 'time-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'enrolled':
        return currentTheme.colors.primary;
      case 'completed':
        return currentTheme.colors.success;
      case 'failed':
        return currentTheme.colors.error;
      case 'dropped':
        return currentTheme.colors.textSecondary;
      case 'waitlisted':
        return currentTheme.colors.warning;
      default:
        return currentTheme.colors.textSecondary;
    }
  };

  const getGradeColor = (grade?: string) => {
    if (!grade) return currentTheme.colors.textSecondary;
    
    if (['A+', 'A', 'A-'].includes(grade)) return currentTheme.colors.success;
    if (['B+', 'B', 'B-'].includes(grade)) return currentTheme.colors.primary;
    if (['C+', 'C', 'C-'].includes(grade)) return currentTheme.colors.warning;
    return currentTheme.colors.error;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const styles = createStyles(currentTheme);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.courseCode}>{courseCode}</Text>
        <Text style={styles.courseTitle} numberOfLines={2}>
          {courseTitle}
        </Text>
        <Text style={styles.attemptsCount}>
          {t('courses.attempts', '{{count}} attempts', { count: attempts.length })}
        </Text>
      </View>

      {/* Attempts Timeline */}
      <View style={styles.timeline}>
        {attempts.map((attempt, index) => (
          <View key={attempt.id} style={styles.attemptContainer}>
            {/* Timeline Line */}
            {index < attempts.length - 1 && <View style={styles.timelineLine} />}
            
            {/* Attempt Details */}
            <View style={styles.attemptContent}>
              {/* Status Icon */}
              <View style={[
                styles.statusIcon,
                { backgroundColor: getStatusColor(attempt.status) + '20' }
              ]}>
                <Ionicons
                  name={getStatusIcon(attempt.status) as any}
                  size={20}
                  color={getStatusColor(attempt.status)}
                />
              </View>

              {/* Attempt Info */}
              <View style={styles.attemptInfo}>
                <View style={styles.attemptHeader}>
                  <Text style={styles.attemptNumber}>
                    {attempt.is_retake 
                      ? t('courses.retake_attempt', 'Retake #{{number}}', { number: attempt.attempt_number })
                      : t('courses.first_attempt', 'First Attempt')
                    }
                  </Text>
                  <Text style={[
                    styles.statusText,
                    { color: getStatusColor(attempt.status) }
                  ]}>
                    {t(`courses.status.${attempt.status}`, attempt.status)}
                  </Text>
                </View>

                {/* Grade Display */}
                {attempt.letter_grade && (
                  <View style={styles.gradeContainer}>
                    <Text style={styles.gradeLabel}>
                      {t('courses.grade', 'Grade')}:
                    </Text>
                    <Text style={[
                      styles.gradeValue,
                      { color: getGradeColor(attempt.letter_grade) }
                    ]}>
                      {attempt.letter_grade}
                    </Text>
                    {attempt.final_grade && (
                      <Text style={styles.numericGrade}>
                        ({attempt.final_grade}%)
                      </Text>
                    )}
                  </View>
                )}

                {/* Dates */}
                <View style={styles.datesContainer}>
                  <Text style={styles.dateText}>
                    {t('courses.enrolled', 'Enrolled')}: {formatDate(attempt.enrolled_at)}
                  </Text>
                  {attempt.completed_at && (
                    <Text style={styles.dateText}>
                      {t('courses.completed', 'Completed')}: {formatDate(attempt.completed_at)}
                    </Text>
                  )}
                  {attempt.dropped_at && (
                    <Text style={styles.dateText}>
                      {t('courses.dropped', 'Dropped')}: {formatDate(attempt.dropped_at)}
                    </Text>
                  )}
                </View>

                {/* Active Indicator */}
                {attempt.is_active && (
                  <View style={styles.activeIndicator}>
                    <Ionicons name="radio-button-on" size={12} color={currentTheme.colors.success} />
                    <Text style={[styles.activeText, { color: currentTheme.colors.success }]}>
                      {t('courses.current', 'Current')}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  courseCode: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  courseTitle: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 4,
  },
  attemptsCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  timeline: {
    paddingLeft: 8,
  },
  attemptContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  timelineLine: {
    position: 'absolute',
    left: 19,
    top: 40,
    bottom: -16,
    width: 2,
    backgroundColor: theme.colors.border,
  },
  attemptContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statusIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  attemptInfo: {
    flex: 1,
  },
  attemptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  attemptNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  gradeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  gradeLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginRight: 8,
  },
  gradeValue: {
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 4,
  },
  numericGrade: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  datesContainer: {
    marginBottom: 8,
  },
  dateText: {
    fontSize: 11,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  activeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeText: {
    fontSize: 11,
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default EnrollmentHistoryCard;
