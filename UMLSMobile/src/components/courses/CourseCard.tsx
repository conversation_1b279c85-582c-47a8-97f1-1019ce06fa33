import React, { memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { lightTheme, darkTheme, createGlassStyles } from '../../styles/theme';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2; // 2 columns with margins

interface Course {
  id: number;
  title: string;
  title_ar?: string;
  code: string;
  description?: string;
  description_ar?: string;
  credit_hours: number;
  instructor_name: string;
  department_name: string;
  semester: 'fall' | 'spring' | 'summer';
  year: number;
  max_students: number;
  enrolled_students_count: number;
  is_active: boolean;
  enrollment_status?: 'enrolled' | 'completed' | 'available' | 'prerequisites_not_met' | 'waitlisted' | 'dropped' | 'failed' | 'retakeable';
  user_enrollment?: {
    id: number;
    enrollment_date: string;
    status: string;
    grade?: string;
    is_active: boolean;
    attempt_number?: number;
    is_retake?: boolean;
    attempt_display?: string;
    letter_grade?: string;
    retake_count?: number;
    previous_attempts?: any[];
  };
}

interface CourseCardProps {
  course: Course;
  theme: 'light' | 'dark';
  language: string;
  onPress: (course: Course) => void;
  onEnrollmentPress?: (course: Course) => void;
}

const CourseCard: React.FC<CourseCardProps> = memo(({
  course,
  theme,
  language,
  onPress,
  onEnrollmentPress
}) => {
  const { t } = useTranslation();
  const currentTheme = theme === 'dark' ? darkTheme : lightTheme;
  const glassStyles = createGlassStyles(currentTheme);
  const styles = createStyles(currentTheme);

  const getCourseName = (course: Course) => {
    return language === 'ar' && course.title_ar ? course.title_ar : course.title;
  };

  const getCourseDescription = (course: Course) => {
    return language === 'ar' && course.description_ar ? course.description_ar : course.description;
  };

  const getSemesterName = (semester: string) => {
    const semesters: Record<string, { en: string; ar: string }> = {
      fall: { en: 'Fall', ar: 'الخريف' },
      spring: { en: 'Spring', ar: 'الربيع' },
      summer: { en: 'Summer', ar: 'الصيف' },
    };
    return language === 'ar' ? semesters[semester]?.ar || semester : semesters[semester]?.en || semester;
  };

  const getEnrollmentPercentage = (enrolled: number, max: number) => {
    return max > 0 ? (enrolled / max) * 100 : 0;
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'enrolled':
      case 'waitlisted':
        return currentTheme.colors.success;
      case 'completed':
        return currentTheme.colors.primary;
      case 'failed':
        return currentTheme.colors.error;
      case 'retakeable':
        return currentTheme.colors.warning;
      case 'available':
        return currentTheme.colors.info;
      default:
        return currentTheme.colors.textSecondary;
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'enrolled':
        return t('courses.status.enrolled', 'Enrolled');
      case 'waitlisted':
        return t('courses.status.waitlisted', 'Waitlisted');
      case 'completed':
        return t('courses.status.completed', 'Completed');
      case 'failed':
        return t('courses.status.failed', 'Failed');
      case 'retakeable':
        return t('courses.status.retakeable', 'Retakeable');
      case 'available':
        return t('courses.status.available', 'Available');
      default:
        return t('courses.status.unknown', 'Unknown');
    }
  };

  const enrollmentPercentage = getEnrollmentPercentage(
    course.enrolled_students_count,
    course.max_students
  );

  return (
    <TouchableOpacity
      style={[glassStyles.card, styles.courseCard]}
      onPress={() => onPress(course)}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={theme === 'dark'
          ? ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
          : ['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.7)']
        }
        style={styles.cardGradient}
      >
        {/* Header */}
        <View style={styles.cardHeader}>
          <View style={styles.courseCodeContainer}>
            <Text style={[styles.courseCode, { color: currentTheme.colors.primary }]}>
              {course.code}
            </Text>
            {course.user_enrollment?.is_retake && course.user_enrollment.attempt_display && (
              <View style={[
                styles.retakeBadge,
                { backgroundColor: currentTheme.colors.warning }
              ]}>
                <Text style={[
                  styles.retakeText,
                  { color: currentTheme.colors.surface }
                ]}>
                  {course.user_enrollment.attempt_display}
                </Text>
              </View>
            )}
          </View>
          
          {course.enrollment_status && (
            <View style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(course.enrollment_status) + '20' }
            ]}>
              <Text style={[
                styles.statusText,
                { color: getStatusColor(course.enrollment_status) }
              ]}>
                {getStatusText(course.enrollment_status)}
              </Text>
            </View>
          )}
        </View>

        {/* Course Title */}
        <Text style={[styles.courseTitle, { color: currentTheme.colors.text }]} numberOfLines={2}>
          {getCourseName(course)}
        </Text>

        {/* Course Info */}
        <View style={styles.courseInfo}>
          <View style={styles.infoRow}>
            <Ionicons name="person-outline" size={14} color={currentTheme.colors.textSecondary} />
            <Text style={[styles.infoText, { color: currentTheme.colors.textSecondary }]} numberOfLines={1}>
              {course.instructor_name}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="calendar-outline" size={14} color={currentTheme.colors.textSecondary} />
            <Text style={[styles.infoText, { color: currentTheme.colors.textSecondary }]}>
              {getSemesterName(course.semester)} {course.year}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="school-outline" size={14} color={currentTheme.colors.textSecondary} />
            <Text style={[styles.infoText, { color: currentTheme.colors.textSecondary }]}>
              {course.credit_hours} {t('courses.credits', 'credits')}
            </Text>
          </View>
        </View>

        {/* Enrollment Progress */}
        <View style={styles.enrollmentProgress}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressText, { color: currentTheme.colors.textSecondary }]}>
              {course.enrolled_students_count}/{course.max_students} {t('courses.students', 'students')}
            </Text>
            <Text style={[styles.progressPercentage, { color: currentTheme.colors.textSecondary }]}>
              {Math.round(enrollmentPercentage)}%
            </Text>
          </View>
          
          <View style={[styles.progressBar, { backgroundColor: currentTheme.colors.border }]}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${enrollmentPercentage}%`,
                  backgroundColor: enrollmentPercentage > 80 
                    ? currentTheme.colors.error 
                    : enrollmentPercentage > 60 
                    ? currentTheme.colors.warning 
                    : currentTheme.colors.success
                }
              ]}
            />
          </View>
        </View>

        {/* Enhanced Action Button */}
        {onEnrollmentPress && (course.enrollment_status === 'available' || course.enrollment_status === 'retakeable') && (
          <TouchableOpacity
            style={[
              styles.enrollButton,
              {
                backgroundColor: course.enrollment_status === 'retakeable'
                  ? currentTheme.colors.warning
                  : currentTheme.colors.primary
              }
            ]}
            onPress={() => onEnrollmentPress(course)}
            activeOpacity={0.8}
          >
            <Ionicons
              name={course.enrollment_status === 'retakeable' ? "refresh-outline" : "add-circle-outline"}
              size={16}
              color={currentTheme.colors.surface}
            />
            <Text style={[styles.enrollButtonText, { color: currentTheme.colors.surface }]}>
              {course.enrollment_status === 'retakeable'
                ? t('courses.retake', 'Retake')
                : t('courses.enroll', 'Enroll')
              }
            </Text>
          </TouchableOpacity>
        )}

        {/* Enhanced Features Indicator */}
        {course.enrollment_status === 'enrolled' && (
          <View style={styles.enhancedFeatures}>
            <View style={[styles.featureIndicator, { backgroundColor: currentTheme.colors.success + '20' }]}>
              <Ionicons name="download-outline" size={12} color={currentTheme.colors.success} />
              <Text style={[styles.featureText, { color: currentTheme.colors.success }]}>
                {t('courses.offline', 'Offline')}
              </Text>
            </View>
            <View style={[styles.featureIndicator, { backgroundColor: currentTheme.colors.primary + '20' }]}>
              <Ionicons name="notifications-outline" size={12} color={currentTheme.colors.primary} />
              <Text style={[styles.featureText, { color: currentTheme.colors.primary }]}>
                {t('courses.notifications', 'Alerts')}
              </Text>
            </View>
          </View>
        )}
      </LinearGradient>
    </TouchableOpacity>
  );
});

CourseCard.displayName = 'CourseCard';

const createStyles = (theme: any) => StyleSheet.create({
  courseCard: {
    width: CARD_WIDTH,
    marginBottom: 16,
    overflow: 'hidden',
  },
  cardGradient: {
    padding: 16,
    borderRadius: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  courseCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  courseCode: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  retakeBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  retakeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  courseTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
    lineHeight: 20,
  },
  courseInfo: {
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  infoText: {
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
  enrollmentProgress: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  progressText: {
    fontSize: 11,
  },
  progressPercentage: {
    fontSize: 11,
    fontWeight: '600',
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  enrollButton: {
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  enrollButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  enhancedFeatures: {
    flexDirection: 'row',
    gap: 6,
    marginTop: 8,
  },
  featureIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 6,
    gap: 3,
  },
  featureText: {
    fontSize: 9,
    fontWeight: '600',
  },
});

export default CourseCard;
