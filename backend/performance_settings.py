"""
Performance Testing Settings
Development-friendly configuration for performance testing
"""

from backend.settings import *

# Override caching to use dummy cache for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'performance-testing',
        'OPTIONS': {
            'MAX_ENTRIES': 10000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# Disable Redis-specific cache invalidation
CACHE_INVALIDATION_ENABLED = False

# Performance testing specific settings
PERFORMANCE_TESTING = True

# Disable unnecessary middleware for performance testing
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
]

# Optimize database for testing
DATABASES['default']['OPTIONS'] = {
    'timeout': 20,
    'check_same_thread': False,
}

# Logging configuration for performance testing
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'level': 'WARNING',  # Reduce log noise during performance testing
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'performance_test.log',
            'formatter': 'verbose',
            'level': 'INFO',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'courses': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'performance': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
}

# Email backend for testing (don't send real emails)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Security settings for testing
SECRET_KEY = 'performance-testing-key-not-for-production'
DEBUG = True
ALLOWED_HOSTS = ['*']

# Performance monitoring
PERFORMANCE_MONITORING = {
    'ENABLE_QUERY_LOGGING': True,
    'ENABLE_CACHE_MONITORING': True,
    'ENABLE_MEMORY_MONITORING': True,
}

print("🔧 Performance testing settings loaded - using local memory cache")
