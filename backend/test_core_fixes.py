#!/usr/bin/env python
"""
Test core enrollment fixes without migration dependencies
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from courses.models import Course, Enrollment, Department
from courses.serializers import CourseSerializer
from django.test import RequestFactory

User = get_user_model()

def test_core_fixes():
    """Test the core enrollment fixes"""
    print("🧪 Testing Core Enrollment Fixes...")
    
    # Clean up
    User.objects.filter(username__startswith='core_test_').delete()
    Department.objects.filter(code='CORE').delete()
    
    try:
        # Create test data
        department = Department.objects.create(name="Core Test", code="CORE")
        teacher = User.objects.create_user(
            username="core_test_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        student = User.objects.create_user(
            username="core_test_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        course = Course.objects.create(
            title="Core Test Course", code="CORE101", description="Test",
            department=department, instructor=teacher, level="undergraduate", 
            credit_hours=3, semester="spring", year=2025, max_students=30, 
            is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
        
        print("✅ Test data created")
        
        # Test 1: Enrollment Lifecycle Management
        print("\n🔍 Test 1: Enrollment Lifecycle Management")
        
        # Initial enrollment
        enrollment1, _ = course.enroll_student(student)
        assert enrollment1.is_active == True
        assert enrollment1.status == 'enrolled'
        assert enrollment1.attempt_number == 1
        print("  ✅ Initial enrollment works")
        
        # Simulate failure
        enrollment1.status = 'failed'
        enrollment1.is_active = False
        enrollment1.save()
        
        # Retake enrollment
        enrollment2, _ = course.enroll_student(student)
        assert enrollment2.is_active == True
        assert enrollment2.status == 'enrolled'
        assert enrollment2.attempt_number == 2
        
        # Verify only one active enrollment
        active_count = Enrollment.objects.filter(
            student=student, course=course, is_active=True
        ).count()
        assert active_count == 1
        print("  ✅ Enrollment lifecycle management works")
        
        # Test 2: Status Determination
        print("\n🔍 Test 2: Status Determination")
        
        factory = RequestFactory()
        request = factory.get('/')
        request.user = student
        
        serializer = CourseSerializer(course, context={'request': request})
        status = serializer.get_user_enrollment_status(course)
        assert status == 'enrolled'
        print("  ✅ Active enrollment status determination works")
        
        # Test retakeable status
        enrollment2.status = 'failed'
        enrollment2.is_active = False
        enrollment2.save()
        
        status = serializer.get_user_enrollment_status(course)
        assert status == 'retakeable'
        print("  ✅ Retakeable status determination works")
        
        # Test 3: Can Enroll Logic
        print("\n🔍 Test 3: Can Enroll Logic")
        
        can_enroll, message = course.can_enroll(student)
        assert can_enroll == True
        print("  ✅ Can enroll logic works for retakes")
        
        # Test 4: Retake Limit
        print("\n🔍 Test 4: Retake Limit Enforcement")
        
        # Create third failed attempt
        enrollment3, _ = course.enroll_student(student)
        enrollment3.status = 'failed'
        enrollment3.is_active = False
        enrollment3.save()
        
        # Now we have 3 failed attempts, should block 4th
        can_enroll, message = course.can_enroll(student)
        assert can_enroll == False
        assert "Maximum retake attempts exceeded" in message
        print("  ✅ Retake limit enforcement works")
        
        # Test 5: Enrollment Message Logic
        print("\n🔍 Test 5: Enrollment Message Logic")
        
        message = serializer.get_user_enrollment_message(course)
        assert "Maximum retake attempts exceeded" in message
        print("  ✅ Enrollment message logic works")
        
        print("\n🎉 ALL CORE TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        User.objects.filter(username__startswith='core_test_').delete()
        Department.objects.filter(code='CORE').delete()

if __name__ == "__main__":
    success = test_core_fixes()
    sys.exit(0 if success else 1)
