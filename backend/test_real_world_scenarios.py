#!/usr/bin/env python
"""
Test real-world enrollment scenarios
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import RequestFactory
from courses.models import Course, Enrollment, Department
from courses.serializers import CourseSerializer

User = get_user_model()

def test_real_world_scenarios():
    """Test real-world enrollment scenarios"""
    print("🧪 Testing Real-World Enrollment Scenarios...")
    
    # Clean up
    User.objects.filter(username__startswith='real_test_').delete()
    Department.objects.filter(code='REAL').delete()
    
    try:
        # Create test data
        department = Department.objects.create(name="Real Test", code="REAL")
        teacher = User.objects.create_user(
            username="real_test_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        
        # Create multiple students for different scenarios
        students = []
        for i in range(5):
            student = User.objects.create_user(
                username=f"real_test_student{i}", email=f"student{i}@test.com", 
                password="password123", role="student"
            )
            students.append(student)
        
        course = Course.objects.create(
            title="Real Test Course", code="REAL101", description="Test",
            department=department, instructor=teacher, level="undergraduate", 
            credit_hours=3, semester="spring", year=2025, max_students=30, 
            is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
        
        print("✅ Test data created")
        
        # Scenario 1: Normal enrollment and completion
        print("\n🔍 Scenario 1: Normal Enrollment and Completion")
        
        student1 = students[0]
        enrollment1, _ = course.enroll_student(student1)
        
        # Complete the course
        enrollment1.status = 'completed'
        enrollment1.is_active = False
        enrollment1.save()
        
        # Check status
        factory = RequestFactory()
        request = factory.get('/')
        request.user = student1
        
        serializer = CourseSerializer(course, context={'request': request})
        status = serializer.get_user_enrollment_status(course)
        
        assert status == 'completed'
        print("  ✅ Student completed course successfully")
        
        # Scenario 2: Student fails and retakes successfully
        print("\n🔍 Scenario 2: Failure and Successful Retake")
        
        student2 = students[1]
        
        # First attempt - fail
        enrollment2a, _ = course.enroll_student(student2)
        enrollment2a.status = 'failed'
        enrollment2a.is_active = False
        enrollment2a.save()
        
        # Second attempt - pass
        enrollment2b, _ = course.enroll_student(student2)
        enrollment2b.status = 'completed'
        enrollment2b.is_active = False
        enrollment2b.save()
        
        # Check final status
        request.user = student2
        serializer = CourseSerializer(course, context={'request': request})
        status = serializer.get_user_enrollment_status(course)
        
        assert status == 'completed'
        assert enrollment2b.attempt_number == 2
        print("  ✅ Student failed first attempt, passed on retake")
        
        # Scenario 3: Student drops and re-enrolls
        print("\n🔍 Scenario 3: Drop and Re-enrollment")
        
        student3 = students[2]
        
        # First enrollment - drop
        enrollment3a, _ = course.enroll_student(student3)
        enrollment3a.status = 'dropped'
        enrollment3a.is_active = False
        enrollment3a.save()
        
        # Re-enroll
        enrollment3b, _ = course.enroll_student(student3)
        
        # Check status
        request.user = student3
        serializer = CourseSerializer(course, context={'request': request})
        status = serializer.get_user_enrollment_status(course)
        
        assert status == 'enrolled'
        assert enrollment3b.attempt_number == 2
        print("  ✅ Student dropped and re-enrolled successfully")
        
        # Scenario 4: Multiple failures leading to limit
        print("\n🔍 Scenario 4: Multiple Failures and Limit")
        
        student4 = students[3]
        
        # Three failed attempts
        for attempt in range(3):
            enrollment, _ = course.enroll_student(student4)
            enrollment.status = 'failed'
            enrollment.is_active = False
            enrollment.save()
            print(f"    Failed attempt {attempt + 1}")
        
        # Try to enroll again - should be blocked
        can_enroll, message = course.can_enroll(student4)
        assert not can_enroll
        assert "Maximum retake attempts exceeded" in message
        
        # Check status
        request.user = student4
        serializer = CourseSerializer(course, context={'request': request})
        status = serializer.get_user_enrollment_status(course)
        
        assert status == 'retake_limit_exceeded'
        print("  ✅ Student blocked after 3 failed attempts")
        
        # Scenario 5: Currently enrolled student
        print("\n🔍 Scenario 5: Currently Enrolled Student")
        
        student5 = students[4]
        enrollment5, _ = course.enroll_student(student5)
        
        # Check status
        request.user = student5
        serializer = CourseSerializer(course, context={'request': request})
        status = serializer.get_user_enrollment_status(course)
        can_enroll = serializer.get_user_can_enroll(course)
        
        assert status == 'enrolled'
        assert not can_enroll
        print("  ✅ Currently enrolled student shows correct status")
        
        # Scenario 6: Enrollment history tracking
        print("\n🔍 Scenario 6: Enrollment History Tracking")
        
        # Check student2's history (had 2 attempts)
        history = Enrollment.objects.filter(
            student=student2, course=course
        ).order_by('attempt_number')
        
        assert history.count() == 2
        assert history[0].attempt_number == 1
        assert history[0].status == 'failed'
        assert history[1].attempt_number == 2
        assert history[1].status == 'completed'
        print("  ✅ Enrollment history tracked correctly")
        
        # Scenario 7: Active enrollment management
        print("\n🔍 Scenario 7: Active Enrollment Management")
        
        # Check that only one active enrollment exists per student
        for student in students:
            active_count = Enrollment.objects.filter(
                student=student, course=course, is_active=True
            ).count()
            assert active_count <= 1, f"Student {student.username} has {active_count} active enrollments"
        
        print("  ✅ Active enrollment management working correctly")
        
        # Scenario 8: Status message accuracy
        print("\n🔍 Scenario 8: Status Message Accuracy")
        
        # Test different status messages
        test_cases = [
            (student1, 'completed', 'Already completed'),
            (student2, 'completed', 'Already completed'),
            (student3, 'enrolled', 'Already enrolled'),
            (student4, 'retake_limit_exceeded', 'Maximum retake attempts exceeded'),
            (student5, 'enrolled', 'Already enrolled')
        ]
        
        for student, expected_status, expected_message_part in test_cases:
            request.user = student
            serializer = CourseSerializer(course, context={'request': request})
            status = serializer.get_user_enrollment_status(course)
            message = serializer.get_user_enrollment_message(course)
            
            assert status == expected_status, f"Expected {expected_status}, got {status} for {student.username}"
            assert expected_message_part in message, f"Expected '{expected_message_part}' in message '{message}'"
        
        print("  ✅ Status messages are accurate")
        
        print("\n🎉 ALL REAL-WORLD SCENARIOS PASSED!")
        print("\n📊 Summary:")
        print(f"  • {len(students)} students tested")
        print(f"  • {Enrollment.objects.filter(course=course).count()} total enrollments created")
        print(f"  • Multiple enrollment statuses validated")
        print(f"  • Retake limits enforced correctly")
        print(f"  • Enrollment history tracked properly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        User.objects.filter(username__startswith='real_test_').delete()
        Department.objects.filter(code='REAL').delete()

if __name__ == "__main__":
    success = test_real_world_scenarios()
    sys.exit(0 if success else 1)
