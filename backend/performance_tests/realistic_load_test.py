#!/usr/bin/env python
"""
REALISTIC UNIVERSITY ENROLLMENT LOAD TESTING
Simulates real university enrollment scenarios with peak registration periods
"""

import os
import sys
import django
import time
import threading
import concurrent.futures
import random
import statistics
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Tuple, Any

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from django.db import transaction
from courses.models import Course, Enrollment, Department
from courses.enrollment_security import EnrollmentSecurityManager

User = get_user_model()


@dataclass
class LoadTestScenario:
    """Load test scenario configuration"""
    name: str
    total_students: int
    concurrent_users: int
    duration_minutes: int
    operations_per_user: int
    course_capacity_range: Tuple[int, int]
    popular_course_ratio: float  # Percentage of students targeting popular courses


@dataclass
class LoadTestResults:
    """Results from load testing"""
    scenario_name: str
    total_operations: int
    successful_operations: int
    failed_operations: int
    total_duration: float
    operations_per_second: float
    average_response_time: float
    p95_response_time: float
    p99_response_time: float
    error_rate: float
    peak_concurrent_users: int
    database_connections_used: int
    memory_usage_mb: float
    errors_by_type: Dict[str, int]


class RealisticLoadTester:
    """Realistic load testing for university enrollment scenarios"""
    
    def __init__(self):
        self.results = []
        self.test_data = {}
        self.active_threads = 0
        self.lock = threading.Lock()
        
    def setup_university_scenario(self, num_students=2000, num_courses=200):
        """Setup realistic university enrollment scenario"""
        print(f"🏫 Setting up university scenario: {num_students} students, {num_courses} courses")
        
        # Clean up existing data
        User.objects.filter(username__startswith='load_test_').delete()
        Department.objects.filter(code__startswith='LOAD').delete()
        cache.clear()
        
        # Create departments
        departments = []
        dept_names = ['Computer Science', 'Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'History']
        
        for i, dept_name in enumerate(dept_names):
            departments.append(Department.objects.create(
                name=dept_name,
                code=f"LOAD{i:02d}"
            ))
        
        # Create instructors
        instructors = []
        for i in range(20):  # 20 instructors
            instructors.append(User.objects.create_user(
                username=f"load_test_instructor_{i:03d}",
                email=f"instructor_{i:03d}@university.edu",
                password="password123",
                role="teacher"
            ))
        
        # Create students in batches
        print("  👥 Creating students...")
        student_batches = []
        batch_size = 100
        
        for batch_start in range(0, num_students, batch_size):
            batch_end = min(batch_start + batch_size, num_students)
            batch = []
            
            for i in range(batch_start, batch_end):
                batch.append(User(
                    username=f"load_test_student_{i:05d}",
                    email=f"student_{i:05d}@university.edu",
                    password="password123",
                    role="student"
                ))
            
            User.objects.bulk_create(batch, batch_size=50)
            student_batches.append(batch)
            
            if (batch_end // batch_size) % 5 == 0:
                print(f"    📊 Created {batch_end}/{num_students} students")
        
        students = User.objects.filter(username__startswith='load_test_student_')
        
        # Create courses with realistic distribution
        print("  📚 Creating courses...")
        courses = []
        course_types = [
            ('Introductory', 100, 150),  # Large intro classes
            ('Intermediate', 30, 50),    # Medium classes
            ('Advanced', 15, 25),        # Small advanced classes
            ('Seminar', 8, 12)          # Very small seminars
        ]
        
        course_counter = 0
        for dept in departments:
            for course_type, min_cap, max_cap in course_types:
                # Create multiple courses of each type per department
                courses_per_type = num_courses // (len(departments) * len(course_types))
                
                for i in range(courses_per_type):
                    capacity = random.randint(min_cap, max_cap)
                    instructor = random.choice(instructors)
                    
                    courses.append(Course(
                        title=f"{course_type} {dept.name} {i+1}",
                        code=f"{dept.code}{course_counter:03d}",
                        description=f"{course_type} course in {dept.name}",
                        department=dept,
                        instructor=instructor,
                        level="undergraduate" if course_type in ['Introductory', 'Intermediate'] else "graduate",
                        credit_hours=random.choice([3, 4]),
                        semester="spring",
                        year=2025,
                        max_students=capacity,
                        is_active=True,
                        is_published=True,
                        start_date=timezone.now().date(),
                        end_date=timezone.now().date() + timedelta(days=120)
                    ))
                    course_counter += 1
        
        # Bulk create courses
        Course.objects.bulk_create(courses, batch_size=50)
        courses = Course.objects.filter(code__startswith='LOAD')
        
        # Identify popular courses (large capacity, intro level)
        popular_courses = courses.filter(
            max_students__gte=80,
            level='undergraduate'
        )[:20]  # Top 20 popular courses
        
        self.test_data = {
            'students': list(students),
            'courses': list(courses),
            'popular_courses': list(popular_courses),
            'departments': departments,
            'instructors': instructors
        }
        
        print(f"✅ University scenario created:")
        print(f"    👥 Students: {len(students)}")
        print(f"    📚 Courses: {len(courses)}")
        print(f"    🔥 Popular courses: {len(popular_courses)}")
        print(f"    🏢 Departments: {len(departments)}")
    
    def simulate_peak_registration_period(self, scenario: LoadTestScenario):
        """Simulate peak registration period with realistic user behavior"""
        print(f"\n🚀 Simulating: {scenario.name}")
        print(f"  👥 {scenario.total_students} students, {scenario.concurrent_users} concurrent")
        print(f"  ⏱️  Duration: {scenario.duration_minutes} minutes")
        
        students = random.sample(self.test_data['students'], scenario.total_students)
        all_courses = self.test_data['courses']
        popular_courses = self.test_data['popular_courses']
        
        # Track metrics
        operation_times = []
        successful_ops = 0
        failed_ops = 0
        errors_by_type = {}
        
        def simulate_student_behavior(student):
            """Simulate realistic student enrollment behavior"""
            operations_completed = 0
            student_times = []
            
            for _ in range(scenario.operations_per_user):
                start_time = time.time()
                
                try:
                    # 70% chance to target popular courses, 30% random courses
                    if random.random() < scenario.popular_course_ratio:
                        target_courses = popular_courses
                    else:
                        target_courses = all_courses
                    
                    course = random.choice(target_courses)
                    
                    # Simulate different operations
                    operation = random.choices(
                        ['enroll', 'check_status', 'validate'],
                        weights=[60, 25, 15]  # 60% enrollments, 25% status checks, 15% validations
                    )[0]
                    
                    if operation == 'enroll':
                        enrollment, waitlist = course.enroll_student(student)
                        success = enrollment is not None or waitlist is not None
                    elif operation == 'check_status':
                        can_enroll, message = course.can_enroll(student)
                        success = True  # Status check always succeeds
                    else:  # validate
                        security_manager = EnrollmentSecurityManager(student, course)
                        result = security_manager.validate_enrollment()
                        success = True  # Validation always succeeds
                    
                    response_time = time.time() - start_time
                    student_times.append(response_time)
                    
                    with self.lock:
                        operation_times.append(response_time)
                        if success:
                            nonlocal successful_ops
                            successful_ops += 1
                        else:
                            nonlocal failed_ops
                            failed_ops += 1
                    
                    operations_completed += 1
                    
                    # Realistic delay between operations (1-5 seconds)
                    time.sleep(random.uniform(1, 5))
                    
                except Exception as e:
                    error_type = type(e).__name__
                    with self.lock:
                        nonlocal errors_by_type
                        errors_by_type[error_type] = errors_by_type.get(error_type, 0) + 1
                        failed_ops += 1
                        operation_times.append(time.time() - start_time)
            
            return operations_completed, student_times
        
        # Execute load test
        start_time = time.time()
        
        # Split students into batches for concurrent execution
        batch_size = scenario.concurrent_users
        student_batches = [
            students[i:i + batch_size] 
            for i in range(0, len(students), batch_size)
        ]
        
        total_operations_completed = 0
        
        for batch_num, batch in enumerate(student_batches):
            print(f"  📊 Processing batch {batch_num + 1}/{len(student_batches)} ({len(batch)} students)")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=scenario.concurrent_users) as executor:
                futures = [executor.submit(simulate_student_behavior, student) for student in batch]
                batch_results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            batch_ops = sum(result[0] for result in batch_results)
            total_operations_completed += batch_ops
            
            # Brief pause between batches to simulate realistic load distribution
            if batch_num < len(student_batches) - 1:
                time.sleep(2)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Calculate metrics
        total_operations = successful_ops + failed_ops
        ops_per_second = total_operations / total_duration if total_duration > 0 else 0
        error_rate = (failed_ops / total_operations * 100) if total_operations > 0 else 0
        
        avg_response_time = statistics.mean(operation_times) if operation_times else 0
        p95_response_time = statistics.quantiles(operation_times, n=20)[18] if len(operation_times) >= 20 else 0
        p99_response_time = statistics.quantiles(operation_times, n=100)[98] if len(operation_times) >= 100 else 0
        
        result = LoadTestResults(
            scenario_name=scenario.name,
            total_operations=total_operations,
            successful_operations=successful_ops,
            failed_operations=failed_ops,
            total_duration=total_duration,
            operations_per_second=ops_per_second,
            average_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            error_rate=error_rate,
            peak_concurrent_users=scenario.concurrent_users,
            database_connections_used=0,  # Would need monitoring to get this
            memory_usage_mb=0,  # Would need monitoring to get this
            errors_by_type=errors_by_type
        )
        
        self.results.append(result)
        self.print_load_test_result(result)
        return result
    
    def print_load_test_result(self, result: LoadTestResults):
        """Print load test results"""
        print(f"\n📊 {result.scenario_name} Results:")
        print(f"  🔢 Total Operations: {result.total_operations:,}")
        print(f"  ✅ Successful: {result.successful_operations:,} ({100-result.error_rate:.1f}%)")
        print(f"  ❌ Failed: {result.failed_operations:,} ({result.error_rate:.1f}%)")
        print(f"  ⏱️  Total Duration: {result.total_duration:.1f}s")
        print(f"  ⚡ Operations/Second: {result.operations_per_second:.1f}")
        print(f"  📈 Avg Response Time: {result.average_response_time*1000:.2f}ms")
        print(f"  📊 P95 Response Time: {result.p95_response_time*1000:.2f}ms")
        print(f"  📊 P99 Response Time: {result.p99_response_time*1000:.2f}ms")
        print(f"  🧵 Peak Concurrent Users: {result.peak_concurrent_users}")
        
        if result.errors_by_type:
            print(f"  ⚠️  Error Types:")
            for error_type, count in result.errors_by_type.items():
                print(f"    - {error_type}: {count}")
    
    def run_realistic_scenarios(self):
        """Run multiple realistic enrollment scenarios"""
        scenarios = [
            LoadTestScenario(
                name="Light Load - Regular Day",
                total_students=200,
                concurrent_users=10,
                duration_minutes=5,
                operations_per_user=3,
                course_capacity_range=(20, 100),
                popular_course_ratio=0.3
            ),
            LoadTestScenario(
                name="Medium Load - Registration Opens",
                total_students=500,
                concurrent_users=25,
                duration_minutes=10,
                operations_per_user=5,
                course_capacity_range=(20, 100),
                popular_course_ratio=0.7
            ),
            LoadTestScenario(
                name="Heavy Load - Peak Registration",
                total_students=800,
                concurrent_users=50,
                duration_minutes=15,
                operations_per_user=4,
                course_capacity_range=(20, 100),
                popular_course_ratio=0.8
            )
        ]
        
        for scenario in scenarios:
            self.simulate_peak_registration_period(scenario)
            
            # Brief cooldown between scenarios
            print("  💤 Cooldown period...")
            time.sleep(10)
    
    def generate_load_test_report(self):
        """Generate comprehensive load test report"""
        print("\n" + "="*80)
        print("🚀 COMPREHENSIVE REALISTIC LOAD TEST REPORT")
        print("="*80)
        
        for result in self.results:
            self.print_load_test_result(result)
        
        print("\n🎯 LOAD TESTING SUMMARY:")
        
        # Overall statistics
        total_ops = sum(r.total_operations for r in self.results)
        total_successful = sum(r.successful_operations for r in self.results)
        total_duration = sum(r.total_duration for r in self.results)
        
        print(f"  📈 Total Operations Across All Tests: {total_ops:,}")
        print(f"  ✅ Overall Success Rate: {(total_successful/total_ops*100):.1f}%")
        print(f"  ⏱️  Total Testing Duration: {total_duration:.1f}s")
        
        # Performance analysis
        avg_ops_per_second = [r.operations_per_second for r in self.results]
        avg_response_times = [r.average_response_time for r in self.results]
        
        if avg_ops_per_second:
            print(f"  ⚡ Average Throughput: {statistics.mean(avg_ops_per_second):.1f} ops/sec")
            print(f"  📊 Peak Throughput: {max(avg_ops_per_second):.1f} ops/sec")
        
        if avg_response_times:
            print(f"  📈 Average Response Time: {statistics.mean(avg_response_times)*1000:.2f}ms")
        
        print("\n💡 PERFORMANCE RECOMMENDATIONS:")
        
        # Analyze results and provide recommendations
        high_error_rate = any(r.error_rate > 5 for r in self.results)
        slow_response = any(r.average_response_time > 1.0 for r in self.results)
        low_throughput = any(r.operations_per_second < 10 for r in self.results)
        
        if high_error_rate:
            print("  ⚠️  High error rates detected - investigate error handling")
        if slow_response:
            print("  ⚠️  Slow response times - consider performance optimization")
        if low_throughput:
            print("  ⚠️  Low throughput - consider scaling or optimization")
        
        if not (high_error_rate or slow_response or low_throughput):
            print("  ✅ Excellent performance across all scenarios!")
            print("  ✅ System handles realistic university enrollment loads well")
    
    def cleanup_load_test_data(self):
        """Clean up load test data"""
        print("\n🧹 Cleaning up load test data...")
        User.objects.filter(username__startswith='load_test_').delete()
        Department.objects.filter(code__startswith='LOAD').delete()
        cache.clear()


def run_realistic_load_tests():
    """Run realistic university enrollment load tests"""
    print("🚀 STARTING REALISTIC UNIVERSITY ENROLLMENT LOAD TESTING")
    print("="*80)
    
    tester = RealisticLoadTester()
    
    try:
        # Setup university scenario
        tester.setup_university_scenario(num_students=1000, num_courses=100)
        
        # Run realistic scenarios
        tester.run_realistic_scenarios()
        
        # Generate comprehensive report
        tester.generate_load_test_report()
        
        return True
        
    except Exception as e:
        print(f"❌ Load testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        tester.cleanup_load_test_data()


if __name__ == "__main__":
    success = run_realistic_load_tests()
    sys.exit(0 if success else 1)
