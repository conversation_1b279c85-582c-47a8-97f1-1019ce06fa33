#!/usr/bin/env python
"""
CONCURRENT ENROLLMENT TESTING SUITE
Tests race conditions, simultaneous enrollments, and system behavior under concurrent load
"""

import os
import sys
import django
import time
import threading
import concurrent.futures
import random
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Tuple, Any

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from django.db import transaction, connection
from django.test.utils import override_settings
from courses.models import Course, Enrollment, Department
from courses.enrollment_security import EnrollmentSecurityManager

User = get_user_model()


@dataclass
class ConcurrentTestResult:
    """Results from concurrent enrollment testing"""
    test_name: str
    total_attempts: int
    successful_enrollments: int
    failed_enrollments: int
    race_condition_detected: bool
    data_integrity_maintained: bool
    average_response_time: float
    max_response_time: float
    min_response_time: float
    concurrent_threads: int
    errors: List[str]


class ConcurrentEnrollmentTester:
    """Test concurrent enrollment scenarios and race conditions"""
    
    def __init__(self):
        self.results = []
        self.test_data = {}
        self.lock = threading.Lock()
        
    def setup_concurrent_test_data(self, num_students=100, num_courses=10):
        """Setup test data for concurrent testing"""
        print(f"🔧 Setting up concurrent test data: {num_students} students, {num_courses} courses")
        
        # Clean up existing data
        User.objects.filter(username__startswith='concurrent_test_').delete()
        Department.objects.filter(code='CONCURRENT').delete()
        cache.clear()
        
        # Create department and instructor
        department = Department.objects.create(
            name="Concurrent Test Department",
            code="CONCURRENT"
        )
        
        instructor = User.objects.create_user(
            username="concurrent_test_instructor",
            email="<EMAIL>",
            password="password123",
            role="teacher"
        )
        
        # Create students
        students = []
        for i in range(num_students):
            students.append(User(
                username=f"concurrent_test_student_{i:04d}",
                email=f"student_{i:04d}@concurrent.com",
                password="password123",
                role="student"
            ))
        
        User.objects.bulk_create(students, batch_size=50)
        students = User.objects.filter(username__startswith='concurrent_test_student_')
        
        # Create courses with limited capacity
        courses = []
        for i in range(num_courses):
            courses.append(Course(
                title=f"Concurrent Test Course {i:02d}",
                code=f"CONC{i:02d}",
                description=f"Concurrent test course {i}",
                department=department,
                instructor=instructor,
                level="undergraduate",
                credit_hours=3,
                semester="spring",
                year=2025,
                max_students=20,  # Limited capacity to test race conditions
                is_active=True,
                is_published=True,
                start_date=timezone.now().date(),
                end_date=timezone.now().date() + timedelta(days=90)
            ))
        
        Course.objects.bulk_create(courses, batch_size=20)
        courses = Course.objects.filter(code__startswith='CONC')
        
        self.test_data = {
            'students': list(students),
            'courses': list(courses),
            'department': department,
            'instructor': instructor
        }
        
        print(f"✅ Concurrent test data created: {len(students)} students, {len(courses)} courses")
    
    def test_race_condition_enrollment(self, num_threads=20, course_capacity=10):
        """Test race conditions when multiple students try to enroll simultaneously"""
        print(f"\n🏁 Testing Race Condition Enrollment ({num_threads} threads, capacity: {course_capacity})")
        
        # Select a course and modify its capacity
        course = self.test_data['courses'][0]
        course.max_students = course_capacity
        course.save()
        
        # Select students (more than capacity)
        students = self.test_data['students'][:num_threads]
        
        enrollment_results = []
        response_times = []
        errors = []
        
        def attempt_enrollment(student):
            """Attempt to enroll a student"""
            start_time = time.time()
            try:
                with transaction.atomic():
                    enrollment, waitlist = course.enroll_student(student)
                    result = {
                        'student_id': student.id,
                        'success': enrollment is not None,
                        'waitlisted': waitlist is not None,
                        'response_time': time.time() - start_time
                    }
                    
                    with self.lock:
                        enrollment_results.append(result)
                        response_times.append(result['response_time'])
                    
                    return result
                    
            except Exception as e:
                error_msg = f"Student {student.id}: {str(e)}"
                with self.lock:
                    errors.append(error_msg)
                    response_times.append(time.time() - start_time)
                
                return {
                    'student_id': student.id,
                    'success': False,
                    'waitlisted': False,
                    'error': str(e),
                    'response_time': time.time() - start_time
                }
        
        # Execute concurrent enrollments
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(attempt_enrollment, student) for student in students]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        
        # Analyze results
        successful_enrollments = sum(1 for r in enrollment_results if r['success'])
        failed_enrollments = len(enrollment_results) - successful_enrollments
        
        # Check data integrity
        actual_enrollments = Enrollment.objects.filter(
            course=course,
            is_active=True,
            status='enrolled'
        ).count()
        
        data_integrity_maintained = actual_enrollments <= course_capacity
        race_condition_detected = actual_enrollments != successful_enrollments
        
        result = ConcurrentTestResult(
            test_name="Race Condition Enrollment",
            total_attempts=num_threads,
            successful_enrollments=successful_enrollments,
            failed_enrollments=failed_enrollments,
            race_condition_detected=race_condition_detected,
            data_integrity_maintained=data_integrity_maintained,
            average_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            concurrent_threads=num_threads,
            errors=errors
        )
        
        self.results.append(result)
        self.print_concurrent_result(result)
        
        print(f"  📊 Actual enrollments in DB: {actual_enrollments}")
        print(f"  📊 Course capacity: {course_capacity}")
        
        return result
    
    def test_concurrent_security_validation(self, num_threads=15):
        """Test security validation under concurrent load"""
        print(f"\n🔒 Testing Concurrent Security Validation ({num_threads} threads)")
        
        course = self.test_data['courses'][1]
        students = self.test_data['students'][:num_threads]
        
        validation_results = []
        response_times = []
        errors = []
        
        def validate_enrollment(student):
            """Validate enrollment for a student"""
            start_time = time.time()
            try:
                security_manager = EnrollmentSecurityManager(student, course)
                result = security_manager.validate_enrollment()
                
                validation_result = {
                    'student_id': student.id,
                    'allowed': result.allowed,
                    'risk_score': result.risk_score,
                    'response_time': time.time() - start_time
                }
                
                with self.lock:
                    validation_results.append(validation_result)
                    response_times.append(validation_result['response_time'])
                
                return validation_result
                
            except Exception as e:
                error_msg = f"Student {student.id}: {str(e)}"
                with self.lock:
                    errors.append(error_msg)
                    response_times.append(time.time() - start_time)
                
                return {
                    'student_id': student.id,
                    'allowed': False,
                    'error': str(e),
                    'response_time': time.time() - start_time
                }
        
        # Execute concurrent validations
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(validate_enrollment, student) for student in students]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Analyze results
        successful_validations = sum(1 for r in validation_results if 'allowed' in r)
        failed_validations = len(validation_results) - successful_validations
        
        result = ConcurrentTestResult(
            test_name="Concurrent Security Validation",
            total_attempts=num_threads,
            successful_enrollments=successful_validations,
            failed_enrollments=failed_validations,
            race_condition_detected=False,  # Not applicable for validation
            data_integrity_maintained=True,  # Validation doesn't modify data
            average_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            concurrent_threads=num_threads,
            errors=errors
        )
        
        self.results.append(result)
        self.print_concurrent_result(result)
        return result
    
    def test_mixed_concurrent_operations(self, num_threads=25):
        """Test mixed concurrent operations (enrollments, drops, validations)"""
        print(f"\n🔄 Testing Mixed Concurrent Operations ({num_threads} threads)")
        
        course = self.test_data['courses'][2]
        students = self.test_data['students'][:num_threads]
        
        # Pre-enroll some students
        for student in students[:10]:
            try:
                course.enroll_student(student)
            except:
                pass
        
        operation_results = []
        response_times = []
        errors = []
        
        def random_operation(student):
            """Perform a random operation"""
            start_time = time.time()
            operations = ['enroll', 'validate', 'check_status']
            operation = random.choice(operations)
            
            try:
                if operation == 'enroll':
                    enrollment, waitlist = course.enroll_student(student)
                    success = enrollment is not None or waitlist is not None
                elif operation == 'validate':
                    security_manager = EnrollmentSecurityManager(student, course)
                    result = security_manager.validate_enrollment()
                    success = True  # Validation always succeeds
                else:  # check_status
                    can_enroll, message = course.can_enroll(student)
                    success = True  # Status check always succeeds
                
                operation_result = {
                    'student_id': student.id,
                    'operation': operation,
                    'success': success,
                    'response_time': time.time() - start_time
                }
                
                with self.lock:
                    operation_results.append(operation_result)
                    response_times.append(operation_result['response_time'])
                
                return operation_result
                
            except Exception as e:
                error_msg = f"Student {student.id} ({operation}): {str(e)}"
                with self.lock:
                    errors.append(error_msg)
                    response_times.append(time.time() - start_time)
                
                return {
                    'student_id': student.id,
                    'operation': operation,
                    'success': False,
                    'error': str(e),
                    'response_time': time.time() - start_time
                }
        
        # Execute mixed concurrent operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(random_operation, student) for student in students]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Analyze results
        successful_operations = sum(1 for r in operation_results if r['success'])
        failed_operations = len(operation_results) - successful_operations
        
        result = ConcurrentTestResult(
            test_name="Mixed Concurrent Operations",
            total_attempts=num_threads,
            successful_enrollments=successful_operations,
            failed_enrollments=failed_operations,
            race_condition_detected=False,
            data_integrity_maintained=True,
            average_response_time=sum(response_times) / len(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            concurrent_threads=num_threads,
            errors=errors
        )
        
        self.results.append(result)
        self.print_concurrent_result(result)
        return result
    
    def print_concurrent_result(self, result: ConcurrentTestResult):
        """Print concurrent test results"""
        print(f"\n📊 {result.test_name} Results:")
        print(f"  🔢 Total Attempts: {result.total_attempts}")
        print(f"  ✅ Successful: {result.successful_enrollments}")
        print(f"  ❌ Failed: {result.failed_enrollments}")
        print(f"  🏁 Race Condition Detected: {'Yes' if result.race_condition_detected else 'No'}")
        print(f"  🛡️  Data Integrity Maintained: {'Yes' if result.data_integrity_maintained else 'No'}")
        print(f"  ⏱️  Avg Response Time: {result.average_response_time*1000:.2f}ms")
        print(f"  ⚡ Min/Max Response Time: {result.min_response_time*1000:.2f}ms / {result.max_response_time*1000:.2f}ms")
        print(f"  🧵 Concurrent Threads: {result.concurrent_threads}")
        
        if result.errors:
            print(f"  ⚠️  Errors ({len(result.errors)}):")
            for error in result.errors[:5]:  # Show first 5 errors
                print(f"    - {error}")
            if len(result.errors) > 5:
                print(f"    ... and {len(result.errors) - 5} more")
    
    def generate_concurrent_test_report(self):
        """Generate comprehensive concurrent testing report"""
        print("\n" + "="*70)
        print("🏁 COMPREHENSIVE CONCURRENT ENROLLMENT TEST REPORT")
        print("="*70)
        
        for result in self.results:
            self.print_concurrent_result(result)
        
        print("\n🎯 CONCURRENT TESTING SUMMARY:")
        print(f"  📈 Total Tests: {len(self.results)}")
        
        # Overall statistics
        total_attempts = sum(r.total_attempts for r in self.results)
        total_successful = sum(r.successful_enrollments for r in self.results)
        total_failed = sum(r.failed_enrollments for r in self.results)
        
        print(f"  🔢 Total Operations: {total_attempts}")
        print(f"  ✅ Overall Success Rate: {(total_successful/total_attempts*100):.1f}%")
        
        # Race condition analysis
        race_conditions = sum(1 for r in self.results if r.race_condition_detected)
        data_integrity_issues = sum(1 for r in self.results if not r.data_integrity_maintained)
        
        print(f"  🏁 Race Conditions Detected: {race_conditions}/{len(self.results)} tests")
        print(f"  🛡️  Data Integrity Issues: {data_integrity_issues}/{len(self.results)} tests")
        
        # Performance analysis
        avg_response_times = [r.average_response_time for r in self.results]
        if avg_response_times:
            overall_avg = sum(avg_response_times) / len(avg_response_times)
            print(f"  ⏱️  Overall Avg Response Time: {overall_avg*1000:.2f}ms")
        
        print("\n💡 CONCURRENT TESTING RECOMMENDATIONS:")
        if race_conditions > 0:
            print("  ⚠️  Race conditions detected - review transaction isolation")
        if data_integrity_issues > 0:
            print("  ⚠️  Data integrity issues - strengthen database constraints")
        if overall_avg > 0.5:  # 500ms
            print("  ⚠️  High response times - consider performance optimization")
        
        if race_conditions == 0 and data_integrity_issues == 0:
            print("  ✅ Excellent concurrent performance - no race conditions detected")
            print("  ✅ Data integrity maintained under concurrent load")
    
    def cleanup_concurrent_test_data(self):
        """Clean up concurrent test data"""
        print("\n🧹 Cleaning up concurrent test data...")
        User.objects.filter(username__startswith='concurrent_test_').delete()
        Department.objects.filter(code='CONCURRENT').delete()
        cache.clear()


def run_concurrent_enrollment_tests():
    """Run all concurrent enrollment tests"""
    print("🏁 STARTING CONCURRENT ENROLLMENT TESTING")
    print("="*70)
    
    tester = ConcurrentEnrollmentTester()
    
    try:
        # Setup test data
        tester.setup_concurrent_test_data(num_students=50, num_courses=5)
        
        # Run concurrent tests
        tester.test_race_condition_enrollment(num_threads=25, course_capacity=15)
        tester.test_concurrent_security_validation(num_threads=20)
        tester.test_mixed_concurrent_operations(num_threads=30)
        
        # Generate report
        tester.generate_concurrent_test_report()
        
        return True
        
    except Exception as e:
        print(f"❌ Concurrent testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        tester.cleanup_concurrent_test_data()


if __name__ == "__main__":
    success = run_concurrent_enrollment_tests()
    sys.exit(0 if success else 1)
