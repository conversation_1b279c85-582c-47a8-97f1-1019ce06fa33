#!/usr/bin/env python
"""
COMPREHENSIVE ENROLLMENT PERFORMANCE TESTING SUITE
Tests high-volume enrollment scenarios for scalability and performance
"""

import os
import sys
import django
import time
import threading
import concurrent.futures
import statistics
import psutil
import gc
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Tuple

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from django.db import transaction, connection
from django.test.utils import override_settings
from courses.models import Course, Enrollment, Department
from courses.enrollment_security import EnrollmentSecurityManager

User = get_user_model()


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    operation: str
    total_operations: int
    total_time: float
    avg_time: float
    min_time: float
    max_time: float
    operations_per_second: float
    success_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float
    db_queries: int


class EnrollmentPerformanceTester:
    """Comprehensive performance testing for enrollment system"""
    
    def __init__(self):
        self.results = []
        self.test_data = {}
        
    def setup_test_data(self, num_students=1000, num_courses=100):
        """Create test data for performance testing"""
        print(f"📊 Setting up test data: {num_students} students, {num_courses} courses...")
        
        # Clear existing test data
        User.objects.filter(username__startswith='perf_test_').delete()
        Department.objects.filter(code__startswith='PERF').delete()
        cache.clear()
        
        # Create department
        department = Department.objects.create(
            name="Performance Test Department", 
            code="PERF_TEST"
        )
        
        # Create instructor
        instructor = User.objects.create_user(
            username="perf_test_instructor",
            email="<EMAIL>",
            password="password123",
            role="teacher"
        )
        
        # Bulk create students
        students = []
        for i in range(num_students):
            students.append(User(
                username=f"perf_test_student_{i:05d}",
                email=f"student_{i:05d}@perftest.com",
                password="password123",
                role="student"
            ))
        
        # Use bulk_create for efficiency
        User.objects.bulk_create(students, batch_size=100)
        students = User.objects.filter(username__startswith='perf_test_student_')
        
        # Bulk create courses
        courses = []
        for i in range(num_courses):
            courses.append(Course(
                title=f"Performance Test Course {i:03d}",
                code=f"PERF{i:03d}",
                description=f"Performance test course {i}",
                department=department,
                instructor=instructor,
                level="undergraduate",
                credit_hours=3,
                semester="spring",
                year=2025,
                max_students=50,  # Reasonable capacity
                is_active=True,
                is_published=True,
                start_date=timezone.now().date(),
                end_date=timezone.now().date() + timedelta(days=90)
            ))
        
        Course.objects.bulk_create(courses, batch_size=50)
        courses = Course.objects.filter(code__startswith='PERF')
        
        self.test_data = {
            'students': list(students),
            'courses': list(courses),
            'department': department,
            'instructor': instructor
        }
        
        print(f"✅ Test data created: {len(students)} students, {len(courses)} courses")
        
    def measure_performance(self, operation_name: str, operation_func, *args, **kwargs):
        """Measure performance of an operation"""
        # Get initial metrics
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu = process.cpu_percent()
        
        # Reset query count
        connection.queries_log.clear()
        
        start_time = time.time()
        
        try:
            result = operation_func(*args, **kwargs)
            success = True
        except Exception as e:
            print(f"❌ Operation failed: {e}")
            result = None
            success = False
        
        end_time = time.time()
        
        # Get final metrics
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = process.cpu_percent()
        
        execution_time = end_time - start_time
        memory_usage = final_memory - initial_memory
        cpu_usage = (initial_cpu + final_cpu) / 2
        db_queries = len(connection.queries)
        
        return {
            'result': result,
            'success': success,
            'execution_time': execution_time,
            'memory_usage': memory_usage,
            'cpu_usage': cpu_usage,
            'db_queries': db_queries
        }
    
    def test_single_enrollment_performance(self, iterations=100):
        """Test single enrollment operation performance"""
        print(f"\n🔍 Testing Single Enrollment Performance ({iterations} iterations)")
        
        students = self.test_data['students'][:iterations]
        course = self.test_data['courses'][0]
        
        times = []
        successes = 0
        total_memory = 0
        total_cpu = 0
        total_queries = 0
        
        for i, student in enumerate(students):
            metrics = self.measure_performance(
                "single_enrollment",
                course.enroll_student,
                student
            )
            
            times.append(metrics['execution_time'])
            if metrics['success']:
                successes += 1
            total_memory += metrics['memory_usage']
            total_cpu += metrics['cpu_usage']
            total_queries += metrics['db_queries']
            
            if (i + 1) % 20 == 0:
                print(f"  📊 Completed {i + 1}/{iterations} enrollments")
        
        # Calculate statistics
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        total_time = sum(times)
        ops_per_second = iterations / total_time if total_time > 0 else 0
        success_rate = (successes / iterations) * 100
        
        result = PerformanceMetrics(
            operation="Single Enrollment",
            total_operations=iterations,
            total_time=total_time,
            avg_time=avg_time,
            min_time=min_time,
            max_time=max_time,
            operations_per_second=ops_per_second,
            success_rate=success_rate,
            memory_usage_mb=total_memory / iterations,
            cpu_usage_percent=total_cpu / iterations,
            db_queries=total_queries // iterations
        )
        
        self.results.append(result)
        self.print_metrics(result)
        return result
    
    def test_concurrent_enrollment_performance(self, num_threads=10, enrollments_per_thread=20):
        """Test concurrent enrollment performance"""
        print(f"\n🔍 Testing Concurrent Enrollment Performance ({num_threads} threads, {enrollments_per_thread} each)")
        
        course = self.test_data['courses'][1]  # Use different course
        students = self.test_data['students'][:num_threads * enrollments_per_thread]
        
        def enroll_batch(student_batch):
            """Enroll a batch of students"""
            batch_times = []
            batch_successes = 0
            
            for student in student_batch:
                start_time = time.time()
                try:
                    course.enroll_student(student)
                    batch_successes += 1
                except Exception:
                    pass
                batch_times.append(time.time() - start_time)
            
            return batch_times, batch_successes
        
        # Split students into batches
        student_batches = [
            students[i:i + enrollments_per_thread] 
            for i in range(0, len(students), enrollments_per_thread)
        ]
        
        start_time = time.time()
        
        # Execute concurrent enrollments
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(enroll_batch, batch) for batch in student_batches]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        
        # Aggregate results
        all_times = []
        total_successes = 0
        
        for batch_times, batch_successes in results:
            all_times.extend(batch_times)
            total_successes += batch_successes
        
        total_operations = num_threads * enrollments_per_thread
        total_time = end_time - start_time
        avg_time = statistics.mean(all_times)
        min_time = min(all_times)
        max_time = max(all_times)
        ops_per_second = total_operations / total_time
        success_rate = (total_successes / total_operations) * 100
        
        result = PerformanceMetrics(
            operation="Concurrent Enrollment",
            total_operations=total_operations,
            total_time=total_time,
            avg_time=avg_time,
            min_time=min_time,
            max_time=max_time,
            operations_per_second=ops_per_second,
            success_rate=success_rate,
            memory_usage_mb=0,  # Difficult to measure in concurrent scenario
            cpu_usage_percent=0,
            db_queries=0
        )
        
        self.results.append(result)
        self.print_metrics(result)
        return result
    
    def test_security_validation_performance(self, iterations=500):
        """Test security validation performance"""
        print(f"\n🔍 Testing Security Validation Performance ({iterations} iterations)")
        
        student = self.test_data['students'][0]
        course = self.test_data['courses'][2]
        
        times = []
        successes = 0
        
        for i in range(iterations):
            security_manager = EnrollmentSecurityManager(student, course)
            
            start_time = time.time()
            try:
                result = security_manager.validate_enrollment()
                if result.allowed:
                    successes += 1
            except Exception:
                pass
            times.append(time.time() - start_time)
            
            if (i + 1) % 100 == 0:
                print(f"  📊 Completed {i + 1}/{iterations} validations")
        
        # Calculate statistics
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        total_time = sum(times)
        ops_per_second = iterations / total_time if total_time > 0 else 0
        success_rate = (successes / iterations) * 100
        
        result = PerformanceMetrics(
            operation="Security Validation",
            total_operations=iterations,
            total_time=total_time,
            avg_time=avg_time,
            min_time=min_time,
            max_time=max_time,
            operations_per_second=ops_per_second,
            success_rate=success_rate,
            memory_usage_mb=0,
            cpu_usage_percent=0,
            db_queries=0
        )
        
        self.results.append(result)
        self.print_metrics(result)
        return result
    
    def print_metrics(self, metrics: PerformanceMetrics):
        """Print performance metrics in a readable format"""
        print(f"\n📊 {metrics.operation} Performance Results:")
        print(f"  🔢 Total Operations: {metrics.total_operations:,}")
        print(f"  ⏱️  Total Time: {metrics.total_time:.2f}s")
        print(f"  📈 Average Time: {metrics.avg_time*1000:.2f}ms")
        print(f"  ⚡ Operations/Second: {metrics.operations_per_second:.1f}")
        print(f"  ✅ Success Rate: {metrics.success_rate:.1f}%")
        print(f"  🧠 Memory Usage: {metrics.memory_usage_mb:.2f}MB")
        print(f"  💾 DB Queries: {metrics.db_queries}")
        print(f"  ⏰ Min/Max Time: {metrics.min_time*1000:.2f}ms / {metrics.max_time*1000:.2f}ms")
    
    def test_bulk_enrollment_performance(self, batch_size=100):
        """Test bulk enrollment performance"""
        print(f"\n🔍 Testing Bulk Enrollment Performance (batch size: {batch_size})")

        students = self.test_data['students'][:batch_size]
        course = self.test_data['courses'][3]

        start_time = time.time()

        # Bulk enrollment using transaction
        with transaction.atomic():
            enrollments = []
            for student in students:
                enrollments.append(Enrollment(
                    student=student,
                    course=course,
                    status='enrolled',
                    enrollment_type='regular'
                ))

            Enrollment.objects.bulk_create(enrollments, batch_size=50)

        end_time = time.time()

        total_time = end_time - start_time
        ops_per_second = batch_size / total_time if total_time > 0 else 0

        result = PerformanceMetrics(
            operation="Bulk Enrollment",
            total_operations=batch_size,
            total_time=total_time,
            avg_time=total_time / batch_size,
            min_time=0,
            max_time=0,
            operations_per_second=ops_per_second,
            success_rate=100.0,
            memory_usage_mb=0,
            cpu_usage_percent=0,
            db_queries=0
        )

        self.results.append(result)
        self.print_metrics(result)
        return result

    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        print("\n" + "="*60)
        print("📊 COMPREHENSIVE PERFORMANCE REPORT")
        print("="*60)

        for metrics in self.results:
            self.print_metrics(metrics)

        print("\n🎯 PERFORMANCE SUMMARY:")
        print(f"  📈 Total Tests: {len(self.results)}")

        # Find best and worst performing operations
        if self.results:
            best_ops = max(self.results, key=lambda x: x.operations_per_second)
            worst_ops = min(self.results, key=lambda x: x.operations_per_second)

            print(f"  🚀 Best Performance: {best_ops.operation} ({best_ops.operations_per_second:.1f} ops/sec)")
            print(f"  🐌 Needs Optimization: {worst_ops.operation} ({worst_ops.operations_per_second:.1f} ops/sec)")

        print("\n💡 RECOMMENDATIONS:")
        for metrics in self.results:
            if metrics.operations_per_second < 10:
                print(f"  ⚠️  {metrics.operation}: Consider optimization (< 10 ops/sec)")
            elif metrics.success_rate < 95:
                print(f"  ⚠️  {metrics.operation}: Low success rate ({metrics.success_rate:.1f}%)")
            else:
                print(f"  ✅ {metrics.operation}: Performance acceptable")

    def cleanup_test_data(self):
        """Clean up test data"""
        print("\n🧹 Cleaning up test data...")
        User.objects.filter(username__startswith='perf_test_').delete()
        Department.objects.filter(code__startswith='PERF').delete()
        cache.clear()
        gc.collect()  # Force garbage collection


def run_comprehensive_performance_tests():
    """Run all performance tests"""
    print("🚀 STARTING COMPREHENSIVE ENROLLMENT PERFORMANCE TESTING")
    print("="*60)

    tester = EnrollmentPerformanceTester()

    try:
        # Setup test data
        tester.setup_test_data(num_students=500, num_courses=20)

        # Run performance tests
        tester.test_single_enrollment_performance(iterations=100)
        tester.test_concurrent_enrollment_performance(num_threads=5, enrollments_per_thread=10)
        tester.test_security_validation_performance(iterations=200)
        tester.test_bulk_enrollment_performance(batch_size=50)

        # Generate report
        tester.generate_performance_report()

        return True

    except Exception as e:
        print(f"❌ Performance testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        tester.cleanup_test_data()


if __name__ == "__main__":
    success = run_comprehensive_performance_tests()
    sys.exit(0 if success else 1)
