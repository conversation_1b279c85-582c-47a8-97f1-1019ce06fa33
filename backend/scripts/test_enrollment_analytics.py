#!/usr/bin/env python3
"""
Test script for the new enrollment analytics endpoint
Run this script to verify the analytics functionality works correctly
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from courses.models import Course, Enrollment, Department
from courses.views import course_enrollment_analytics
from django.test import RequestFactory
from django.utils import timezone
from datetime import date, timedelta
import json

User = get_user_model()

def create_test_data():
    """Create test data for analytics testing"""
    print("🔧 Creating test data...")
    
    # Create test users
    admin_user = User.objects.get_or_create(
        username='test_admin',
        defaults={
            'email': '<EMAIL>',
            'role': 'admin',
            'first_name': 'Test',
            'last_name': 'Admin'
        }
    )[0]
    
    students = []
    for i in range(5):
        student = User.objects.get_or_create(
            username=f'test_student_{i}',
            defaults={
                'email': f'student{i}@test.com',
                'role': 'student',
                'student_id': f'STU{i:03d}',
                'first_name': f'Student',
                'last_name': f'{i}'
            }
        )[0]
        students.append(student)
    
    teacher = User.objects.get_or_create(
        username='test_teacher',
        defaults={
            'email': '<EMAIL>',
            'role': 'teacher',
            'employee_id': 'TEA001',
            'first_name': 'Test',
            'last_name': 'Teacher'
        }
    )[0]
    
    # Create test department
    department = Department.objects.get_or_create(
        code='TEST',
        defaults={
            'name': 'Test Department',
            'description': 'Test department for analytics'
        }
    )[0]
    
    # Create test course
    course = Course.objects.get_or_create(
        code='TEST101',
        defaults={
            'title': 'Test Course for Analytics',
            'description': 'Test course to verify analytics functionality',
            'department': department,
            'instructor': teacher,
            'level': 'undergraduate',
            'credit_hours': 3,
            'semester': 'fall',
            'year': 2024,
            'max_students': 30,
            'start_date': date.today() + timedelta(days=30),
            'end_date': date.today() + timedelta(days=120),
            'is_active': True,
            'is_published': True
        }
    )[0]
    
    # Create diverse enrollment scenarios
    print("📊 Creating enrollment scenarios...")
    
    # Student 0: Completed successfully
    Enrollment.objects.get_or_create(
        student=students[0],
        course=course,
        defaults={
            'status': Enrollment.EnrollmentStatus.COMPLETED,
            'final_grade': 88.5,
            'letter_grade': 'B+',
            'attempt_number': 1,
            'is_active': True,
            'completed_at': timezone.now() - timedelta(days=30)
        }
    )
    
    # Student 1: Failed first attempt, currently retaking
    Enrollment.objects.get_or_create(
        student=students[1],
        course=course,
        attempt_number=1,
        defaults={
            'status': Enrollment.EnrollmentStatus.FAILED,
            'final_grade': 45.0,
            'letter_grade': 'F',
            'is_retake': False,
            'is_active': False,
            'completed_at': timezone.now() - timedelta(days=60)
        }
    )
    
    Enrollment.objects.get_or_create(
        student=students[1],
        course=course,
        attempt_number=2,
        defaults={
            'status': Enrollment.EnrollmentStatus.ENROLLED,
            'is_retake': True,
            'is_active': True
        }
    )
    
    # Student 2: Multiple failed attempts
    for attempt in range(1, 4):
        Enrollment.objects.get_or_create(
            student=students[2],
            course=course,
            attempt_number=attempt,
            defaults={
                'status': Enrollment.EnrollmentStatus.FAILED,
                'final_grade': 40.0 + attempt * 5,  # Improving scores
                'letter_grade': 'F',
                'is_retake': attempt > 1,
                'is_active': False,
                'completed_at': timezone.now() - timedelta(days=90 - attempt * 30)
            }
        )
    
    # Student 3: Currently enrolled (first attempt)
    Enrollment.objects.get_or_create(
        student=students[3],
        course=course,
        defaults={
            'status': Enrollment.EnrollmentStatus.ENROLLED,
            'attempt_number': 1,
            'is_active': True
        }
    )
    
    # Student 4: Dropped course
    Enrollment.objects.get_or_create(
        student=students[4],
        course=course,
        defaults={
            'status': Enrollment.EnrollmentStatus.DROPPED,
            'attempt_number': 1,
            'is_active': False,
            'dropped_at': timezone.now() - timedelta(days=45)
        }
    )
    
    print("✅ Test data created successfully!")
    return course, admin_user

def test_analytics_endpoint(course, admin_user):
    """Test the analytics endpoint with the created data"""
    print(f"🧪 Testing analytics for course: {course.code}")
    
    # Create a mock request
    factory = RequestFactory()
    request = factory.get(f'/api/courses/{course.id}/analytics/')
    request.user = admin_user
    
    # Call the analytics view
    response = course_enrollment_analytics(request, course.id)
    
    if response.status_code == 200:
        data = response.data
        print("📈 Analytics Results:")
        print(f"   Course: {data['course']['code']} - {data['course']['title']}")
        print(f"   Max Students: {data['course']['max_students']}")
        print()
        
        print("📊 Enrollment Statistics:")
        stats = data['enrollment_statistics']
        print(f"   Total Enrollments: {stats['total_enrollments']}")
        print(f"   Unique Students: {stats['unique_students']}")
        print(f"   Currently Enrolled: {stats['current_enrolled']}")
        print()
        
        print("📋 Status Breakdown:")
        for status, count in stats['status_breakdown'].items():
            if count > 0:
                print(f"   {status.title()}: {count}")
        print()
        
        print("🔄 Retake Statistics:")
        retake_stats = data['retake_statistics']
        print(f"   Students with Retakes: {retake_stats['students_with_retakes']}")
        print(f"   Total Retakes: {retake_stats['total_retakes']}")
        print(f"   Average Attempts: {retake_stats['average_attempts']:.2f}")
        print()
        
        if data['grade_distribution']:
            print("📝 Grade Distribution:")
            for grade, count in data['grade_distribution'].items():
                print(f"   {grade}: {count}")
            print()
        
        print(f"✅ Success Rate: {data['success_rate']:.1f}%")
        
        return True
    else:
        print(f"❌ Analytics endpoint failed with status: {response.status_code}")
        print(f"   Error: {response.data}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    print("🧹 Cleaning up test data...")
    
    # Delete test enrollments
    Enrollment.objects.filter(course__code='TEST101').delete()
    
    # Delete test course
    Course.objects.filter(code='TEST101').delete()
    
    # Delete test users
    User.objects.filter(username__startswith='test_').delete()
    
    # Delete test department
    Department.objects.filter(code='TEST').delete()
    
    print("✅ Cleanup completed!")

def main():
    """Main test function"""
    print("🚀 Starting Enrollment Analytics Test")
    print("=" * 50)
    
    try:
        # Create test data
        course, admin_user = create_test_data()
        
        # Test analytics
        success = test_analytics_endpoint(course, admin_user)
        
        if success:
            print("\n🎉 All tests passed! Analytics endpoint is working correctly.")
        else:
            print("\n❌ Tests failed! Check the analytics implementation.")
            
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up
        print("\n" + "=" * 50)
        cleanup_test_data()

if __name__ == '__main__':
    main()
