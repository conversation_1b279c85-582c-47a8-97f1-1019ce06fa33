#!/usr/bin/env python
"""
Quick test script to verify enrollment system fixes
Run this after applying the fixes to ensure everything works correctly
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from courses.models import Course, Enrollment, Department

User = get_user_model()

def test_enrollment_fixes():
    """Test the critical enrollment system fixes"""
    print("🧪 Testing Enrollment System Fixes...")
    
    # Clean up any existing test data
    User.objects.filter(username__startswith='test_').delete()
    Department.objects.filter(code='TEST').delete()
    
    try:
        # Create test data
        print("📝 Creating test data...")
        
        department = Department.objects.create(
            name="Test Department",
            code="TEST"
        )
        
        teacher = User.objects.create_user(
            username="test_teacher",
            email="<EMAIL>",
            password="password123",
            role="teacher"
        )
        
        student = User.objects.create_user(
            username="test_student",
            email="<EMAIL>",
            password="password123",
            role="student"
        )
        
        course = Course.objects.create(
            title="Test Course",
            code="TEST101",
            description="Test course for enrollment fixes",
            department=department,
            instructor=teacher,
            level="undergraduate",
            credit_hours=3,
            semester="spring",
            year=2025,
            max_students=30,
            is_active=True,
            is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
        
        print("✅ Test data created successfully")
        
        # Test 1: Initial enrollment
        print("\n🔍 Test 1: Initial enrollment...")
        enrollment1, _ = course.enroll_student(student)
        
        assert enrollment1.is_active == True, "First enrollment should be active"
        assert enrollment1.status == 'enrolled', "First enrollment should be enrolled"
        assert enrollment1.attempt_number == 1, "First enrollment should be attempt 1"
        
        print("✅ Initial enrollment works correctly")
        
        # Test 2: Course failure and retake
        print("\n🔍 Test 2: Course failure and retake...")
        
        # Simulate course failure
        enrollment1.status = 'failed'
        enrollment1.is_active = False
        enrollment1.save()
        
        # Attempt retake
        enrollment2, _ = course.enroll_student(student)
        
        # Verify only one active enrollment
        active_enrollments = Enrollment.objects.filter(
            student=student,
            course=course,
            is_active=True
        )
        
        assert active_enrollments.count() == 1, f"Should have exactly 1 active enrollment, found {active_enrollments.count()}"
        assert enrollment2.is_active == True, "Second enrollment should be active"
        assert enrollment2.attempt_number == 2, "Second enrollment should be attempt 2"
        assert enrollment2.is_retake == True, "Second enrollment should be marked as retake"
        
        # Verify first enrollment is still inactive
        enrollment1.refresh_from_db()
        assert enrollment1.is_active == False, "First enrollment should remain inactive"
        
        print("✅ Course failure and retake works correctly")
        
        # Test 3: Status determination
        print("\n🔍 Test 3: Status determination...")
        
        from courses.serializers import CourseSerializer
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.get('/')
        request.user = student
        
        serializer = CourseSerializer(course, context={'request': request})
        status = serializer.get_user_enrollment_status(course)
        
        assert status == 'enrolled', f"Status should be 'enrolled', got '{status}'"
        
        print("✅ Status determination works correctly")
        
        # Test 4: Retake limit
        print("\n🔍 Test 4: Retake limit enforcement...")
        
        # Create 2 more failed attempts (total 3 failures)
        for i in range(2):
            enrollment2.status = 'failed'
            enrollment2.is_active = False
            enrollment2.save()
            
            enrollment_next, _ = course.enroll_student(student)
            enrollment_next.status = 'failed'
            enrollment_next.is_active = False
            enrollment_next.save()
        
        # Try to enroll again (4th attempt - should fail)
        can_enroll, message = course.can_enroll(student)
        
        assert can_enroll == False, "Should not be able to enroll after 3 failed attempts"
        assert "Maximum retake attempts exceeded" in message, f"Expected retake limit message, got: {message}"
        
        print("✅ Retake limit enforcement works correctly")
        
        print("\n🎉 All tests passed! Enrollment system fixes are working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test data
        print("\n🧹 Cleaning up test data...")
        User.objects.filter(username__startswith='test_').delete()
        Department.objects.filter(code='TEST').delete()
    
    return True

if __name__ == "__main__":
    success = test_enrollment_fixes()
    sys.exit(0 if success else 1)
