"""
Enterprise Monitoring & Error Handling Configuration
30+ Years of Production Systems Expertise

Features:
- Comprehensive error handling with context
- Advanced logging strategies
- Real-time monitoring dashboards
- Alerting systems with escalation
- Performance tracking and optimization
- Health checks and system diagnostics
"""

import logging
import traceback
import sys
import os
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from django.conf import settings
from django.core.mail import send_mail
from django.http import JsonResponse
from django.utils import timezone
import json

# Configure enterprise logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/umls_enterprise.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('enterprise_monitoring')


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = 'info'
    WARNING = 'warning'
    ERROR = 'error'
    CRITICAL = 'critical'


class SystemComponent(Enum):
    """System components for monitoring"""
    DATABASE = 'database'
    CACHE = 'cache'
    WEBSOCKET = 'websocket'
    API = 'api'
    SECURITY = 'security'
    PERFORMANCE = 'performance'


@dataclass
class SystemAlert:
    """System alert data structure"""
    level: AlertLevel
    component: SystemComponent
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    resolved: bool = False
    escalated: bool = False


@dataclass
class HealthCheckResult:
    """Health check result structure"""
    component: SystemComponent
    status: str  # healthy, warning, critical
    response_time: float
    details: Dict[str, Any]
    timestamp: datetime


class EnterpriseErrorHandler:
    """
    Enterprise-grade error handling with comprehensive context
    """
    
    def __init__(self):
        self.error_counts = {}
        self.error_patterns = {}
        self.alert_thresholds = {
            AlertLevel.WARNING: 10,   # 10 errors in 5 minutes
            AlertLevel.ERROR: 25,     # 25 errors in 5 minutes
            AlertLevel.CRITICAL: 50   # 50 errors in 5 minutes
        }
    
    def handle_exception(self, exc_type, exc_value, exc_traceback, 
                        context: Dict[str, Any] = None):
        """
        Comprehensive exception handling with context
        """
        error_id = f"{exc_type.__name__}_{int(time.time())}"
        
        error_details = {
            'error_id': error_id,
            'type': exc_type.__name__,
            'message': str(exc_value),
            'traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)),
            'context': context or {},
            'timestamp': datetime.now().isoformat(),
            'system_info': self._get_system_context()
        }
        
        # Log the error
        logger.error(f"Exception {error_id}: {error_details}")
        
        # Track error patterns
        self._track_error_pattern(exc_type.__name__)
        
        # Check if alerting is needed
        self._check_alert_thresholds(exc_type.__name__)
        
        return error_details
    
    def _get_system_context(self) -> Dict[str, Any]:
        """
        Get comprehensive system context for error analysis
        """
        try:
            process = psutil.Process()
            return {
                'memory_usage_mb': process.memory_info().rss / 1024 / 1024,
                'cpu_percent': process.cpu_percent(),
                'open_files': len(process.open_files()),
                'connections': len(process.connections()),
                'threads': process.num_threads(),
                'system_load': os.getloadavg() if hasattr(os, 'getloadavg') else None,
                'disk_usage': psutil.disk_usage('/').percent,
                'python_version': sys.version,
                'django_version': getattr(settings, 'DJANGO_VERSION', 'unknown')
            }
        except Exception as e:
            return {'error': f'Failed to get system context: {e}'}
    
    def _track_error_pattern(self, error_type: str):
        """
        Track error patterns for analysis
        """
        now = datetime.now()
        if error_type not in self.error_patterns:
            self.error_patterns[error_type] = []
        
        self.error_patterns[error_type].append(now)
        
        # Keep only last 24 hours
        cutoff = now - timedelta(hours=24)
        self.error_patterns[error_type] = [
            ts for ts in self.error_patterns[error_type] if ts > cutoff
        ]
    
    def _check_alert_thresholds(self, error_type: str):
        """
        Check if error thresholds are exceeded and trigger alerts
        """
        now = datetime.now()
        recent_errors = [
            ts for ts in self.error_patterns.get(error_type, [])
            if ts > now - timedelta(minutes=5)
        ]
        
        error_count = len(recent_errors)
        
        if error_count >= self.alert_thresholds[AlertLevel.CRITICAL]:
            self._send_alert(AlertLevel.CRITICAL, error_type, error_count)
        elif error_count >= self.alert_thresholds[AlertLevel.ERROR]:
            self._send_alert(AlertLevel.ERROR, error_type, error_count)
        elif error_count >= self.alert_thresholds[AlertLevel.WARNING]:
            self._send_alert(AlertLevel.WARNING, error_type, error_count)
    
    def _send_alert(self, level: AlertLevel, error_type: str, count: int):
        """
        Send alert notifications
        """
        alert = SystemAlert(
            level=level,
            component=SystemComponent.API,
            message=f"High error rate detected: {error_type}",
            details={
                'error_type': error_type,
                'count': count,
                'timeframe': '5 minutes'
            },
            timestamp=datetime.now()
        )
        
        logger.critical(f"ALERT {level.value.upper()}: {alert.message}")
        
        # In production, integrate with alerting systems like PagerDuty, Slack, etc.
        if level == AlertLevel.CRITICAL:
            self._escalate_alert(alert)
    
    def _escalate_alert(self, alert: SystemAlert):
        """
        Escalate critical alerts
        """
        # In production, implement escalation logic
        logger.critical(f"ESCALATED ALERT: {alert.message}")


class SystemHealthMonitor:
    """
    Comprehensive system health monitoring
    """
    
    def __init__(self):
        self.health_checks = {}
        self.performance_metrics = {}
        self.alert_manager = EnterpriseErrorHandler()
    
    def register_health_check(self, component: SystemComponent, 
                            check_function: Callable[[], HealthCheckResult]):
        """
        Register a health check function
        """
        self.health_checks[component] = check_function
    
    def run_health_checks(self) -> Dict[SystemComponent, HealthCheckResult]:
        """
        Run all registered health checks
        """
        results = {}
        
        for component, check_function in self.health_checks.items():
            try:
                start_time = time.time()
                result = check_function()
                result.response_time = time.time() - start_time
                results[component] = result
                
                # Check for health issues
                if result.status == 'critical':
                    self._handle_health_issue(component, result)
                    
            except Exception as e:
                results[component] = HealthCheckResult(
                    component=component,
                    status='critical',
                    response_time=0,
                    details={'error': str(e)},
                    timestamp=datetime.now()
                )
                
                self.alert_manager.handle_exception(
                    type(e), e, e.__traceback__,
                    {'component': component.value, 'check': 'health_check'}
                )
        
        return results
    
    def _handle_health_issue(self, component: SystemComponent, result: HealthCheckResult):
        """
        Handle health check failures
        """
        alert = SystemAlert(
            level=AlertLevel.CRITICAL,
            component=component,
            message=f"Health check failed for {component.value}",
            details=result.details,
            timestamp=datetime.now()
        )
        
        logger.critical(f"Health check failure: {component.value} - {result.details}")
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive system metrics
        """
        try:
            process = psutil.Process()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'system': {
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory': {
                        'total': psutil.virtual_memory().total,
                        'available': psutil.virtual_memory().available,
                        'percent': psutil.virtual_memory().percent
                    },
                    'disk': {
                        'total': psutil.disk_usage('/').total,
                        'free': psutil.disk_usage('/').free,
                        'percent': psutil.disk_usage('/').percent
                    },
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
                },
                'process': {
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'cpu_percent': process.cpu_percent(),
                    'threads': process.num_threads(),
                    'open_files': len(process.open_files()),
                    'connections': len(process.connections())
                },
                'health_status': self._get_overall_health_status()
            }
        except Exception as e:
            logger.error(f"Failed to get system metrics: {e}")
            return {'error': str(e)}
    
    def _get_overall_health_status(self) -> str:
        """
        Determine overall system health status
        """
        health_results = self.run_health_checks()
        
        if not health_results:
            return 'unknown'
        
        statuses = [result.status for result in health_results.values()]
        
        if 'critical' in statuses:
            return 'critical'
        elif 'warning' in statuses:
            return 'warning'
        else:
            return 'healthy'


# Health check functions
def database_health_check() -> HealthCheckResult:
    """Database health check"""
    try:
        from django.db import connection
        from courses.models import Course
        
        start_time = time.time()
        
        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        # Test model query
        course_count = Course.objects.count()
        
        response_time = time.time() - start_time
        
        status = 'healthy'
        if response_time > 1.0:
            status = 'warning'
        if response_time > 5.0:
            status = 'critical'
        
        return HealthCheckResult(
            component=SystemComponent.DATABASE,
            status=status,
            response_time=response_time,
            details={
                'connection_status': 'connected',
                'course_count': course_count,
                'response_time_ms': response_time * 1000
            },
            timestamp=datetime.now()
        )
        
    except Exception as e:
        return HealthCheckResult(
            component=SystemComponent.DATABASE,
            status='critical',
            response_time=0,
            details={'error': str(e)},
            timestamp=datetime.now()
        )


def cache_health_check() -> HealthCheckResult:
    """Cache health check"""
    try:
        from django.core.cache import cache
        
        start_time = time.time()
        
        # Test cache operations
        test_key = 'health_check_test'
        test_value = 'test_value'
        
        cache.set(test_key, test_value, 60)
        retrieved_value = cache.get(test_key)
        cache.delete(test_key)
        
        response_time = time.time() - start_time
        
        status = 'healthy' if retrieved_value == test_value else 'critical'
        
        return HealthCheckResult(
            component=SystemComponent.CACHE,
            status=status,
            response_time=response_time,
            details={
                'cache_working': retrieved_value == test_value,
                'response_time_ms': response_time * 1000
            },
            timestamp=datetime.now()
        )
        
    except Exception as e:
        return HealthCheckResult(
            component=SystemComponent.CACHE,
            status='critical',
            response_time=0,
            details={'error': str(e)},
            timestamp=datetime.now()
        )


# Global instances
error_handler = EnterpriseErrorHandler()
health_monitor = SystemHealthMonitor()

# Register health checks
health_monitor.register_health_check(SystemComponent.DATABASE, database_health_check)
health_monitor.register_health_check(SystemComponent.CACHE, cache_health_check)


# Django exception handler
def handle_django_exception(request, exception):
    """
    Django exception handler with enterprise error handling
    """
    error_details = error_handler.handle_exception(
        type(exception), exception, exception.__traceback__,
        {
            'request_path': request.path,
            'request_method': request.method,
            'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') else None,
            'ip_address': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT')
        }
    )
    
    return JsonResponse({
        'error': 'Internal server error',
        'error_id': error_details['error_id'],
        'timestamp': error_details['timestamp']
    }, status=500)


# Monitoring endpoints
def system_health_endpoint(request):
    """
    System health monitoring endpoint
    """
    if not request.user.is_authenticated or request.user.role not in ['admin', 'super_admin']:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    health_results = health_monitor.run_health_checks()
    system_metrics = health_monitor.get_system_metrics()
    
    return JsonResponse({
        'health_checks': {
            component.value: {
                'status': result.status,
                'response_time': result.response_time,
                'details': result.details,
                'timestamp': result.timestamp.isoformat()
            }
            for component, result in health_results.items()
        },
        'system_metrics': system_metrics,
        'overall_status': health_monitor._get_overall_health_status()
    })


def performance_metrics_endpoint(request):
    """
    Performance metrics endpoint
    """
    if not request.user.is_authenticated or request.user.role not in ['admin', 'super_admin']:
        return JsonResponse({'error': 'Unauthorized'}, status=403)
    
    from backend.performance_middleware import get_performance_metrics
    
    return JsonResponse({
        'performance_metrics': get_performance_metrics(),
        'system_metrics': health_monitor.get_system_metrics(),
        'timestamp': datetime.now().isoformat()
    })
