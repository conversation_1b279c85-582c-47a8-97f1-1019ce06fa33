"""
Enterprise Performance Monitoring Middleware
30+ Years of Performance Optimization Experience

Features:
- Real-time performance monitoring
- Automatic slow query detection
- Memory usage tracking
- Cache hit rate monitoring
- API response time analysis
- Automatic performance alerts
"""

import time
import logging
import psutil
import threading
from django.db import connection
from django.conf import settings
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from backend.cache_config import cache_manager
from typing import Dict, List, Any
from collections import defaultdict, deque
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """
    Thread-safe performance metrics collector
    """
    
    def __init__(self):
        self._lock = threading.Lock()
        self.request_times = deque(maxlen=1000)  # Last 1000 requests
        self.slow_requests = deque(maxlen=100)   # Last 100 slow requests
        self.endpoint_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'avg_time': 0,
            'max_time': 0,
            'min_time': float('inf')
        })
        self.database_queries = deque(maxlen=500)  # Last 500 DB queries
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'hit_rate': 0
        }
        
    def add_request_metric(self, path: str, method: str, duration: float, 
                          query_count: int, query_time: float):
        """Add request performance metric"""
        with self._lock:
            timestamp = datetime.now()
            
            # Add to request times
            self.request_times.append({
                'timestamp': timestamp,
                'path': path,
                'method': method,
                'duration': duration,
                'query_count': query_count,
                'query_time': query_time
            })
            
            # Track slow requests (>1 second)
            if duration > 1.0:
                self.slow_requests.append({
                    'timestamp': timestamp,
                    'path': path,
                    'method': method,
                    'duration': duration,
                    'query_count': query_count,
                    'query_time': query_time
                })
            
            # Update endpoint statistics
            endpoint_key = f"{method} {path}"
            stats = self.endpoint_stats[endpoint_key]
            stats['count'] += 1
            stats['total_time'] += duration
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['max_time'] = max(stats['max_time'], duration)
            stats['min_time'] = min(stats['min_time'], duration)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        with self._lock:
            if not self.request_times:
                return {'message': 'No performance data available'}
            
            recent_requests = list(self.request_times)[-100:]  # Last 100 requests
            durations = [r['duration'] for r in recent_requests]
            query_counts = [r['query_count'] for r in recent_requests]
            
            # Calculate system metrics
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'request_metrics': {
                    'total_requests': len(self.request_times),
                    'avg_response_time': sum(durations) / len(durations) if durations else 0,
                    'max_response_time': max(durations) if durations else 0,
                    'min_response_time': min(durations) if durations else 0,
                    'slow_requests_count': len(self.slow_requests),
                    'avg_queries_per_request': sum(query_counts) / len(query_counts) if query_counts else 0
                },
                'system_metrics': {
                    'memory_usage_mb': memory_info.rss / 1024 / 1024,
                    'cpu_percent': psutil.cpu_percent(),
                    'active_connections': len(connection.queries)
                },
                'cache_metrics': cache_manager.get_stats(),
                'top_slow_endpoints': self._get_slowest_endpoints(),
                'recent_slow_requests': list(self.slow_requests)[-10:]  # Last 10 slow requests
            }
    
    def _get_slowest_endpoints(self) -> List[Dict[str, Any]]:
        """Get the slowest endpoints"""
        sorted_endpoints = sorted(
            self.endpoint_stats.items(),
            key=lambda x: x[1]['avg_time'],
            reverse=True
        )
        
        return [
            {
                'endpoint': endpoint,
                'avg_time': stats['avg_time'],
                'max_time': stats['max_time'],
                'count': stats['count']
            }
            for endpoint, stats in sorted_endpoints[:10]
        ]


# Global performance metrics instance
performance_metrics = PerformanceMetrics()


class EnterprisePerformanceMiddleware(MiddlewareMixin):
    """
    Enterprise performance monitoring middleware
    
    Monitors:
    - Request/response times
    - Database query performance
    - Memory usage
    - Cache hit rates
    - Slow endpoint detection
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Start performance monitoring for request"""
        request._performance_start_time = time.time()
        request._performance_query_count = len(connection.queries)
        return None
    
    def process_response(self, request, response):
        """Complete performance monitoring and log metrics"""
        if not hasattr(request, '_performance_start_time'):
            return response
        
        # Calculate metrics
        duration = time.time() - request._performance_start_time
        current_query_count = len(connection.queries)
        query_count = current_query_count - request._performance_query_count
        
        # Calculate total query time
        query_time = sum(
            float(query['time']) 
            for query in connection.queries[request._performance_query_count:]
        )
        
        # Add to performance metrics
        performance_metrics.add_request_metric(
            path=request.path,
            method=request.method,
            duration=duration,
            query_count=query_count,
            query_time=query_time
        )
        
        # Add performance headers for debugging
        if settings.DEBUG:
            response['X-Response-Time'] = f"{duration:.3f}s"
            response['X-Query-Count'] = str(query_count)
            response['X-Query-Time'] = f"{query_time:.3f}s"
        
        # Log slow requests
        if duration > 1.0:
            logger.warning(
                f"Slow request detected: {request.method} {request.path} "
                f"took {duration:.3f}s with {query_count} queries ({query_time:.3f}s)"
            )
        
        # Log excessive database queries
        if query_count > 20:
            logger.warning(
                f"High query count: {request.method} {request.path} "
                f"executed {query_count} database queries"
            )
        
        return response


class PerformanceMonitoringView:
    """
    Real-time performance monitoring endpoint
    """
    
    @staticmethod
    def get_performance_dashboard(request):
        """
        Get comprehensive performance dashboard data
        """
        if not request.user.is_authenticated or request.user.role not in ['admin', 'super_admin']:
            return JsonResponse({'error': 'Unauthorized'}, status=403)
        
        try:
            # Get performance summary
            summary = performance_metrics.get_performance_summary()
            
            # Add database health metrics
            from backend.database_optimization import check_database_health
            db_health = check_database_health()
            
            # Combine all metrics
            dashboard_data = {
                'timestamp': datetime.now().isoformat(),
                'performance_summary': summary,
                'database_health': db_health,
                'recommendations': _generate_performance_recommendations(summary, db_health)
            }
            
            return JsonResponse(dashboard_data)
            
        except Exception as e:
            logger.error(f"Performance dashboard error: {e}")
            return JsonResponse({'error': 'Internal server error'}, status=500)


def _generate_performance_recommendations(perf_summary: Dict, db_health: Dict) -> List[str]:
    """
    Generate intelligent performance recommendations
    """
    recommendations = []
    
    if 'request_metrics' in perf_summary:
        metrics = perf_summary['request_metrics']
        
        # Response time recommendations
        if metrics.get('avg_response_time', 0) > 0.5:
            recommendations.append(
                "Average response time is high (>500ms). Consider optimizing database queries or adding caching."
            )
        
        # Query count recommendations
        if metrics.get('avg_queries_per_request', 0) > 10:
            recommendations.append(
                "High average queries per request. Consider using select_related() and prefetch_related() for database optimization."
            )
        
        # Slow requests recommendations
        if metrics.get('slow_requests_count', 0) > 10:
            recommendations.append(
                "Multiple slow requests detected. Review the slowest endpoints and optimize critical paths."
            )
    
    # Cache recommendations
    if 'cache_metrics' in perf_summary:
        cache_metrics = perf_summary['cache_metrics']
        hit_rate = cache_metrics.get('hit_rate_percentage', 0)
        
        if hit_rate < 70:
            recommendations.append(
                f"Cache hit rate is low ({hit_rate:.1f}%). Consider increasing cache timeouts or warming critical data."
            )
    
    # Database recommendations
    if db_health.get('database_size_mb', 0) > 500:
        recommendations.append(
            "Database size is large. Consider archiving old data or implementing data retention policies."
        )
    
    # Memory recommendations
    if 'system_metrics' in perf_summary:
        memory_mb = perf_summary['system_metrics'].get('memory_usage_mb', 0)
        if memory_mb > 1000:  # 1GB
            recommendations.append(
                "High memory usage detected. Consider optimizing memory-intensive operations or increasing server resources."
            )
    
    if not recommendations:
        recommendations.append("System performance is optimal. No immediate recommendations.")
    
    return recommendations


def get_performance_metrics():
    """
    Get current performance metrics (for external use)
    """
    return performance_metrics.get_performance_summary()


def reset_performance_metrics():
    """
    Reset performance metrics (for testing or maintenance)
    """
    global performance_metrics
    performance_metrics = PerformanceMetrics()
    logger.info("Performance metrics reset")


# Performance monitoring decorator for critical functions
def monitor_performance(func_name: str = None):
    """
    Decorator to monitor function performance
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Log slow functions
                if duration > 0.1:  # 100ms threshold
                    logger.info(f"Function {func_name or func.__name__} took {duration:.3f}s")
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Function {func_name or func.__name__} failed after {duration:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator
