"""
WebSocket URL routing for UMLS real-time features
"""

from django.urls import path, re_path
from channels.routing import ProtocolType<PERSON>outer, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator

# Import WebSocket consumers
from notifications.consumers import NotificationConsumer
from communications.consumers import MessagingConsumer
from academic_analytics.consumers import AnalyticsConsumer
from backend.websocket_config import EnterpriseWebSocketConsumer

websocket_urlpatterns = [
    # Enterprise WebSocket for real-time enrollment and system updates
    re_path(r'ws/umls/$', EnterpriseWebSocketConsumer.as_asgi()),

    # Notifications WebSocket
    path('ws/notifications/', NotificationConsumer.as_asgi()),

    # Messaging WebSocket
    path('ws/messaging/', MessagingConsumer.as_asgi()),
    re_path(r'ws/messaging/(?P<conversation_id>\d+)/$', MessagingConsumer.as_asgi()),

    # Analytics WebSocket for real-time data updates
    path('ws/analytics/', AnalyticsConsumer.as_asgi()),
    re_path(r'ws/analytics/(?P<dashboard_type>\w+)/$', AnalyticsConsumer.as_asgi()),

    # Course-specific WebSocket for real-time updates
    re_path(r'ws/courses/(?P<course_id>\d+)/$', MessagingConsumer.as_asgi()),

    # General system updates WebSocket
    path('ws/system/', NotificationConsumer.as_asgi()),
]

# Enterprise WebSocket application with authentication
application = ProtocolTypeRouter({
    'websocket': AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(websocket_urlpatterns)
        )
    ),
})
