"""
Enterprise API Documentation & Schema Generation
30+ Years of API Design Excellence

Features:
- Comprehensive OpenAPI 3.0 schema
- Interactive API documentation
- API versioning strategy
- Response standardization
- Error handling documentation
"""

from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.openapi import AutoSchema
from rest_framework import serializers
from django.conf import settings

# API Documentation Configuration
API_TITLE = "UMLS Enterprise API"
API_DESCRIPTION = """
# University Management Learning System (UMLS) Enterprise API

## Overview
The UMLS Enterprise API provides comprehensive access to university management functionality including:
- Student enrollment management with advanced retake capabilities
- Course management and analytics
- Real-time notifications and updates
- Performance monitoring and health checks
- Security audit and compliance features

## Authentication
All API endpoints require JWT authentication. Obtain tokens via the `/api/auth/login/` endpoint.

## Rate Limiting
API requests are rate-limited based on user role and endpoint sensitivity:
- Students: 100 requests/minute
- Teachers: 200 requests/minute  
- Admins: 500 requests/minute

## Error Handling
All errors follow RFC 7807 Problem Details format with comprehensive context.

## Real-time Features
WebSocket connections available at `/ws/umls/` for real-time enrollment updates.

## Enterprise Features
- Advanced caching with intelligent invalidation
- Comprehensive security monitoring
- Performance analytics and optimization
- Multi-tenant architecture support
"""

API_VERSION = "v1.0.0"

# Standard API Response Schemas
class StandardResponseSchema(serializers.Serializer):
    """Standard API response format"""
    success = serializers.BooleanField(default=True)
    message = serializers.CharField(required=False)
    timestamp = serializers.DateTimeField()
    request_id = serializers.UUIDField(required=False)

class ErrorResponseSchema(serializers.Serializer):
    """Standard error response format"""
    success = serializers.BooleanField(default=False)
    error = serializers.CharField()
    error_code = serializers.CharField(required=False)
    details = serializers.DictField(required=False)
    timestamp = serializers.DateTimeField()
    request_id = serializers.UUIDField(required=False)

class PaginationSchema(serializers.Serializer):
    """Pagination metadata schema"""
    count = serializers.IntegerField()
    next = serializers.URLField(required=False, allow_null=True)
    previous = serializers.URLField(required=False, allow_null=True)
    page_size = serializers.IntegerField()
    total_pages = serializers.IntegerField()

class PaginatedResponseSchema(serializers.Serializer):
    """Paginated response schema"""
    results = serializers.ListField()
    pagination = PaginationSchema()

# Enhanced Course Documentation
course_list_schema = extend_schema(
    summary="List Courses",
    description="""
    Retrieve a paginated list of courses with enrollment status information.
    
    **Features:**
    - Real-time enrollment counts
    - User-specific enrollment status
    - Advanced filtering and search
    - Performance optimized with caching
    
    **Enrollment Status Values:**
    - `available`: Course is open for enrollment
    - `enrolled`: User is currently enrolled
    - `completed`: User has completed the course
    - `failed`: User failed and course is retakeable
    - `retakeable`: Course can be retaken after failure
    - `prerequisites_not_met`: Prerequisites required
    - `waitlisted`: User is on the waitlist
    """,
    parameters=[
        OpenApiParameter(
            name='department',
            type=str,
            location=OpenApiParameter.QUERY,
            description='Filter by department code'
        ),
        OpenApiParameter(
            name='level',
            type=str,
            location=OpenApiParameter.QUERY,
            description='Filter by course level (undergraduate/graduate)'
        ),
        OpenApiParameter(
            name='search',
            type=str,
            location=OpenApiParameter.QUERY,
            description='Search in course title, code, or description'
        ),
        OpenApiParameter(
            name='enrollment_status',
            type=str,
            location=OpenApiParameter.QUERY,
            description='Filter by user enrollment status'
        ),
    ],
    examples=[
        OpenApiExample(
            'Successful Response',
            value={
                "success": True,
                "results": [
                    {
                        "id": 1,
                        "title": "Advanced Programming",
                        "code": "CS301",
                        "department": "Computer Science",
                        "instructor": "Dr. Smith",
                        "max_students": 30,
                        "enrolled_count": 25,
                        "available_spots": 5,
                        "user_enrollment_status": "available",
                        "user_can_enroll": True,
                        "user_enrollment_message": "Course is available for enrollment"
                    }
                ],
                "pagination": {
                    "count": 50,
                    "next": "http://api.example.com/courses/?page=2",
                    "previous": None,
                    "page_size": 20,
                    "total_pages": 3
                }
            }
        )
    ],
    responses={
        200: PaginatedResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        429: ErrorResponseSchema,
    }
)

# Enhanced Enrollment Documentation
enrollment_create_schema = extend_schema(
    summary="Enroll in Course",
    description="""
    Enroll a student in a course with comprehensive validation and retake support.
    
    **Features:**
    - Automatic prerequisite checking
    - Retake eligibility validation
    - Waitlist management
    - Real-time capacity checking
    - Audit trail creation
    
    **Enrollment Process:**
    1. Validate user permissions
    2. Check course availability
    3. Verify prerequisites
    4. Handle retake scenarios
    5. Create enrollment record
    6. Send real-time notifications
    
    **Retake Logic:**
    - Failed courses become retakeable
    - Attempt numbers are tracked
    - Previous enrollments are deactivated
    - Grade history is preserved
    """,
    examples=[
        OpenApiExample(
            'Successful Enrollment',
            value={
                "success": True,
                "message": "Successfully enrolled in CS301",
                "enrollment": {
                    "id": 123,
                    "course_id": 1,
                    "status": "enrolled",
                    "attempt_number": 1,
                    "is_retake": False,
                    "enrolled_at": "2024-01-15T10:30:00Z"
                }
            }
        ),
        OpenApiExample(
            'Retake Enrollment',
            value={
                "success": True,
                "message": "Successfully enrolled in CS301 (Retake)",
                "enrollment": {
                    "id": 124,
                    "course_id": 1,
                    "status": "enrolled",
                    "attempt_number": 2,
                    "is_retake": True,
                    "enrolled_at": "2024-01-15T10:30:00Z"
                }
            }
        ),
        OpenApiExample(
            'Course Full Error',
            value={
                "success": False,
                "error": "Course is at maximum capacity",
                "error_code": "COURSE_FULL",
                "details": {
                    "max_students": 30,
                    "enrolled_count": 30,
                    "waitlist_available": True
                }
            }
        )
    ],
    responses={
        201: StandardResponseSchema,
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        409: ErrorResponseSchema,
    }
)

# Performance Monitoring Documentation
performance_schema = extend_schema(
    summary="System Performance Metrics",
    description="""
    Retrieve comprehensive system performance metrics and health status.
    
    **Admin/Teacher Only Endpoint**
    
    **Metrics Included:**
    - Request/response performance
    - Database query statistics
    - Cache hit rates
    - Memory and CPU usage
    - Error rates and patterns
    - Real-time connection counts
    
    **Health Checks:**
    - Database connectivity
    - Cache functionality
    - WebSocket status
    - External service health
    """,
    examples=[
        OpenApiExample(
            'Performance Metrics',
            value={
                "performance_summary": {
                    "request_metrics": {
                        "total_requests": 1250,
                        "avg_response_time": 0.145,
                        "slow_requests_count": 3
                    },
                    "cache_metrics": {
                        "hit_rate_percentage": 87.5,
                        "total_requests": 500
                    }
                },
                "database_health": {
                    "status": "healthy",
                    "database_size_mb": 125.4
                },
                "recommendations": [
                    "System performance is optimal"
                ]
            }
        )
    ],
    responses={
        200: StandardResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
    }
)

# Analytics Documentation
analytics_schema = extend_schema(
    summary="Course Analytics",
    description="""
    Retrieve detailed analytics for a specific course.
    
    **Teacher/Admin Only Endpoint**
    
    **Analytics Include:**
    - Enrollment statistics and trends
    - Retake analysis and success rates
    - Student performance metrics
    - Capacity utilization
    - Historical enrollment data
    
    **Use Cases:**
    - Course performance evaluation
    - Capacity planning
    - Student success analysis
    - Curriculum optimization
    """,
    examples=[
        OpenApiExample(
            'Course Analytics',
            value={
                "enrollment_statistics": {
                    "total_enrollments": 45,
                    "unique_students": 38,
                    "current_enrolled": 30,
                    "completion_rate": 85.5
                },
                "retake_statistics": {
                    "students_with_retakes": 7,
                    "retake_success_rate": 71.4,
                    "avg_attempts": 1.2
                },
                "success_rate": 85.5,
                "grade_distribution": {
                    "A": 12,
                    "B": 15,
                    "C": 8,
                    "D": 3,
                    "F": 7
                }
            }
        )
    ],
    responses={
        200: StandardResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
    }
)

# WebSocket Documentation
websocket_documentation = """
# WebSocket API Documentation

## Connection
Connect to: `ws://your-domain/ws/umls/`

**Authentication Required:** Include JWT token in connection headers

## Message Format
All messages follow this structure:
```json
{
    "event_type": "enrollment_update",
    "data": { ... },
    "timestamp": "2024-01-15T10:30:00Z",
    "user_id": 123,
    "priority": "high"
}
```

## Event Types

### enrollment_update
Real-time enrollment status changes
```json
{
    "event_type": "enrollment_update",
    "data": {
        "enrollment_id": 123,
        "student_id": 456,
        "course_id": 1,
        "status": "enrolled",
        "message": "Successfully enrolled in CS301"
    }
}
```

### course_update
Course information changes
```json
{
    "event_type": "course_update", 
    "data": {
        "course_id": 1,
        "enrolled_count": 25,
        "available_spots": 5,
        "is_full": false
    }
}
```

### notification
System notifications
```json
{
    "event_type": "notification",
    "data": {
        "title": "Grade Posted",
        "message": "Your grade for CS301 is now available",
        "type": "info",
        "action_url": "/grades/cs301"
    }
}
```

## Client Commands

### Heartbeat
```json
{
    "type": "heartbeat",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### Subscribe to Course
```json
{
    "type": "subscribe_course",
    "course_id": 1
}
```

### Join Room
```json
{
    "type": "join_room",
    "room": "course_1"
}
```
"""

# API Versioning Strategy
API_VERSIONING_STRATEGY = """
# API Versioning Strategy

## Version Format
- **Current Version:** v1.0.0
- **Format:** Semantic Versioning (MAJOR.MINOR.PATCH)

## Version Headers
- **Accept:** application/vnd.umls.v1+json
- **API-Version:** 1.0

## Backward Compatibility
- **Minor versions:** Backward compatible
- **Major versions:** Breaking changes allowed
- **Deprecation:** 6-month notice period

## Version Lifecycle
1. **Development:** Alpha/Beta testing
2. **Stable:** Production ready
3. **Deprecated:** 6-month support
4. **Retired:** No longer supported
"""
