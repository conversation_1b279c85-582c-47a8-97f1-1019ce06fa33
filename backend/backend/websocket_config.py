"""
Enterprise WebSocket Configuration
30+ Years of Real-time Systems Expertise

Features:
- Real-time enrollment updates
- Live notifications
- Event-driven architecture
- Connection management
- Message queuing
- Scalable WebSocket handling
"""

import json
import asyncio
import logging
from typing import Dict, List, Set, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.core.serializers.json import DjangoJSONEncoder

logger = logging.getLogger('websocket')
User = get_user_model()


class EventType(Enum):
    """WebSocket event types"""
    ENROLLMENT_UPDATE = 'enrollment_update'
    COURSE_UPDATE = 'course_update'
    NOTIFICATION = 'notification'
    GRADE_UPDATE = 'grade_update'
    ANNOUNCEMENT = 'announcement'
    SYSTEM_MESSAGE = 'system_message'
    USER_STATUS = 'user_status'
    REAL_TIME_ANALYTICS = 'real_time_analytics'


@dataclass
class WebSocketMessage:
    """Standardized WebSocket message format"""
    event_type: EventType
    data: Dict[str, Any]
    timestamp: datetime
    user_id: Optional[int] = None
    room: Optional[str] = None
    priority: str = 'normal'  # low, normal, high, critical
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_type': self.event_type.value,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'user_id': self.user_id,
            'room': self.room,
            'priority': self.priority
        }


class ConnectionManager:
    """
    Enterprise WebSocket connection manager
    
    Manages connections, rooms, and message broadcasting
    with enterprise-grade features:
    - Connection pooling
    - Room-based messaging
    - User presence tracking
    - Message queuing
    - Connection health monitoring
    """
    
    def __init__(self):
        # Active connections by user ID
        self.user_connections: Dict[int, Set[AsyncWebsocketConsumer]] = {}
        
        # Room-based connections
        self.room_connections: Dict[str, Set[AsyncWebsocketConsumer]] = {}
        
        # User presence tracking
        self.user_presence: Dict[int, datetime] = {}
        
        # Message queue for offline users
        self.message_queue: Dict[int, List[WebSocketMessage]] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[AsyncWebsocketConsumer, Dict[str, Any]] = {}
    
    async def connect_user(self, consumer: AsyncWebsocketConsumer, user_id: int, 
                          metadata: Dict[str, Any] = None):
        """Connect a user with metadata"""
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        
        self.user_connections[user_id].add(consumer)
        self.user_presence[user_id] = datetime.now()
        
        if metadata:
            self.connection_metadata[consumer] = metadata
        
        # Send queued messages
        await self._send_queued_messages(user_id, consumer)
        
        logger.info(f"User {user_id} connected via WebSocket")
    
    async def disconnect_user(self, consumer: AsyncWebsocketConsumer, user_id: int):
        """Disconnect a user"""
        if user_id in self.user_connections:
            self.user_connections[user_id].discard(consumer)
            
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
                if user_id in self.user_presence:
                    del self.user_presence[user_id]
        
        # Remove from all rooms
        for room_connections in self.room_connections.values():
            room_connections.discard(consumer)
        
        # Clean up metadata
        if consumer in self.connection_metadata:
            del self.connection_metadata[consumer]
        
        logger.info(f"User {user_id} disconnected from WebSocket")
    
    async def join_room(self, consumer: AsyncWebsocketConsumer, room: str):
        """Join a room for group messaging"""
        if room not in self.room_connections:
            self.room_connections[room] = set()
        
        self.room_connections[room].add(consumer)
        logger.info(f"Consumer joined room: {room}")
    
    async def leave_room(self, consumer: AsyncWebsocketConsumer, room: str):
        """Leave a room"""
        if room in self.room_connections:
            self.room_connections[room].discard(consumer)
            
            if not self.room_connections[room]:
                del self.room_connections[room]
        
        logger.info(f"Consumer left room: {room}")
    
    async def send_to_user(self, user_id: int, message: WebSocketMessage):
        """Send message to specific user"""
        if user_id in self.user_connections:
            # User is online, send immediately
            connections = self.user_connections[user_id].copy()
            for consumer in connections:
                try:
                    await consumer.send(text_data=json.dumps(
                        message.to_dict(), 
                        cls=DjangoJSONEncoder
                    ))
                except Exception as e:
                    logger.error(f"Failed to send message to user {user_id}: {e}")
                    # Remove broken connection
                    await self.disconnect_user(consumer, user_id)
        else:
            # User is offline, queue message
            if user_id not in self.message_queue:
                self.message_queue[user_id] = []
            
            self.message_queue[user_id].append(message)
            
            # Limit queue size
            if len(self.message_queue[user_id]) > 100:
                self.message_queue[user_id] = self.message_queue[user_id][-100:]
    
    async def send_to_room(self, room: str, message: WebSocketMessage):
        """Send message to all users in a room"""
        if room in self.room_connections:
            connections = self.room_connections[room].copy()
            for consumer in connections:
                try:
                    await consumer.send(text_data=json.dumps(
                        message.to_dict(),
                        cls=DjangoJSONEncoder
                    ))
                except Exception as e:
                    logger.error(f"Failed to send message to room {room}: {e}")
                    # Remove broken connection
                    self.room_connections[room].discard(consumer)
    
    async def broadcast_to_all(self, message: WebSocketMessage):
        """Broadcast message to all connected users"""
        all_consumers = set()
        for connections in self.user_connections.values():
            all_consumers.update(connections)
        
        for consumer in all_consumers:
            try:
                await consumer.send(text_data=json.dumps(
                    message.to_dict(),
                    cls=DjangoJSONEncoder
                ))
            except Exception as e:
                logger.error(f"Failed to broadcast message: {e}")
    
    async def _send_queued_messages(self, user_id: int, consumer: AsyncWebsocketConsumer):
        """Send queued messages to newly connected user"""
        if user_id in self.message_queue:
            messages = self.message_queue[user_id].copy()
            del self.message_queue[user_id]
            
            for message in messages:
                try:
                    await consumer.send(text_data=json.dumps(
                        message.to_dict(),
                        cls=DjangoJSONEncoder
                    ))
                except Exception as e:
                    logger.error(f"Failed to send queued message: {e}")
    
    def get_online_users(self) -> List[int]:
        """Get list of online user IDs"""
        return list(self.user_connections.keys())
    
    def get_room_users(self, room: str) -> int:
        """Get count of users in a room"""
        return len(self.room_connections.get(room, set()))
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            'total_connections': sum(len(conns) for conns in self.user_connections.values()),
            'unique_users': len(self.user_connections),
            'active_rooms': len(self.room_connections),
            'queued_messages': sum(len(msgs) for msgs in self.message_queue.values()),
            'online_users': self.get_online_users()
        }


# Global connection manager
connection_manager = ConnectionManager()


class EnterpriseWebSocketConsumer(AsyncWebsocketConsumer):
    """
    Enterprise WebSocket consumer with advanced features
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_id: Optional[int] = None
        self.rooms: Set[str] = set()
        self.last_heartbeat: datetime = datetime.now()
    
    async def connect(self):
        """Handle WebSocket connection"""
        # Get user from scope (set by authentication middleware)
        user = self.scope.get('user')
        
        if not user or not user.is_authenticated:
            await self.close(code=4001)  # Unauthorized
            return
        
        self.user_id = user.id
        
        # Accept connection
        await self.accept()
        
        # Register with connection manager
        metadata = {
            'connected_at': datetime.now(),
            'user_agent': self.scope.get('headers', {}).get(b'user-agent', b'').decode(),
            'ip_address': self.scope.get('client', ['unknown'])[0]
        }
        
        await connection_manager.connect_user(self, self.user_id, metadata)
        
        # Join user-specific room
        user_room = f"user_{self.user_id}"
        await connection_manager.join_room(self, user_room)
        self.rooms.add(user_room)
        
        # Join role-based room
        role_room = f"role_{user.role}"
        await connection_manager.join_room(self, role_room)
        self.rooms.add(role_room)
        
        # Send welcome message
        welcome_message = WebSocketMessage(
            event_type=EventType.SYSTEM_MESSAGE,
            data={
                'message': 'Connected to UMLS real-time system',
                'features': ['enrollment_updates', 'notifications', 'live_analytics']
            },
            timestamp=datetime.now(),
            user_id=self.user_id
        )
        
        await self.send(text_data=json.dumps(welcome_message.to_dict(), cls=DjangoJSONEncoder))
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if self.user_id:
            await connection_manager.disconnect_user(self, self.user_id)
        
        # Leave all rooms
        for room in self.rooms:
            await connection_manager.leave_room(self, room)
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'heartbeat':
                await self._handle_heartbeat()
            elif message_type == 'join_room':
                await self._handle_join_room(data.get('room'))
            elif message_type == 'leave_room':
                await self._handle_leave_room(data.get('room'))
            elif message_type == 'subscribe_course':
                await self._handle_subscribe_course(data.get('course_id'))
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
    
    async def _handle_heartbeat(self):
        """Handle heartbeat message"""
        self.last_heartbeat = datetime.now()
        
        heartbeat_response = WebSocketMessage(
            event_type=EventType.SYSTEM_MESSAGE,
            data={'type': 'heartbeat_ack', 'timestamp': datetime.now().isoformat()},
            timestamp=datetime.now(),
            user_id=self.user_id
        )
        
        await self.send(text_data=json.dumps(heartbeat_response.to_dict(), cls=DjangoJSONEncoder))
    
    async def _handle_join_room(self, room: str):
        """Handle room join request"""
        if room and await self._can_join_room(room):
            await connection_manager.join_room(self, room)
            self.rooms.add(room)
            
            response = WebSocketMessage(
                event_type=EventType.SYSTEM_MESSAGE,
                data={'type': 'room_joined', 'room': room},
                timestamp=datetime.now(),
                user_id=self.user_id
            )
            
            await self.send(text_data=json.dumps(response.to_dict(), cls=DjangoJSONEncoder))
    
    async def _handle_leave_room(self, room: str):
        """Handle room leave request"""
        if room in self.rooms:
            await connection_manager.leave_room(self, room)
            self.rooms.discard(room)
            
            response = WebSocketMessage(
                event_type=EventType.SYSTEM_MESSAGE,
                data={'type': 'room_left', 'room': room},
                timestamp=datetime.now(),
                user_id=self.user_id
            )
            
            await self.send(text_data=json.dumps(response.to_dict(), cls=DjangoJSONEncoder))
    
    async def _handle_subscribe_course(self, course_id: int):
        """Handle course subscription for real-time updates"""
        if course_id and await self._can_access_course(course_id):
            course_room = f"course_{course_id}"
            await connection_manager.join_room(self, course_room)
            self.rooms.add(course_room)
            
            response = WebSocketMessage(
                event_type=EventType.SYSTEM_MESSAGE,
                data={'type': 'course_subscribed', 'course_id': course_id},
                timestamp=datetime.now(),
                user_id=self.user_id
            )
            
            await self.send(text_data=json.dumps(response.to_dict(), cls=DjangoJSONEncoder))
    
    @database_sync_to_async
    def _can_join_room(self, room: str) -> bool:
        """Check if user can join a specific room"""
        # Implement room access control logic
        if room.startswith('course_'):
            course_id = room.split('_')[1]
            return self._can_access_course(int(course_id))
        elif room.startswith('department_'):
            # Check department access
            return True  # Implement department access logic
        else:
            return False
    
    @database_sync_to_async
    def _can_access_course(self, course_id: int) -> bool:
        """Check if user can access a specific course"""
        from courses.models import Course, Enrollment
        
        try:
            course = Course.objects.get(id=course_id)
            user = User.objects.get(id=self.user_id)
            
            # Students can access courses they're enrolled in
            if user.role == 'student':
                return Enrollment.objects.filter(
                    student=user,
                    course=course,
                    is_active=True
                ).exists()
            
            # Teachers can access courses they teach
            elif user.role == 'teacher':
                return course.instructor == user
            
            # Admins can access all courses
            elif user.role in ['admin', 'super_admin']:
                return True
            
            return False
            
        except (Course.DoesNotExist, User.DoesNotExist):
            return False
