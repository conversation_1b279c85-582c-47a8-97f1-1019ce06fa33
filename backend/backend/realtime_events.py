"""
Real-time Event Broadcasting Service
30+ Years of Event-Driven Architecture Expertise

Features:
- Real-time enrollment updates
- Live notifications
- Course status changes
- Grade updates
- System announcements
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

from backend.websocket_config import (
    connection_manager, 
    WebSocketMessage, 
    EventType
)

logger = logging.getLogger('realtime')
User = get_user_model()


class RealTimeEventBroadcaster:
    """
    Enterprise real-time event broadcasting service
    
    Handles broadcasting of real-time events to connected WebSocket clients
    with intelligent routing and message optimization
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    async def broadcast_enrollment_update(self, enrollment_data: Dict[str, Any], 
                                        affected_users: List[int] = None):
        """
        Broadcast enrollment status updates
        """
        message = WebSocketMessage(
            event_type=EventType.ENROLLMENT_UPDATE,
            data={
                'enrollment_id': enrollment_data.get('id'),
                'student_id': enrollment_data.get('student_id'),
                'course_id': enrollment_data.get('course_id'),
                'status': enrollment_data.get('status'),
                'attempt_number': enrollment_data.get('attempt_number'),
                'is_retake': enrollment_data.get('is_retake', False),
                'enrolled_at': enrollment_data.get('enrolled_at'),
                'message': self._get_enrollment_message(enrollment_data)
            },
            timestamp=datetime.now(),
            priority='high'
        )
        
        # Send to affected users
        if affected_users:
            for user_id in affected_users:
                await connection_manager.send_to_user(user_id, message)
        
        # Send to course room
        course_id = enrollment_data.get('course_id')
        if course_id:
            course_room = f"course_{course_id}"
            await connection_manager.send_to_room(course_room, message)
        
        # Send to admin/teacher rooms
        await connection_manager.send_to_room("role_admin", message)
        await connection_manager.send_to_room("role_teacher", message)
        
        logger.info(f"Broadcasted enrollment update: {enrollment_data.get('id')}")
    
    async def broadcast_course_update(self, course_data: Dict[str, Any]):
        """
        Broadcast course information updates
        """
        message = WebSocketMessage(
            event_type=EventType.COURSE_UPDATE,
            data={
                'course_id': course_data.get('id'),
                'title': course_data.get('title'),
                'code': course_data.get('code'),
                'max_students': course_data.get('max_students'),
                'enrolled_count': course_data.get('enrolled_count', 0),
                'waitlisted_count': course_data.get('waitlisted_count', 0),
                'available_spots': course_data.get('available_spots', 0),
                'is_full': course_data.get('is_full', False),
                'is_published': course_data.get('is_published', True),
                'message': f"Course {course_data.get('code')} has been updated"
            },
            timestamp=datetime.now(),
            priority='normal'
        )
        
        # Send to course room
        course_room = f"course_{course_data.get('id')}"
        await connection_manager.send_to_room(course_room, message)
        
        # Send to all students (for course discovery)
        await connection_manager.send_to_room("role_student", message)
        
        logger.info(f"Broadcasted course update: {course_data.get('id')}")
    
    async def broadcast_grade_update(self, grade_data: Dict[str, Any]):
        """
        Broadcast grade updates to students
        """
        message = WebSocketMessage(
            event_type=EventType.GRADE_UPDATE,
            data={
                'enrollment_id': grade_data.get('enrollment_id'),
                'student_id': grade_data.get('student_id'),
                'course_id': grade_data.get('course_id'),
                'course_code': grade_data.get('course_code'),
                'grade': grade_data.get('grade'),
                'letter_grade': grade_data.get('letter_grade'),
                'gpa_points': grade_data.get('gpa_points'),
                'message': f"New grade available for {grade_data.get('course_code')}"
            },
            timestamp=datetime.now(),
            priority='high'
        )
        
        # Send to specific student
        student_id = grade_data.get('student_id')
        if student_id:
            await connection_manager.send_to_user(student_id, message)
        
        logger.info(f"Broadcasted grade update to student {student_id}")
    
    async def broadcast_notification(self, notification_data: Dict[str, Any], 
                                   target_users: List[int] = None,
                                   target_roles: List[str] = None):
        """
        Broadcast notifications to users
        """
        message = WebSocketMessage(
            event_type=EventType.NOTIFICATION,
            data={
                'id': notification_data.get('id'),
                'title': notification_data.get('title'),
                'message': notification_data.get('message'),
                'type': notification_data.get('type', 'info'),
                'priority': notification_data.get('priority', 'normal'),
                'action_url': notification_data.get('action_url'),
                'expires_at': notification_data.get('expires_at'),
                'created_at': notification_data.get('created_at')
            },
            timestamp=datetime.now(),
            priority=notification_data.get('priority', 'normal')
        )
        
        # Send to specific users
        if target_users:
            for user_id in target_users:
                await connection_manager.send_to_user(user_id, message)
        
        # Send to specific roles
        if target_roles:
            for role in target_roles:
                role_room = f"role_{role}"
                await connection_manager.send_to_room(role_room, message)
        
        # If no specific targets, broadcast to all
        if not target_users and not target_roles:
            await connection_manager.broadcast_to_all(message)
        
        logger.info(f"Broadcasted notification: {notification_data.get('title')}")
    
    async def broadcast_announcement(self, announcement_data: Dict[str, Any]):
        """
        Broadcast system announcements
        """
        message = WebSocketMessage(
            event_type=EventType.ANNOUNCEMENT,
            data={
                'id': announcement_data.get('id'),
                'title': announcement_data.get('title'),
                'content': announcement_data.get('content'),
                'author': announcement_data.get('author'),
                'course_id': announcement_data.get('course_id'),
                'is_urgent': announcement_data.get('is_urgent', False),
                'published_at': announcement_data.get('published_at')
            },
            timestamp=datetime.now(),
            priority='high' if announcement_data.get('is_urgent') else 'normal'
        )
        
        # Send to course room if course-specific
        course_id = announcement_data.get('course_id')
        if course_id:
            course_room = f"course_{course_id}"
            await connection_manager.send_to_room(course_room, message)
        else:
            # System-wide announcement
            await connection_manager.broadcast_to_all(message)
        
        logger.info(f"Broadcasted announcement: {announcement_data.get('title')}")
    
    async def broadcast_analytics_update(self, analytics_data: Dict[str, Any]):
        """
        Broadcast real-time analytics updates
        """
        message = WebSocketMessage(
            event_type=EventType.REAL_TIME_ANALYTICS,
            data=analytics_data,
            timestamp=datetime.now(),
            priority='low'
        )
        
        # Send to admin and teacher roles only
        await connection_manager.send_to_room("role_admin", message)
        await connection_manager.send_to_room("role_teacher", message)
        
        logger.info("Broadcasted analytics update")
    
    def _get_enrollment_message(self, enrollment_data: Dict[str, Any]) -> str:
        """
        Generate user-friendly enrollment message
        """
        status = enrollment_data.get('status')
        course_code = enrollment_data.get('course_code', 'Course')
        
        messages = {
            'enrolled': f"Successfully enrolled in {course_code}",
            'waitlisted': f"Added to waitlist for {course_code}",
            'dropped': f"Dropped from {course_code}",
            'completed': f"Completed {course_code}",
            'failed': f"Failed {course_code} - retake available",
        }
        
        return messages.get(status, f"Enrollment status updated for {course_code}")


# Global broadcaster instance
event_broadcaster = RealTimeEventBroadcaster()


# Django Signal Handlers for Real-time Updates
@receiver(post_save, sender='courses.Enrollment')
def handle_enrollment_update(sender, instance, created, **kwargs):
    """
    Handle enrollment model changes and broadcast updates
    """
    try:
        enrollment_data = {
            'id': instance.id,
            'student_id': instance.student.id,
            'course_id': instance.course.id,
            'course_code': instance.course.code,
            'status': instance.status,
            'attempt_number': instance.attempt_number,
            'is_retake': instance.is_retake,
            'enrolled_at': instance.enrolled_at.isoformat() if instance.enrolled_at else None,
        }
        
        # Determine affected users
        affected_users = [instance.student.id]
        
        # Add instructor if exists
        if instance.course.instructor:
            affected_users.append(instance.course.instructor.id)
        
        # Broadcast the update
        async_to_sync(event_broadcaster.broadcast_enrollment_update)(
            enrollment_data, 
            affected_users
        )
        
    except Exception as e:
        logger.error(f"Failed to broadcast enrollment update: {e}")


@receiver(post_save, sender='courses.Course')
def handle_course_update(sender, instance, created, **kwargs):
    """
    Handle course model changes and broadcast updates
    """
    try:
        course_data = {
            'id': instance.id,
            'title': instance.title,
            'code': instance.code,
            'max_students': instance.max_students,
            'enrolled_count': instance.enrolled_students_count,
            'waitlisted_count': instance.waitlisted_students_count,
            'available_spots': instance.available_spots,
            'is_full': instance.is_full,
            'is_published': instance.is_published,
        }
        
        # Broadcast the update
        async_to_sync(event_broadcaster.broadcast_course_update)(course_data)
        
    except Exception as e:
        logger.error(f"Failed to broadcast course update: {e}")


# Utility functions for manual broadcasting
def broadcast_system_message(message: str, priority: str = 'normal'):
    """
    Broadcast a system-wide message
    """
    message_data = WebSocketMessage(
        event_type=EventType.SYSTEM_MESSAGE,
        data={
            'message': message,
            'timestamp': datetime.now().isoformat()
        },
        timestamp=datetime.now(),
        priority=priority
    )
    
    async_to_sync(connection_manager.broadcast_to_all)(message_data)


def broadcast_maintenance_notification(start_time: datetime, duration_minutes: int):
    """
    Broadcast maintenance notification
    """
    notification_data = {
        'title': 'Scheduled Maintenance',
        'message': f'System maintenance scheduled for {start_time.strftime("%Y-%m-%d %H:%M")} '
                  f'(Duration: {duration_minutes} minutes)',
        'type': 'warning',
        'priority': 'high',
        'created_at': datetime.now().isoformat()
    }
    
    async_to_sync(event_broadcaster.broadcast_notification)(notification_data)


def get_realtime_stats() -> Dict[str, Any]:
    """
    Get real-time system statistics
    """
    return {
        'websocket_connections': connection_manager.get_connection_stats(),
        'timestamp': datetime.now().isoformat()
    }
