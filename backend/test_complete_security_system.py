#!/usr/bin/env python
"""
COMPREHENSIVE SECURITY SYSTEM VALIDATION
Tests all security fixes and ensures the enrollment system is bulletproof
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from courses.models import Course, Enrollment, Department, EnrollmentAttemptLog
from courses.enrollment_security import EnrollmentSecurityManager
from courses.monitoring import enrollment_monitor

User = get_user_model()

def test_complete_security_system():
    """Comprehensive test of the entire security system"""
    print("🔒 COMPREHENSIVE SECURITY SYSTEM VALIDATION")
    print("=" * 60)
    
    # Clear cache and clean up
    cache.clear()
    User.objects.filter(username__startswith='security_test_').delete()
    Department.objects.filter(code='SECURITY').delete()
    
    try:
        # Create test data
        print("\n📝 Creating test data...")
        
        department = Department.objects.create(name="Security Test", code="SECURITY")
        teacher = User.objects.create_user(
            username="security_test_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        student = User.objects.create_user(
            username="security_test_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        admin = User.objects.create_user(
            username="security_test_admin", email="<EMAIL>", 
            password="password123", role="admin"
        )
        course = Course.objects.create(
            title="Security Test Course", code="SECURITY101", description="Test",
            department=department, instructor=teacher, level="undergraduate", 
            credit_hours=3, semester="spring", year=2025, max_students=30, 
            is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
        
        print("✅ Test data created successfully")
        
        # Test 1: IRONCLAD COMPLETED COURSE PROTECTION
        print("\n🔍 Test 1: Ironclad Completed Course Protection")
        
        # Complete the course
        completed_enrollment = Enrollment.objects.create(
            student=student, course=course, status='completed'
        )
        print("  📚 Course marked as completed")
        
        # Try to enroll again - should be BLOCKED
        try:
            enrollment, _ = course.enroll_student(student)
            print("  ❌ SECURITY FAILURE: Re-enrollment in completed course was allowed!")
            return False
        except ValueError as e:
            if "completed course" in str(e):
                print("  ✅ SECURITY SUCCESS: Re-enrollment in completed course blocked")
            else:
                print(f"  ❌ SECURITY FAILURE: Wrong error message: {e}")
                return False
        
        # Try admin override for completed course - should STILL be blocked
        try:
            enrollment, _ = course.enroll_student(
                student, admin_override=True, override_reason="Admin trying to override"
            )
            print("  ❌ SECURITY FAILURE: Admin override allowed re-enrollment in completed course!")
            return False
        except ValueError as e:
            if "completed course" in str(e):
                print("  ✅ SECURITY SUCCESS: Even admin override blocked for completed course")
            else:
                print(f"  ❌ SECURITY FAILURE: Wrong error message: {e}")
                return False
        
        # Clean up for next test
        completed_enrollment.delete()
        
        # Test 2: TOTAL ATTEMPT LIMITS
        print("\n🔍 Test 2: Total Attempt Limits (Not Just Failed)")
        
        # Create 3 attempts with different statuses
        for i, status in enumerate(['dropped', 'failed', 'dropped']):
            enrollment = Enrollment.objects.create(
                student=student, course=course, status=status
            )
            EnrollmentAttemptLog.log_attempt(
                student=student, course=course,
                attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                enrollment_status=status
            )
            print(f"  📝 Created attempt {i+1} with status: {status}")
        
        # 4th attempt should be blocked
        try:
            enrollment, _ = course.enroll_student(student)
            print("  ❌ SECURITY FAILURE: 4th enrollment attempt was allowed!")
            return False
        except ValueError as e:
            if "Maximum total attempts exceeded" in str(e):
                print("  ✅ SECURITY SUCCESS: 4th attempt blocked due to total limit")
            else:
                print(f"  ❌ SECURITY FAILURE: Wrong error message: {e}")
                return False
        
        # Clean up for next test
        Enrollment.objects.filter(student=student, course=course).delete()
        EnrollmentAttemptLog.objects.filter(student=student, course=course).delete()
        
        # Test 3: RATE LIMITING
        print("\n🔍 Test 3: Rate Limiting Protection")
        
        security_manager = EnrollmentSecurityManager(student, course)
        
        # First attempt should pass
        result1 = security_manager.validate_enrollment()
        if result1.allowed:
            print("  ✅ First attempt allowed")
        else:
            print("  ❌ SECURITY FAILURE: First attempt blocked incorrectly")
            return False
        
        # Immediate second attempt should be blocked
        result2 = security_manager.validate_enrollment()
        if not result2.allowed and "enrollment attempts" in result2.message:
            print("  ✅ SECURITY SUCCESS: Rapid attempts blocked by rate limiting")
        else:
            print("  ❌ SECURITY FAILURE: Rate limiting not working")
            return False
        
        # Test 4: MONITORING AND ALERTING
        print("\n🔍 Test 4: Security Monitoring and Alerting")
        
        # Create suspicious pattern
        for i in range(12):
            EnrollmentAttemptLog.log_attempt(
                student=student, course=course,
                attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                enrollment_status='blocked',
                risk_score=85
            )
        
        # Monitor should detect suspicious pattern
        alerts = enrollment_monitor.monitor_enrollment_attempt(
            student=student, course=course, attempt_type='enrollment',
            enrollment_status='blocked', risk_score=85
        )
        
        if alerts and any('Suspicious' in alert.title for alert in alerts):
            print("  ✅ SECURITY SUCCESS: Suspicious pattern detected and alerted")
        else:
            print("  ❌ SECURITY FAILURE: Monitoring system not detecting suspicious patterns")
            return False
        
        # Test 5: API SECURITY INTEGRATION
        print("\n🔍 Test 5: API Security Integration")
        
        # Clean up for API test
        Enrollment.objects.filter(student=student, course=course).delete()
        EnrollmentAttemptLog.objects.filter(student=student, course=course).delete()
        cache.clear()
        
        # Test normal enrollment through secure API
        try:
            enrollment, _ = course.enroll_student(student)
            if enrollment and enrollment.status == 'enrolled':
                print("  ✅ SECURITY SUCCESS: Normal enrollment works through secure API")
            else:
                print("  ❌ SECURITY FAILURE: Normal enrollment failed")
                return False
        except Exception as e:
            print(f"  ❌ SECURITY FAILURE: Normal enrollment failed: {e}")
            return False
        
        # Test 6: AUDIT TRAIL VERIFICATION
        print("\n🔍 Test 6: Audit Trail Verification")
        
        # Check that enrollment was logged
        log_entry = EnrollmentAttemptLog.objects.filter(
            student=student, course=course
        ).first()
        
        if log_entry:
            print("  ✅ SECURITY SUCCESS: Enrollment attempt logged in audit trail")
            print(f"    📊 Log details: {log_entry.attempt_type}, Risk: {log_entry.risk_score}")
        else:
            print("  ❌ SECURITY FAILURE: Enrollment not logged in audit trail")
            return False
        
        # Test 7: COMPREHENSIVE VALIDATION LAYERS
        print("\n🔍 Test 7: Defense-in-Depth Validation Layers")
        
        security_manager = EnrollmentSecurityManager(student, course)
        result = security_manager.validate_enrollment()
        
        expected_layers = [
            'basic_eligibility', 'completion_protection', 'attempt_limits',
            'rate_limits', 'academic_integrity', 'prerequisites', 'capacity_scheduling'
        ]
        
        validated_layers = [layer[0] for layer in result.audit_data.get('validation_layers', [])]
        
        if all(layer in validated_layers for layer in expected_layers):
            print("  ✅ SECURITY SUCCESS: All validation layers executed")
            print(f"    🔍 Layers validated: {', '.join(validated_layers)}")
        else:
            missing = [layer for layer in expected_layers if layer not in validated_layers]
            print(f"  ❌ SECURITY FAILURE: Missing validation layers: {missing}")
            return False
        
        # Test 8: SECURITY REPORTING
        print("\n🔍 Test 8: Security Reporting System")
        
        # Generate security report
        report = enrollment_monitor.generate_security_report(days=1)
        
        if report and 'total_attempts' in report:
            print("  ✅ SECURITY SUCCESS: Security reporting system functional")
            print(f"    📊 Report summary: {report['total_attempts']} attempts, {report['blocked_attempts']} blocked")
        else:
            print("  ❌ SECURITY FAILURE: Security reporting system not working")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 ALL SECURITY TESTS PASSED!")
        print("🛡️  ENROLLMENT SYSTEM IS NOW BULLETPROOF!")
        print("=" * 60)
        
        print("\n📊 SECURITY SYSTEM SUMMARY:")
        print("✅ Ironclad completed course protection")
        print("✅ Comprehensive attempt limits (total + failed)")
        print("✅ Rate limiting and anti-spam protection")
        print("✅ Real-time security monitoring")
        print("✅ Comprehensive audit trails")
        print("✅ Defense-in-depth validation (7 layers)")
        print("✅ API security integration")
        print("✅ Database-level constraints")
        print("✅ Anomaly detection and alerting")
        print("✅ Security reporting and analytics")
        
        return True
        
    except Exception as e:
        print(f"\n❌ SECURITY TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test data
        print("\n🧹 Cleaning up test data...")
        User.objects.filter(username__startswith='security_test_').delete()
        Department.objects.filter(code='SECURITY').delete()
        cache.clear()

if __name__ == "__main__":
    success = test_complete_security_system()
    sys.exit(0 if success else 1)
