"""
Enterprise Database Optimization Management Command

Usage:
    python manage.py optimize_database --full
    python manage.py optimize_database --analyze-only
    python manage.py optimize_database --report
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import connection
from backend.database_optimization import (
    EnterpriseDBOptimizer, 
    setup_database_monitoring,
    check_database_health
)
import json
from datetime import datetime


class Command(BaseCommand):
    help = 'Enterprise database optimization and performance analysis'

    def add_arguments(self, parser):
        parser.add_argument(
            '--full',
            action='store_true',
            help='Perform full database optimization including vacuum and analyze',
        )
        parser.add_argument(
            '--analyze-only',
            action='store_true',
            help='Only analyze performance without making changes',
        )
        parser.add_argument(
            '--report',
            action='store_true',
            help='Generate comprehensive performance report',
        )
        parser.add_argument(
            '--health-check',
            action='store_true',
            help='Perform database health check',
        )
        parser.add_argument(
            '--setup-monitoring',
            action='store_true',
            help='Setup database performance monitoring',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Enterprise Database Optimization Tool')
        )
        self.stdout.write('=' * 60)
        
        optimizer = EnterpriseDBOptimizer()
        
        try:
            if options['health_check']:
                self.perform_health_check()
            
            elif options['setup_monitoring']:
                self.setup_monitoring()
            
            elif options['report']:
                self.generate_performance_report(optimizer)
            
            elif options['analyze_only']:
                self.analyze_performance(optimizer)
            
            elif options['full']:
                self.perform_full_optimization(optimizer)
            
            else:
                # Default: basic optimization
                self.perform_basic_optimization(optimizer)
                
        except Exception as e:
            raise CommandError(f'Optimization failed: {str(e)}')

    def perform_health_check(self):
        """Perform comprehensive database health check"""
        self.stdout.write('\n🔍 Performing Database Health Check...')
        
        health_report = check_database_health()
        
        self.stdout.write(f"\n📊 Database Health Report:")
        self.stdout.write(f"   Database Size: {health_report['database_size_mb']} MB")
        self.stdout.write(f"   Total Pages: {health_report['total_pages']:,}")
        self.stdout.write(f"   Page Size: {health_report['page_size']} bytes")
        self.stdout.write(f"   Total Indexes: {health_report['total_indexes']}")
        self.stdout.write(f"   Tables with Indexes: {health_report['tables_with_indexes']}")
        
        status_color = self.style.SUCCESS if health_report['status'] == 'healthy' else self.style.WARNING
        self.stdout.write(f"   Status: {status_color(health_report['status'].upper())}")
        
        if health_report['recommendations']:
            self.stdout.write(f"\n💡 Recommendations:")
            for rec in health_report['recommendations']:
                self.stdout.write(f"   • {rec}")
        
        self.stdout.write(self.style.SUCCESS('\n✅ Health check completed'))

    def setup_monitoring(self):
        """Setup database performance monitoring"""
        self.stdout.write('\n⚙️  Setting up Database Performance Monitoring...')
        
        optimizer = setup_database_monitoring()
        
        self.stdout.write('   • Applied SQLite optimizations')
        self.stdout.write('   • Configured connection pooling')
        self.stdout.write('   • Setup query performance tracking')
        self.stdout.write('   • Enabled slow query logging')
        
        self.stdout.write(self.style.SUCCESS('\n✅ Monitoring setup completed'))

    def perform_basic_optimization(self, optimizer):
        """Perform basic database optimization"""
        self.stdout.write('\n🔧 Performing Basic Database Optimization...')
        
        # Apply connection optimizations
        optimizer.optimize_sqlite_connection(connection)
        self.stdout.write('   • Applied SQLite connection optimizations')
        
        # Optimize enrollment queries
        optimizer.optimize_enrollment_queries()
        self.stdout.write('   • Optimized enrollment system queries')
        
        # Get index statistics
        index_stats = optimizer.get_index_usage_stats()
        self.stdout.write(f'   • Found {index_stats["total_indexes"]} indexes across {len(index_stats["indexes_by_table"])} tables')
        
        self.stdout.write(self.style.SUCCESS('\n✅ Basic optimization completed'))

    def perform_full_optimization(self, optimizer):
        """Perform comprehensive database optimization"""
        self.stdout.write('\n🚀 Performing Full Database Optimization...')
        
        # Basic optimizations first
        self.perform_basic_optimization(optimizer)
        
        # Full maintenance
        self.stdout.write('\n🔧 Performing Database Maintenance...')
        optimizer.vacuum_and_analyze()
        self.stdout.write('   • Database vacuum completed')
        self.stdout.write('   • Table analysis completed')
        self.stdout.write('   • Query planner optimized')
        
        # Performance analysis
        self.stdout.write('\n📊 Analyzing Performance...')
        self.analyze_performance(optimizer)
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Full optimization completed successfully!'))

    def analyze_performance(self, optimizer):
        """Analyze database performance"""
        self.stdout.write('\n📈 Analyzing Database Performance...')
        
        # Test critical enrollment queries
        test_queries = [
            # Student enrollment status check
            """
            SELECT COUNT(*) FROM courses_enrollment 
            WHERE student_id = 1 AND is_active = true
            """,
            
            # Course enrollment count
            """
            SELECT COUNT(*) FROM courses_enrollment 
            WHERE course_id = 1 AND status = 'enrolled' AND is_active = true
            """,
            
            # Retake eligibility check
            """
            SELECT * FROM courses_enrollment 
            WHERE student_id = 1 AND course_id = 1 
            ORDER BY attempt_number DESC LIMIT 1
            """,
            
            # Course listing with enrollment counts
            """
            SELECT c.id, c.title, c.code, COUNT(e.id) as enrollment_count
            FROM courses_course c
            LEFT JOIN courses_enrollment e ON c.id = e.course_id 
                AND e.status = 'enrolled' AND e.is_active = true
            WHERE c.is_published = true AND c.is_active = true
            GROUP BY c.id, c.title, c.code
            LIMIT 10
            """,
        ]
        
        total_time = 0
        for i, query in enumerate(test_queries, 1):
            metrics = optimizer.analyze_query_performance(query.strip())
            total_time += metrics.execution_time
            
            self.stdout.write(f'   Query {i}: {metrics.execution_time:.3f}s ({metrics.rows_returned} rows)')
            
            if metrics.execution_time > 0.1:
                self.stdout.write(
                    self.style.WARNING(f'     ⚠️  Slow query detected!')
                )
        
        self.stdout.write(f'\n   Total test time: {total_time:.3f}s')
        
        # Show index usage
        index_stats = optimizer.get_index_usage_stats()
        self.stdout.write(f'\n📋 Index Statistics:')
        for table, indexes in index_stats['indexes_by_table'].items():
            self.stdout.write(f'   {table}: {len(indexes)} indexes')

    def generate_performance_report(self, optimizer):
        """Generate comprehensive performance report"""
        self.stdout.write('\n📊 Generating Performance Report...')
        
        # Run some test queries first
        self.analyze_performance(optimizer)
        
        # Get performance report
        report = optimizer.get_performance_report()
        
        if 'message' in report:
            self.stdout.write(f'   {report["message"]}')
            return
        
        self.stdout.write(f'\n📈 Performance Metrics:')
        self.stdout.write(f'   Total Queries: {report["total_queries"]}')
        self.stdout.write(f'   Average Execution Time: {report["avg_execution_time"]:.3f}s')
        self.stdout.write(f'   Max Execution Time: {report["max_execution_time"]:.3f}s')
        self.stdout.write(f'   Min Execution Time: {report["min_execution_time"]:.3f}s')
        self.stdout.write(f'   Slow Queries: {report["slow_queries_count"]} ({report["slow_query_percentage"]:.1f}%)')
        
        if report['recent_slow_queries']:
            self.stdout.write(f'\n🐌 Recent Slow Queries:')
            for query in report['recent_slow_queries']:
                self.stdout.write(f'   • {query["execution_time"]:.3f}s: {query["query"]}')
        
        # Save detailed report to file
        report_file = f'db_performance_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.stdout.write(f'\n💾 Detailed report saved to: {report_file}')
        self.stdout.write(self.style.SUCCESS('\n✅ Performance report generated'))
