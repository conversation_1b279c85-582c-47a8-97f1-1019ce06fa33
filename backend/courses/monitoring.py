"""
Comprehensive Enrollment Monitoring and Auditing System
Real-time monitoring, anomaly detection, and security alerting
"""

from django.core.cache import cache
from django.utils import timezone
from django.db.models import Count, Q
from django.core.mail import send_mail
from django.conf import settings
from typing import Dict, List, Optional
import logging
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """Security alert levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityAlert:
    """Security alert data structure"""
    level: AlertLevel
    title: str
    description: str
    student_id: Optional[int]
    course_id: Optional[int]
    risk_score: int
    timestamp: str
    metadata: Dict


class EnrollmentMonitor:
    """
    Real-time enrollment monitoring and anomaly detection system
    Monitors enrollment patterns and detects suspicious activities
    """
    
    # Alert thresholds
    RAPID_ATTEMPTS_THRESHOLD = 10  # Attempts per hour
    HIGH_RISK_SCORE_THRESHOLD = 80
    FAILED_ATTEMPTS_THRESHOLD = 5  # Per day
    SUSPICIOUS_PATTERN_THRESHOLD = 15  # Attempts per day
    
    def __init__(self):
        self.cache_prefix = "enrollment_monitor"
        
    def monitor_enrollment_attempt(self, student, course, attempt_type, 
                                 enrollment_status, risk_score, 
                                 ip_address=None, user_agent=None):
        """Monitor and analyze enrollment attempt for suspicious patterns"""
        
        alerts = []
        
        # Check for rapid attempts
        rapid_alert = self._check_rapid_attempts(student, course)
        if rapid_alert:
            alerts.append(rapid_alert)
        
        # Check for high risk scores
        if risk_score >= self.HIGH_RISK_SCORE_THRESHOLD:
            alerts.append(SecurityAlert(
                level=AlertLevel.HIGH,
                title="High Risk Enrollment Attempt",
                description=f"Enrollment attempt with risk score {risk_score}",
                student_id=student.id,
                course_id=course.id,
                risk_score=risk_score,
                timestamp=timezone.now().isoformat(),
                metadata={
                    'attempt_type': attempt_type,
                    'enrollment_status': enrollment_status,
                    'ip_address': ip_address,
                    'user_agent': user_agent
                }
            ))
        
        # Check for suspicious patterns
        pattern_alert = self._check_suspicious_patterns(student)
        if pattern_alert:
            alerts.append(pattern_alert)
        
        # Check for IP-based anomalies
        ip_alert = self._check_ip_anomalies(student, ip_address)
        if ip_alert:
            alerts.append(ip_alert)
        
        # Process alerts
        for alert in alerts:
            self._process_alert(alert)
        
        # Update monitoring cache
        self._update_monitoring_cache(student, course, attempt_type, risk_score)
        
        return alerts
    
    def _check_rapid_attempts(self, student, course) -> Optional[SecurityAlert]:
        """Check for rapid enrollment attempts"""
        cache_key = f"{self.cache_prefix}:rapid:{student.id}:{course.id}"
        attempts = cache.get(cache_key, 0)
        
        if attempts >= self.RAPID_ATTEMPTS_THRESHOLD:
            return SecurityAlert(
                level=AlertLevel.HIGH,
                title="Rapid Enrollment Attempts Detected",
                description=f"Student {student.id} made {attempts} attempts in 1 hour for course {course.id}",
                student_id=student.id,
                course_id=course.id,
                risk_score=90,
                timestamp=timezone.now().isoformat(),
                metadata={'attempts_per_hour': attempts}
            )
        
        # Increment counter
        cache.set(cache_key, attempts + 1, 3600)  # 1 hour window
        return None
    
    def _check_suspicious_patterns(self, student) -> Optional[SecurityAlert]:
        """Check for suspicious enrollment patterns across all courses"""
        from .models import EnrollmentAttemptLog
        
        # Count attempts in last 24 hours
        recent_attempts = EnrollmentAttemptLog.objects.filter(
            student=student,
            created_at__gte=timezone.now() - timezone.timedelta(hours=24)
        ).count()
        
        if recent_attempts >= self.SUSPICIOUS_PATTERN_THRESHOLD:
            return SecurityAlert(
                level=AlertLevel.CRITICAL,
                title="Suspicious Enrollment Pattern Detected",
                description=f"Student {student.id} made {recent_attempts} enrollment attempts in 24 hours",
                student_id=student.id,
                course_id=None,
                risk_score=95,
                timestamp=timezone.now().isoformat(),
                metadata={
                    'attempts_24h': recent_attempts,
                    'pattern_type': 'excessive_attempts'
                }
            )
        
        return None
    
    def _check_ip_anomalies(self, student, ip_address) -> Optional[SecurityAlert]:
        """Check for IP-based anomalies"""
        if not ip_address:
            return None
        
        from .models import EnrollmentAttemptLog
        
        # Check for multiple students from same IP
        recent_ip_attempts = EnrollmentAttemptLog.objects.filter(
            ip_address=ip_address,
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        ).values('student_id').distinct().count()
        
        if recent_ip_attempts >= 5:  # 5 different students from same IP
            return SecurityAlert(
                level=AlertLevel.MEDIUM,
                title="Multiple Students from Same IP",
                description=f"IP {ip_address} used by {recent_ip_attempts} different students in 1 hour",
                student_id=student.id,
                course_id=None,
                risk_score=70,
                timestamp=timezone.now().isoformat(),
                metadata={
                    'ip_address': ip_address,
                    'unique_students': recent_ip_attempts
                }
            )
        
        return None
    
    def _process_alert(self, alert: SecurityAlert):
        """Process security alert - log, cache, and notify if needed"""
        
        # Log alert
        log_level = {
            AlertLevel.LOW: logging.INFO,
            AlertLevel.MEDIUM: logging.WARNING,
            AlertLevel.HIGH: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }[alert.level]
        
        logger.log(log_level, f"SECURITY ALERT: {alert.title} - {alert.description}", 
                  extra=alert.metadata)
        
        # Cache alert for dashboard
        alert_cache_key = f"{self.cache_prefix}:alerts:{timezone.now().strftime('%Y%m%d')}"
        cached_alerts = cache.get(alert_cache_key, [])
        cached_alerts.append({
            'level': alert.level.value,
            'title': alert.title,
            'description': alert.description,
            'student_id': alert.student_id,
            'course_id': alert.course_id,
            'risk_score': alert.risk_score,
            'timestamp': alert.timestamp,
            'metadata': alert.metadata
        })
        cache.set(alert_cache_key, cached_alerts, 86400)  # 24 hours
        
        # Send notifications for high/critical alerts
        if alert.level in [AlertLevel.HIGH, AlertLevel.CRITICAL]:
            self._send_security_notification(alert)
    
    def _send_security_notification(self, alert: SecurityAlert):
        """Send security notification to administrators"""
        try:
            subject = f"UMLS Security Alert: {alert.title}"
            message = f"""
Security Alert Detected in UMLS Enrollment System

Level: {alert.level.value.upper()}
Title: {alert.title}
Description: {alert.description}
Risk Score: {alert.risk_score}/100
Timestamp: {alert.timestamp}

Student ID: {alert.student_id}
Course ID: {alert.course_id}

Metadata: {json.dumps(alert.metadata, indent=2)}

Please investigate this security incident immediately.
            """
            
            # Send to administrators
            admin_emails = getattr(settings, 'SECURITY_ALERT_EMAILS', ['<EMAIL>'])
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=admin_emails,
                fail_silently=True
            )
            
        except Exception as e:
            logger.error(f"Failed to send security notification: {e}")
    
    def _update_monitoring_cache(self, student, course, attempt_type, risk_score):
        """Update monitoring statistics in cache"""
        
        # Update daily stats
        today = timezone.now().strftime('%Y%m%d')
        stats_key = f"{self.cache_prefix}:stats:{today}"
        
        stats = cache.get(stats_key, {
            'total_attempts': 0,
            'blocked_attempts': 0,
            'high_risk_attempts': 0,
            'unique_students': set(),
            'unique_courses': set()
        })
        
        stats['total_attempts'] += 1
        if attempt_type == 'blocked':
            stats['blocked_attempts'] += 1
        if risk_score >= self.HIGH_RISK_SCORE_THRESHOLD:
            stats['high_risk_attempts'] += 1
        
        stats['unique_students'].add(student.id)
        stats['unique_courses'].add(course.id)
        
        # Convert sets to lists for JSON serialization
        stats_serializable = {
            **stats,
            'unique_students': list(stats['unique_students']),
            'unique_courses': list(stats['unique_courses'])
        }
        
        cache.set(stats_key, stats_serializable, 86400)  # 24 hours
    
    def get_daily_alerts(self, date=None) -> List[Dict]:
        """Get security alerts for a specific date"""
        if not date:
            date = timezone.now().strftime('%Y%m%d')
        
        alert_cache_key = f"{self.cache_prefix}:alerts:{date}"
        return cache.get(alert_cache_key, [])
    
    def get_daily_stats(self, date=None) -> Dict:
        """Get enrollment monitoring statistics for a specific date"""
        if not date:
            date = timezone.now().strftime('%Y%m%d')
        
        stats_key = f"{self.cache_prefix}:stats:{date}"
        return cache.get(stats_key, {
            'total_attempts': 0,
            'blocked_attempts': 0,
            'high_risk_attempts': 0,
            'unique_students': [],
            'unique_courses': []
        })
    
    def generate_security_report(self, days=7) -> Dict:
        """Generate comprehensive security report"""
        from .models import EnrollmentAttemptLog, Enrollment
        
        end_date = timezone.now()
        start_date = end_date - timezone.timedelta(days=days)
        
        # Get attempt statistics
        total_attempts = EnrollmentAttemptLog.objects.filter(
            created_at__gte=start_date
        ).count()
        
        blocked_attempts = EnrollmentAttemptLog.objects.filter(
            created_at__gte=start_date,
            enrollment_status='blocked'
        ).count()
        
        high_risk_attempts = EnrollmentAttemptLog.objects.filter(
            created_at__gte=start_date,
            risk_score__gte=self.HIGH_RISK_SCORE_THRESHOLD
        ).count()
        
        # Get top risk students
        top_risk_students = EnrollmentAttemptLog.objects.filter(
            created_at__gte=start_date
        ).values('student_id').annotate(
            total_attempts=Count('id'),
            avg_risk_score=models.Avg('risk_score')
        ).order_by('-avg_risk_score')[:10]
        
        # Get security alerts summary
        alerts_summary = {}
        for i in range(days):
            date = (end_date - timezone.timedelta(days=i)).strftime('%Y%m%d')
            daily_alerts = self.get_daily_alerts(date)
            alerts_summary[date] = {
                'total': len(daily_alerts),
                'critical': len([a for a in daily_alerts if a['level'] == 'critical']),
                'high': len([a for a in daily_alerts if a['level'] == 'high']),
                'medium': len([a for a in daily_alerts if a['level'] == 'medium']),
                'low': len([a for a in daily_alerts if a['level'] == 'low'])
            }
        
        return {
            'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            'total_attempts': total_attempts,
            'blocked_attempts': blocked_attempts,
            'high_risk_attempts': high_risk_attempts,
            'block_rate': (blocked_attempts / total_attempts * 100) if total_attempts > 0 else 0,
            'top_risk_students': list(top_risk_students),
            'alerts_summary': alerts_summary,
            'generated_at': timezone.now().isoformat()
        }


# Global monitor instance
enrollment_monitor = EnrollmentMonitor()
