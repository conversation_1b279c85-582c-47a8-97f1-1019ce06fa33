# Performance optimization migration - Strategic database indexes
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0010_database_security_constraints'),
    ]

    operations = [
        # Enrollment performance indexes
        migrations.RunSQL(
            """
            -- Composite index for student enrollment lookups
            CREATE INDEX IF NOT EXISTS idx_enrollment_student_active 
            ON courses_enrollment(student_id, is_active, status) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_student_active;"
        ),
        
        migrations.RunSQL(
            """
            -- Composite index for course enrollment lookups
            CREATE INDEX IF NOT EXISTS idx_enrollment_course_active 
            ON courses_enrollment(course_id, is_active, status) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_course_active;"
        ),
        
        migrations.RunSQL(
            """
            -- Index for enrollment date range queries
            CREATE INDEX IF NOT EXISTS idx_enrollment_date_range 
            ON courses_enrollment(enrollment_date, status);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_date_range;"
        ),
        
        migrations.RunSQL(
            """
            -- Index for completion tracking
            CREATE INDEX IF NOT EXISTS idx_enrollment_completion 
            ON courses_enrollment(student_id, course_id, status) 
            WHERE status IN ('completed', 'failed');
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_completion;"
        ),
        
        # Course performance indexes
        migrations.RunSQL(
            """
            -- Index for active published courses
            CREATE INDEX IF NOT EXISTS idx_course_active_published 
            ON courses_course(is_active, is_published, department_id) 
            WHERE is_active = true AND is_published = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_course_active_published;"
        ),
        
        migrations.RunSQL(
            """
            -- Index for course search by semester/year
            CREATE INDEX IF NOT EXISTS idx_course_semester_year 
            ON courses_course(semester, year, is_active) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_course_semester_year;"
        ),
        
        migrations.RunSQL(
            """
            -- Index for instructor course lookups
            CREATE INDEX IF NOT EXISTS idx_course_instructor 
            ON courses_course(instructor_id, is_active) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_course_instructor;"
        ),
        
        # Waitlist performance indexes
        migrations.RunSQL(
            """
            -- Index for waitlist position queries
            CREATE INDEX IF NOT EXISTS idx_waitlist_course_position 
            ON courses_waitlist(course_id, position, is_active) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_waitlist_course_position;"
        ),
        
        migrations.RunSQL(
            """
            -- Index for student waitlist lookups
            CREATE INDEX IF NOT EXISTS idx_waitlist_student_active 
            ON courses_waitlist(student_id, is_active, added_at) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_waitlist_student_active;"
        ),
        
        # Security and audit indexes
        migrations.RunSQL(
            """
            -- Index for security monitoring queries
            CREATE INDEX IF NOT EXISTS idx_enrollment_attempt_security 
            ON courses_enrollmentattemptlog(student_id, course_id, created_at DESC, risk_score);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_attempt_security;"
        ),
        
        migrations.RunSQL(
            """
            -- Index for high-risk attempt detection
            CREATE INDEX IF NOT EXISTS idx_enrollment_attempt_risk 
            ON courses_enrollmentattemptlog(risk_score, created_at DESC) 
            WHERE risk_score >= 80;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_attempt_risk;"
        ),
        
        # Department and user indexes
        migrations.RunSQL(
            """
            -- Index for department course queries
            CREATE INDEX IF NOT EXISTS idx_course_department_level 
            ON courses_course(department_id, level, is_active) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_course_department_level;"
        ),
        
        # Partial indexes for common queries
        migrations.RunSQL(
            """
            -- Partial index for current semester enrollments
            CREATE INDEX IF NOT EXISTS idx_enrollment_current_semester 
            ON courses_enrollment(student_id, course_id) 
            WHERE is_active = true AND status = 'enrolled';
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_current_semester;"
        ),
        
        migrations.RunSQL(
            """
            -- Partial index for failed enrollments (for retake logic)
            CREATE INDEX IF NOT EXISTS idx_enrollment_failed_retakes 
            ON courses_enrollment(student_id, course_id, attempt_number) 
            WHERE status = 'failed';
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_failed_retakes;"
        ),
        
        # Performance monitoring indexes
        migrations.RunSQL(
            """
            -- Index for enrollment statistics queries
            CREATE INDEX IF NOT EXISTS idx_enrollment_stats_course 
            ON courses_enrollment(course_id, status, enrollment_date);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_stats_course;"
        ),
        
        migrations.RunSQL(
            """
            -- Index for student academic progress
            CREATE INDEX IF NOT EXISTS idx_enrollment_student_progress 
            ON courses_enrollment(student_id, status, completion_date, grade);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_student_progress;"
        ),
        
        # Covering indexes for common SELECT queries
        migrations.RunSQL(
            """
            -- Covering index for enrollment list queries
            CREATE INDEX IF NOT EXISTS idx_enrollment_list_covering 
            ON courses_enrollment(student_id, is_active) 
            INCLUDE (course_id, status, enrollment_date, grade);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_list_covering;"
        ),
        
        # Optimize foreign key constraints
        migrations.RunSQL(
            """
            -- Ensure foreign key indexes exist for performance
            CREATE INDEX IF NOT EXISTS idx_enrollment_student_fk 
            ON courses_enrollment(student_id);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_student_fk;"
        ),
        
        migrations.RunSQL(
            """
            CREATE INDEX IF NOT EXISTS idx_enrollment_course_fk 
            ON courses_enrollment(course_id);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_course_fk;"
        ),
        
        # Text search indexes (if using PostgreSQL)
        migrations.RunSQL(
            """
            -- Full-text search index for course titles and descriptions
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_trgm') THEN
                    CREATE INDEX IF NOT EXISTS idx_course_text_search 
                    ON courses_course USING gin(title gin_trgm_ops, description gin_trgm_ops);
                END IF;
            EXCEPTION
                WHEN others THEN
                    -- Skip for non-PostgreSQL or if extension not available
                    NULL;
            END $$;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_course_text_search;"
        ),
        
        # Update table statistics for query planner
        migrations.RunSQL(
            """
            -- Update statistics for better query planning
            DO $$
            BEGIN
                ANALYZE courses_enrollment;
                ANALYZE courses_course;
                ANALYZE courses_waitlist;
                ANALYZE courses_enrollmentattemptlog;
            EXCEPTION
                WHEN others THEN
                    -- Skip for non-PostgreSQL databases
                    NULL;
            END $$;
            """,
            reverse_sql=""
        ),
    ]
