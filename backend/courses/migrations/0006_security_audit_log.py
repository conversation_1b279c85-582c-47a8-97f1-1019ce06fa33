# Generated by Django 4.2.7 on 2025-07-14 10:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0005_merge_20250714_1000'),
    ]

    operations = [
        migrations.CreateModel(
            name='SecurityAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('event_type', models.CharField(choices=[('login_success', 'Login Success'), ('login_failure', 'Login Failure'), ('logout', 'Logout'), ('password_change', 'Password Change'), ('permission_denied', 'Permission Denied'), ('suspicious_activity', 'Suspicious Activity'), ('data_access', 'Data Access'), ('data_modification', 'Data Modification'), ('rate_limit_exceeded', 'Rate Limit Exceeded'), ('security_violation', 'Security Violation')], db_index=True, max_length=50)),
                ('ip_address', models.GenericIPAddressField(db_index=True)),
                ('user_agent', models.TextField()),
                ('request_path', models.CharField(max_length=500)),
                ('request_method', models.CharField(max_length=10)),
                ('details', models.JSONField(default=dict)),
                ('risk_score', models.IntegerField(db_index=True, default=0)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['timestamp', 'event_type'], name='courses_sec_timesta_8b5c8a_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['ip_address', 'timestamp'], name='courses_sec_ip_addr_9f4e2d_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['user', 'event_type', 'timestamp'], name='courses_sec_user_id_7a3b1c_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['risk_score', 'timestamp'], name='courses_sec_risk_sc_2e9f4a_idx'),
        ),
    ]
