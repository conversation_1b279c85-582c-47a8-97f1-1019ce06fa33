# Generated migration to fix enrollment system constraints
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0004_add_retake_tracking'),
    ]

    operations = [
        # Add unique constraint: Only one active enrollment per student per course
        # This prevents multiple active enrollments while allowing retakes
        migrations.RunSQL(
            """
            CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_unique_active_enrollment 
            ON courses_enrollment(student_id, course_id) 
            WHERE is_active = true;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_unique_active_enrollment;"
        ),
        
        # Add index for better performance on enrollment status queries
        migrations.RunSQL(
            """
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_enrollment_status_lookup 
            ON courses_enrollment(student_id, course_id, is_active, status, attempt_number DESC);
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_status_lookup;"
        ),
        
        # Add index for retake limit checking
        migrations.RunSQL(
            """
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_enrollment_failed_attempts 
            ON courses_enrollment(student_id, course_id, status) 
            WHERE status = 'failed';
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_failed_attempts;"
        ),
    ]
