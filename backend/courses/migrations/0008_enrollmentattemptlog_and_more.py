# Generated by Django 4.2.7 on 2025-07-14 11:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0007_merge_20250714_1217'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnrollmentAttemptLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attempt_type', models.CharField(choices=[('enrollment', 'Enrollment'), ('drop', 'Drop'), ('withdrawal', 'Withdrawal'), ('completion', 'Completion'), ('failure', 'Failure')], max_length=20, verbose_name='attempt type')),
                ('attempt_number', models.PositiveIntegerField(verbose_name='attempt number')),
                ('enrollment_status', models.Char<PERSON><PERSON>(max_length=20, verbose_name='enrollment status')),
                ('risk_score', models.PositiveIntegerField(default=0, verbose_name='security risk score')),
                ('admin_override', models.BooleanField(default=False, verbose_name='admin override used')),
                ('override_reason', models.TextField(blank=True, verbose_name='override reason')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, verbose_name='user agent')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
            ],
            options={
                'verbose_name': 'Enrollment Attempt Log',
                'verbose_name_plural': 'Enrollment Attempt Logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.RenameIndex(
            model_name='securityauditlog',
            new_name='courses_sec_timesta_5723c9_idx',
            old_name='courses_sec_timesta_8b5c8a_idx',
        ),
        migrations.RenameIndex(
            model_name='securityauditlog',
            new_name='courses_sec_ip_addr_d0671e_idx',
            old_name='courses_sec_ip_addr_9f4e2d_idx',
        ),
        migrations.RenameIndex(
            model_name='securityauditlog',
            new_name='courses_sec_user_id_1e7eca_idx',
            old_name='courses_sec_user_id_7a3b1c_idx',
        ),
        migrations.RenameIndex(
            model_name='securityauditlog',
            new_name='courses_sec_risk_sc_0ad88f_idx',
            old_name='courses_sec_risk_sc_2e9f4a_idx',
        ),
        migrations.AddField(
            model_name='enrollmentattemptlog',
            name='course',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollment_attempts', to='courses.course'),
        ),
        migrations.AddField(
            model_name='enrollmentattemptlog',
            name='student',
            field=models.ForeignKey(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='enrollment_attempts', to=settings.AUTH_USER_MODEL),
        ),
    ]
