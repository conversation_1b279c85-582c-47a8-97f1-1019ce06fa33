# Generated migration for comprehensive enrollment attempt tracking
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0007_merge_20250714_1217'),
    ]

    operations = [
        # Add comprehensive attempt tracking
        migrations.CreateModel(
            name='EnrollmentAttemptLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollment_attempts', to='accounts.user')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollment_attempts', to='courses.course')),
                ('attempt_type', models.CharField(choices=[
                    ('enrollment', 'Enrollment'),
                    ('drop', 'Drop'),
                    ('withdrawal', 'Withdrawal'),
                    ('completion', 'Completion'),
                    ('failure', 'Failure')
                ], max_length=20, verbose_name='attempt type')),
                ('attempt_number', models.PositiveIntegerField(verbose_name='attempt number')),
                ('enrollment_status', models.CharField(max_length=20, verbose_name='enrollment status')),
                ('risk_score', models.PositiveIntegerField(default=0, verbose_name='security risk score')),
                ('admin_override', models.BooleanField(default=False, verbose_name='admin override used')),
                ('override_reason', models.TextField(blank=True, verbose_name='override reason')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, verbose_name='user agent')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
            ],
            options={
                'verbose_name': 'Enrollment Attempt Log',
                'verbose_name_plural': 'Enrollment Attempt Logs',
                'ordering': ['-created_at'],
            },
        ),
        
        # Add security fields to existing Enrollment model
        migrations.AddField(
            model_name='enrollment',
            name='security_validated',
            field=models.BooleanField(default=False, verbose_name='security validated'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='validation_timestamp',
            field=models.DateTimeField(blank=True, null=True, verbose_name='validation timestamp'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='risk_score',
            field=models.PositiveIntegerField(default=0, verbose_name='security risk score'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='total_course_attempts',
            field=models.PositiveIntegerField(default=1, verbose_name='total attempts for this course'),
        ),
        
        # Add indexes for performance
        migrations.RunSQL(
            """
            CREATE INDEX idx_enrollment_attempt_tracking 
            ON courses_enrollmentattemptlog(student_id, course_id, attempt_type, created_at DESC);
            """,
            reverse_sql="DROP INDEX idx_enrollment_attempt_tracking;"
        ),
        
        migrations.RunSQL(
            """
            CREATE INDEX idx_enrollment_security_lookup 
            ON courses_enrollment(student_id, course_id, security_validated, risk_score);
            """,
            reverse_sql="DROP INDEX idx_enrollment_security_lookup;"
        ),
        
        # Add constraint to prevent excessive attempts
        migrations.RunSQL(
            """
            CREATE OR REPLACE FUNCTION check_enrollment_attempt_limit()
            RETURNS TRIGGER AS $$
            DECLARE
                attempt_count INTEGER;
            BEGIN
                -- Count total attempts for this student-course combination
                SELECT COUNT(*) INTO attempt_count
                FROM courses_enrollmentattemptlog
                WHERE student_id = NEW.student_id 
                AND course_id = NEW.course_id
                AND attempt_type IN ('enrollment', 'drop', 'withdrawal');
                
                -- Enforce maximum attempt limit (configurable)
                IF attempt_count >= 5 THEN
                    RAISE EXCEPTION 'Maximum enrollment attempts exceeded for this course';
                END IF;
                
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """,
            reverse_sql="DROP FUNCTION IF EXISTS check_enrollment_attempt_limit();"
        ),
        
        # Only create trigger for PostgreSQL (skip for SQLite in development)
        migrations.RunSQL(
            """
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'enrollment_attempt_limit_trigger') THEN
                    DROP TRIGGER enrollment_attempt_limit_trigger ON courses_enrollmentattemptlog;
                END IF;
                
                CREATE TRIGGER enrollment_attempt_limit_trigger
                    BEFORE INSERT ON courses_enrollmentattemptlog
                    FOR EACH ROW
                    EXECUTE FUNCTION check_enrollment_attempt_limit();
            EXCEPTION
                WHEN others THEN
                    -- Skip trigger creation for non-PostgreSQL databases
                    NULL;
            END $$;
            """,
            reverse_sql="DROP TRIGGER IF EXISTS enrollment_attempt_limit_trigger ON courses_enrollmentattemptlog;"
        ),
    ]
