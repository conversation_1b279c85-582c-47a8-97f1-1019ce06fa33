# Generated by Django 4.2.7 on 2025-07-14 09:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0003_enrollment_approved_by_enrollment_dropped_at_and_more'),
    ]

    operations = [
        # Enterprise Database Indexes for Optimal Performance
        # Based on 30+ years of database optimization experience
        
        # 1. CRITICAL ENROLLMENT QUERY INDEXES
        # These indexes support the most frequent enrollment system queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_student_active ON courses_enrollment(student_id, is_active) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_student_active;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_course_status_active ON courses_enrollment(course_id, status, is_active) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_course_status_active;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_student_course_attempts ON courses_enrollment(student_id, course_id, attempt_number DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_student_course_attempts;"
        ),
        
        # 2. RETAKE SYSTEM OPTIMIZATION INDEXES
        # Critical for our enhanced retake functionality
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_retake_tracking ON courses_enrollment(student_id, course_id, is_retake, status) WHERE status IN ('failed', 'dropped');",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_retake_tracking;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_failed_attempts ON courses_enrollment(student_id, course_id, status) WHERE status = 'failed';",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_failed_attempts;"
        ),
        
        # 3. COURSE LISTING & SEARCH OPTIMIZATION
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_course_published_active ON courses_course(is_published, is_active, semester, year) WHERE is_published = true AND is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_course_published_active;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_course_department_level ON courses_course(department_id, level, semester, year);",
            reverse_sql="DROP INDEX IF EXISTS idx_course_department_level;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_course_instructor_semester ON courses_course(instructor_id, semester, year);",
            reverse_sql="DROP INDEX IF EXISTS idx_course_instructor_semester;"
        ),
        
        # 4. WAITLIST OPTIMIZATION INDEXES
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_waitlist_course_position ON courses_waitlist(course_id, position, status) WHERE status = 'active';",
            reverse_sql="DROP INDEX IF EXISTS idx_waitlist_course_position;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_waitlist_student_active ON courses_waitlist(student_id, status) WHERE status = 'active';",
            reverse_sql="DROP INDEX IF EXISTS idx_waitlist_student_active;"
        ),
        
        # 5. TEMPORAL QUERY OPTIMIZATION
        # For enrollment history and analytics
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_temporal ON courses_enrollment(enrolled_at DESC, updated_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_temporal;"
        ),
        
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_completion_tracking ON courses_enrollment(completed_at, status) WHERE completed_at IS NOT NULL;",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_completion_tracking;"
        ),
        
        # 6. ANALYTICS & REPORTING INDEXES
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_analytics ON courses_enrollment(course_id, status, final_grade, letter_grade) WHERE status IN ('completed', 'failed');",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_analytics;"
        ),
        
        # 7. PREREQUISITE CHECKING OPTIMIZATION
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_enrollment_prerequisites ON courses_enrollment(student_id, status, is_active) WHERE status = 'completed' AND is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_enrollment_prerequisites;"
        ),
        
        # 8. FULL-TEXT SEARCH INDEXES (SQLite FTS)
        # For course search functionality
        migrations.RunSQL(
            """
            CREATE VIRTUAL TABLE IF NOT EXISTS courses_course_fts USING fts5(
                title, title_ar, code, description, description_ar,
                content='courses_course',
                content_rowid='id'
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS courses_course_fts;"
        ),
        
        # Populate FTS table
        migrations.RunSQL(
            """
            INSERT INTO courses_course_fts(rowid, title, title_ar, code, description, description_ar)
            SELECT id, title, title_ar, code, description, description_ar FROM courses_course;
            """,
            reverse_sql=""
        ),
        
        # FTS triggers for automatic updates
        migrations.RunSQL(
            """
            CREATE TRIGGER IF NOT EXISTS courses_course_fts_insert AFTER INSERT ON courses_course BEGIN
                INSERT INTO courses_course_fts(rowid, title, title_ar, code, description, description_ar)
                VALUES (new.id, new.title, new.title_ar, new.code, new.description, new.description_ar);
            END;
            """,
            reverse_sql="DROP TRIGGER IF EXISTS courses_course_fts_insert;"
        ),
        
        migrations.RunSQL(
            """
            CREATE TRIGGER IF NOT EXISTS courses_course_fts_delete AFTER DELETE ON courses_course BEGIN
                INSERT INTO courses_course_fts(courses_course_fts, rowid, title, title_ar, code, description, description_ar)
                VALUES('delete', old.id, old.title, old.title_ar, old.code, old.description, old.description_ar);
            END;
            """,
            reverse_sql="DROP TRIGGER IF EXISTS courses_course_fts_delete;"
        ),
        
        migrations.RunSQL(
            """
            CREATE TRIGGER IF NOT EXISTS courses_course_fts_update AFTER UPDATE ON courses_course BEGIN
                INSERT INTO courses_course_fts(courses_course_fts, rowid, title, title_ar, code, description, description_ar)
                VALUES('delete', old.id, old.title, old.title_ar, old.code, old.description, old.description_ar);
                INSERT INTO courses_course_fts(rowid, title, title_ar, code, description, description_ar)
                VALUES (new.id, new.title, new.title_ar, new.code, new.description, new.description_ar);
            END;
            """,
            reverse_sql="DROP TRIGGER IF EXISTS courses_course_fts_update;"
        ),
        
        # 9. PERFORMANCE STATISTICS COLLECTION
        # Enable SQLite query planner statistics
        migrations.RunSQL(
            "PRAGMA optimize;",
            reverse_sql=""
        ),
    ]
