# Generated migration for database-level security constraints
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0007_enrollment_attempt_tracking'),
    ]

    operations = [
        # Add check constraint: Cannot re-enroll in completed courses
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollment 
            ADD CONSTRAINT check_no_reenroll_completed 
            CHECK (
                NOT EXISTS (
                    SELECT 1 FROM courses_enrollment e2 
                    WHERE e2.student_id = courses_enrollment.student_id 
                    AND e2.course_id = courses_enrollment.course_id 
                    AND e2.status = 'completed' 
                    AND e2.id != courses_enrollment.id
                )
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollment DROP CONSTRAINT IF EXISTS check_no_reenroll_completed;"
        ),
        
        # Add check constraint: Maximum 3 failed attempts per course
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollment 
            ADD CONSTRAINT check_max_failed_attempts 
            CHECK (
                (
                    SELECT COUNT(*) FROM courses_enrollment e2 
                    WHERE e2.student_id = courses_enrollment.student_id 
                    AND e2.course_id = courses_enrollment.course_id 
                    AND e2.status = 'failed'
                ) <= 3
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollment DROP CONSTRAINT IF EXISTS check_max_failed_attempts;"
        ),
        
        # Add check constraint: Only one active enrollment per student per course
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollment 
            ADD CONSTRAINT check_single_active_enrollment 
            CHECK (
                NOT (
                    is_active = true AND EXISTS (
                        SELECT 1 FROM courses_enrollment e2 
                        WHERE e2.student_id = courses_enrollment.student_id 
                        AND e2.course_id = courses_enrollment.course_id 
                        AND e2.is_active = true 
                        AND e2.id != courses_enrollment.id
                    )
                )
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollment DROP CONSTRAINT IF EXISTS check_single_active_enrollment;"
        ),
        
        # Add check constraint: Valid enrollment status transitions
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollment 
            ADD CONSTRAINT check_valid_status_transitions 
            CHECK (
                status IN ('enrolled', 'waitlisted', 'dropped', 'completed', 'failed')
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollment DROP CONSTRAINT IF EXISTS check_valid_status_transitions;"
        ),
        
        # Add check constraint: Reasonable attempt numbers
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollment 
            ADD CONSTRAINT check_reasonable_attempt_number 
            CHECK (
                attempt_number > 0 AND attempt_number <= 5
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollment DROP CONSTRAINT IF EXISTS check_reasonable_attempt_number;"
        ),
        
        # Add check constraint: Risk score bounds
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollment 
            ADD CONSTRAINT check_risk_score_bounds 
            CHECK (
                risk_score >= 0 AND risk_score <= 100
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollment DROP CONSTRAINT IF EXISTS check_risk_score_bounds;"
        ),
        
        # Add check constraint for EnrollmentAttemptLog: Risk score bounds
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollmentattemptlog 
            ADD CONSTRAINT check_attempt_risk_score_bounds 
            CHECK (
                risk_score >= 0 AND risk_score <= 100
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollmentattemptlog DROP CONSTRAINT IF EXISTS check_attempt_risk_score_bounds;"
        ),
        
        # Add check constraint: Valid attempt types
        migrations.RunSQL(
            """
            ALTER TABLE courses_enrollmentattemptlog 
            ADD CONSTRAINT check_valid_attempt_types 
            CHECK (
                attempt_type IN ('enrollment', 'drop', 'withdrawal', 'completion', 'failure')
            );
            """,
            reverse_sql="ALTER TABLE courses_enrollmentattemptlog DROP CONSTRAINT IF EXISTS check_valid_attempt_types;"
        ),
        
        # Add function to validate enrollment business rules
        migrations.RunSQL(
            """
            CREATE OR REPLACE FUNCTION validate_enrollment_business_rules()
            RETURNS TRIGGER AS $$
            DECLARE
                completed_count INTEGER;
                failed_count INTEGER;
                active_count INTEGER;
                total_attempts INTEGER;
            BEGIN
                -- Check for completed course re-enrollment
                SELECT COUNT(*) INTO completed_count
                FROM courses_enrollment
                WHERE student_id = NEW.student_id 
                AND course_id = NEW.course_id 
                AND status = 'completed'
                AND id != COALESCE(NEW.id, 0);
                
                IF completed_count > 0 THEN
                    RAISE EXCEPTION 'SECURITY VIOLATION: Cannot re-enroll in completed course (Student: %, Course: %)', 
                        NEW.student_id, NEW.course_id;
                END IF;
                
                -- Check failed attempt limits
                SELECT COUNT(*) INTO failed_count
                FROM courses_enrollment
                WHERE student_id = NEW.student_id 
                AND course_id = NEW.course_id 
                AND status = 'failed';
                
                IF NEW.status = 'failed' AND failed_count >= 3 THEN
                    RAISE EXCEPTION 'SECURITY VIOLATION: Maximum failed attempts exceeded (Student: %, Course: %)', 
                        NEW.student_id, NEW.course_id;
                END IF;
                
                -- Check single active enrollment
                IF NEW.is_active = true THEN
                    SELECT COUNT(*) INTO active_count
                    FROM courses_enrollment
                    WHERE student_id = NEW.student_id 
                    AND course_id = NEW.course_id 
                    AND is_active = true
                    AND id != COALESCE(NEW.id, 0);
                    
                    IF active_count > 0 THEN
                        RAISE EXCEPTION 'SECURITY VIOLATION: Multiple active enrollments not allowed (Student: %, Course: %)', 
                            NEW.student_id, NEW.course_id;
                    END IF;
                END IF;
                
                -- Check total attempt limits
                SELECT COUNT(*) INTO total_attempts
                FROM courses_enrollmentattemptlog
                WHERE student_id = NEW.student_id 
                AND course_id = NEW.course_id
                AND attempt_type IN ('enrollment', 'drop', 'withdrawal');
                
                IF total_attempts >= 5 THEN
                    RAISE EXCEPTION 'SECURITY VIOLATION: Maximum total attempts exceeded (Student: %, Course: %)', 
                        NEW.student_id, NEW.course_id;
                END IF;
                
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """,
            reverse_sql="DROP FUNCTION IF EXISTS validate_enrollment_business_rules();"
        ),
        
        # Add trigger for enrollment validation (PostgreSQL only)
        migrations.RunSQL(
            """
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'enrollment_business_rules_trigger') THEN
                    DROP TRIGGER enrollment_business_rules_trigger ON courses_enrollment;
                END IF;
                
                CREATE TRIGGER enrollment_business_rules_trigger
                    BEFORE INSERT OR UPDATE ON courses_enrollment
                    FOR EACH ROW
                    EXECUTE FUNCTION validate_enrollment_business_rules();
            EXCEPTION
                WHEN others THEN
                    -- Skip trigger creation for non-PostgreSQL databases
                    NULL;
            END $$;
            """,
            reverse_sql="DROP TRIGGER IF EXISTS enrollment_business_rules_trigger ON courses_enrollment;"
        ),
    ]
