"""
Course Enrollment Cache Invalidation Signals
Ensures real-time data consistency by invalidating cache when enrollment data changes
"""

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.core.cache import cache
from backend.cache_config import cache_manager, invalidate_enrollment_cache
import logging

from .models import Enrollment, Course

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Enrollment)
def invalidate_enrollment_cache_on_save(sender, instance, created, **kwargs):
    """
    Invalidate enrollment-related cache when enrollment is created or updated
    """
    try:
        # Invalidate specific enrollment cache
        invalidate_enrollment_cache(
            student_id=instance.student_id,
            course_id=instance.course_id
        )
        
        # Invalidate course-specific cache patterns
        course_cache_patterns = [
            f"umls:enrollment_status:*course_id*{instance.course_id}*",
            f"umls:course_detail:*{instance.course_id}*",
            f"umls:course_enrollment_stats:*{instance.course_id}*",
        ]
        
        # Invalidate student-specific cache patterns
        student_cache_patterns = [
            f"umls:enrollment_status:*student_id*{instance.student_id}*",
            f"umls:course_list:*student_id*{instance.student_id}*",
            f"umls:student_dashboard:*{instance.student_id}*",
        ]
        
        total_invalidated = 0
        for pattern in course_cache_patterns + student_cache_patterns:
            total_invalidated += cache_manager.invalidate_pattern(pattern)
        
        action = "created" if created else "updated"
        logger.info(
            f"Enrollment {action} - invalidated {total_invalidated} cache entries "
            f"for student {instance.student_id} and course {instance.course_id}"
        )
        
    except Exception as e:
        logger.error(f"Error invalidating enrollment cache: {e}")


@receiver(post_delete, sender=Enrollment)
def invalidate_enrollment_cache_on_delete(sender, instance, **kwargs):
    """
    Invalidate enrollment-related cache when enrollment is deleted
    """
    try:
        # Invalidate specific enrollment cache
        invalidate_enrollment_cache(
            student_id=instance.student_id,
            course_id=instance.course_id
        )
        
        # Invalidate broader cache patterns
        patterns = [
            f"umls:enrollment_status:*",
            f"umls:course_detail:*{instance.course_id}*",
            f"umls:course_list:*student_id*{instance.student_id}*",
        ]
        
        total_invalidated = 0
        for pattern in patterns:
            total_invalidated += cache_manager.invalidate_pattern(pattern)
        
        logger.info(
            f"Enrollment deleted - invalidated {total_invalidated} cache entries "
            f"for student {instance.student_id} and course {instance.course_id}"
        )
        
    except Exception as e:
        logger.error(f"Error invalidating enrollment cache on delete: {e}")


@receiver(pre_save, sender=Enrollment)
def track_enrollment_status_changes(sender, instance, **kwargs):
    """
    Track enrollment status changes to invalidate specific cache entries
    """
    if instance.pk:  # Only for updates, not new records
        try:
            old_instance = Enrollment.objects.get(pk=instance.pk)
            
            # If status changed, invalidate more aggressively
            if old_instance.status != instance.status:
                logger.info(
                    f"Enrollment status changing from {old_instance.status} to {instance.status} "
                    f"for student {instance.student_id} and course {instance.course_id}"
                )
                
                # Store the change for post_save signal
                instance._status_changed = True
                instance._old_status = old_instance.status
                
        except Enrollment.DoesNotExist:
            # This shouldn't happen, but handle gracefully
            pass
        except Exception as e:
            logger.error(f"Error tracking enrollment status changes: {e}")


@receiver(post_save, sender=Course)
def invalidate_course_cache_on_save(sender, instance, created, **kwargs):
    """
    Invalidate course-related cache when course is updated
    """
    try:
        # Invalidate course-specific cache
        patterns = [
            f"umls:course_detail:*{instance.id}*",
            f"umls:course_enrollment_stats:*{instance.id}*",
            f"umls:enrollment_status:*course_id*{instance.id}*",
        ]
        
        # If course capacity or status changed, invalidate enrollment-related cache
        if not created:
            patterns.extend([
                f"umls:course_list:*",  # Broad invalidation for course list views
                f"umls:enrollment:*course_id*{instance.id}*",
            ])
        
        total_invalidated = 0
        for pattern in patterns:
            total_invalidated += cache_manager.invalidate_pattern(pattern)
        
        action = "created" if created else "updated"
        logger.info(
            f"Course {action} - invalidated {total_invalidated} cache entries "
            f"for course {instance.id}"
        )
        
    except Exception as e:
        logger.error(f"Error invalidating course cache: {e}")


def invalidate_all_enrollment_cache():
    """
    Nuclear option: invalidate all enrollment-related cache
    Use sparingly, only for major system changes
    """
    try:
        patterns = [
            "umls:enrollment*",
            "umls:course_list*",
            "umls:course_detail*",
            "umls:student_dashboard*",
        ]
        
        total_invalidated = 0
        for pattern in patterns:
            total_invalidated += cache_manager.invalidate_pattern(pattern)
        
        logger.warning(f"Nuclear cache invalidation - cleared {total_invalidated} entries")
        return total_invalidated
        
    except Exception as e:
        logger.error(f"Error in nuclear cache invalidation: {e}")
        return 0


def warm_enrollment_cache_for_student(student_id):
    """
    Pre-warm cache for a specific student's enrollment data
    """
    try:
        from .serializers import CourseSerializer
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        student = User.objects.get(id=student_id, role='student')
        
        # Get student's courses
        enrolled_courses = Course.objects.filter(
            enrollments__student=student,
            enrollments__is_active=True
        ).distinct()
        
        warmed_count = 0
        for course in enrolled_courses:
            try:
                # Create request context for serializer
                class MockRequest:
                    def __init__(self, user):
                        self.user = user
                
                mock_request = MockRequest(student)
                serializer = CourseSerializer(course, context={'request': mock_request})
                
                # Cache the serialized data
                cache_key = cache_manager.generate_cache_key(
                    "course_detail_with_enrollment",
                    course_id=course.id,
                    student_id=student_id
                )
                
                cache_manager.set(cache_key, serializer.data, timeout=1800)  # 30 minutes
                warmed_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to warm cache for course {course.id}: {e}")
        
        logger.info(f"Warmed enrollment cache for {warmed_count} courses for student {student_id}")
        return warmed_count
        
    except Exception as e:
        logger.error(f"Error warming enrollment cache for student {student_id}: {e}")
        return 0
