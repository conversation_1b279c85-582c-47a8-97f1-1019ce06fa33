from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class Department(models.Model):
    """
    Academic departments in the university
    """
    name = models.Char<PERSON><PERSON>(_('name'), max_length=100)
    name_ar = models.CharField(_('name (Arabic)'), max_length=100, blank=True)
    code = models.CharField(_('department code'), max_length=10, unique=True)
    description = models.TextField(_('description'), blank=True)
    description_ar = models.TextField(_('description (Arabic)'), blank=True)
    head = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        limit_choices_to={'role': 'teacher'}
    )
    is_active = models.BooleanField(_('active'), default=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Course(models.Model):
    """
    University courses
    """
    class CourseLevel(models.TextChoices):
        UNDERGRADUATE = 'undergraduate', _('Undergraduate')
        GRADUATE = 'graduate', _('Graduate')
        POSTGRADUATE = 'postgraduate', _('Postgraduate')

    class Semester(models.TextChoices):
        FALL = 'fall', _('Fall')
        SPRING = 'spring', _('Spring')
        SUMMER = 'summer', _('Summer')

    # Basic Information
    title = models.CharField(_('title'), max_length=200)
    title_ar = models.CharField(_('title (Arabic)'), max_length=200, blank=True)
    code = models.CharField(_('course code'), max_length=20, unique=True)
    description = models.TextField(_('description'))
    description_ar = models.TextField(_('description (Arabic)'), blank=True)

    # Academic Details
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='courses')
    level = models.CharField(_('level'), max_length=20, choices=CourseLevel.choices)
    credit_hours = models.PositiveIntegerField(_('credit hours'), default=3)
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='prerequisite_for')

    # Scheduling
    semester = models.CharField(_('semester'), max_length=10, choices=Semester.choices)
    year = models.PositiveIntegerField(_('academic year'))
    max_students = models.PositiveIntegerField(_('maximum students'), default=30)

    # Instructors
    instructor = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='taught_courses',
        limit_choices_to={'role': 'teacher'}
    )
    assistant_instructors = models.ManyToManyField(
        User,
        blank=True,
        related_name='assisted_courses',
        limit_choices_to={'role': 'teacher'}
    )

    # Status
    is_active = models.BooleanField(_('active'), default=True)
    is_published = models.BooleanField(_('published'), default=False)

    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    start_date = models.DateField(_('start date'))
    end_date = models.DateField(_('end date'))

    class Meta:
        verbose_name = _('Course')
        verbose_name_plural = _('Courses')
        ordering = ['-year', 'semester', 'code']
        unique_together = ['code', 'semester', 'year']

    def __str__(self):
        return f"{self.code} - {self.title} ({self.semester} {self.year})"

    @property
    def enrolled_students_count(self):
        return self.enrollments.filter(
            status=Enrollment.EnrollmentStatus.ENROLLED,
            is_active=True
        ).count()

    @property
    def waitlisted_students_count(self):
        return self.waitlists.filter(status='active').count()

    @property
    def is_full(self):
        return self.enrolled_students_count >= self.max_students

    @property
    def available_spots(self):
        return max(0, self.max_students - self.enrolled_students_count)

    @property
    def enrollment_status(self):
        """Get current enrollment status for the course"""
        if not self.is_published:
            return 'closed'
        elif self.is_full:
            return 'waitlist' if self.waitlisted_students_count < 20 else 'closed'  # Max 20 on waitlist
        else:
            return 'open'

    def can_enroll(self, student):
        """Enhanced can_enroll method with proper retake logic"""
        # Check if course is published and active
        if not self.is_published or not self.is_active:
            return False, "Course is not available for enrollment"

        # CRITICAL FIX: Check for active enrollment only (not all enrollments)
        active_enrollment = self.enrollments.filter(
            student=student,
            is_active=True
        ).first()

        if active_enrollment:
            if active_enrollment.status in ['enrolled', 'waitlisted']:
                return False, f"Student is already {active_enrollment.status} in this course"

        # Check enrollment history for retake scenarios
        enrollment_history = self.enrollments.filter(student=student).order_by('-attempt_number')

        if enrollment_history.exists():
            latest_enrollment = enrollment_history.first()

            # Cannot retake completed courses
            if latest_enrollment.status == 'completed':
                return False, "Course already completed successfully"

            # Check retake limits for failed courses
            if latest_enrollment.status == 'failed':
                failed_count = enrollment_history.filter(status='failed').count()
                if failed_count >= 3:
                    return False, "Maximum retake attempts exceeded (3 attempts)"

        # Check prerequisites (for both new enrollments and retakes)
        if self.prerequisites.exists():
            # Get completed courses from student's enrollment history
            completed_courses = student.enrollments.filter(
                status=Enrollment.EnrollmentStatus.COMPLETED
            ).values_list('course_id', flat=True)

            missing_prerequisites = self.prerequisites.exclude(id__in=completed_courses)
            if missing_prerequisites.exists():
                prereq_names = ', '.join([p.code for p in missing_prerequisites])
                return False, f"Missing prerequisites: {prereq_names}"

        # Check if spots available
        if self.is_full:
            if self.waitlisted_students_count < 20:  # Max waitlist size
                return True, "Can join waitlist"
            else:
                return False, "Course is full and waitlist is full"

        return True, "Can enroll"

    def enroll_student(self, student, enrollment_type='regular'):
        """Enroll a student in the course"""
        from django.utils import timezone

        can_enroll, message = self.can_enroll(student)

        if not can_enroll and "waitlist" not in message:
            raise ValueError(message)

        if self.is_full:
            # Add to waitlist
            from .models import Waitlist  # Avoid circular import
            waitlist_entry = Waitlist.add_to_waitlist(student, self)
            return None, waitlist_entry
        else:
            # CRITICAL FIX: Deactivate any previous enrollments before creating new one
            # This prevents multiple active enrollments for the same student-course combination
            previous_enrollments = Enrollment.objects.filter(
                student=student,
                course=self,
                is_active=True
            )

            if previous_enrollments.exists():
                previous_enrollments.update(
                    is_active=False,
                    updated_at=timezone.now()
                )

            # Direct enrollment - create new active enrollment
            enrollment = Enrollment.objects.create(
                student=student,
                course=self,
                enrollment_type=enrollment_type,
                status=Enrollment.EnrollmentStatus.ENROLLED
            )
            return enrollment, None


class Enrollment(models.Model):
    """
    Student course enrollments
    """
    class EnrollmentStatus(models.TextChoices):
        ENROLLED = 'enrolled', _('Enrolled')
        WAITLISTED = 'waitlisted', _('Waitlisted')
        DROPPED = 'dropped', _('Dropped')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='enrollments',
        limit_choices_to={'role': 'student'}
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='enrollments')
    status = models.CharField(_('status'), max_length=20, choices=EnrollmentStatus.choices, default=EnrollmentStatus.ENROLLED)

    # Enrollment Details
    enrollment_type = models.CharField(
        _('enrollment type'),
        max_length=20,
        choices=[
            ('regular', _('Regular')),
            ('audit', _('Audit')),
            ('credit_no_credit', _('Credit/No Credit'))
        ],
        default='regular'
    )
    waitlist_position = models.PositiveIntegerField(_('waitlist position'), null=True, blank=True)

    # Prerequisites tracking
    prerequisites_met = models.BooleanField(_('prerequisites met'), default=True)
    prerequisite_override = models.BooleanField(_('prerequisite override'), default=False)
    override_reason = models.TextField(_('override reason'), blank=True)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_enrollments',
        limit_choices_to={'role__in': ['admin', 'super_admin']}
    )

    # Grades
    midterm_grade = models.DecimalField(_('midterm grade'), max_digits=5, decimal_places=2, null=True, blank=True)
    final_grade = models.DecimalField(_('final grade'), max_digits=5, decimal_places=2, null=True, blank=True)
    total_grade = models.DecimalField(_('total grade'), max_digits=5, decimal_places=2, null=True, blank=True)
    letter_grade = models.CharField(_('letter grade'), max_length=2, blank=True)
    gpa_points = models.DecimalField(_('GPA points'), max_digits=3, decimal_places=2, null=True, blank=True)

    # Timestamps
    enrolled_at = models.DateTimeField(_('enrolled at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    completed_at = models.DateTimeField(_('completed at'), null=True, blank=True)
    dropped_at = models.DateTimeField(_('dropped at'), null=True, blank=True)

    # Flags
    is_active = models.BooleanField(_('active'), default=True)
    is_audit = models.BooleanField(_('audit enrollment'), default=False)

    # Retake tracking
    attempt_number = models.PositiveIntegerField(_('attempt number'), default=1)
    is_retake = models.BooleanField(_('is retake'), default=False)

    class Meta:
        verbose_name = _('Enrollment')
        verbose_name_plural = _('Enrollments')
        ordering = ['-enrolled_at', '-attempt_number']

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.course.code}"

    def check_prerequisites(self):
        """Check if student has completed all prerequisites for this course"""
        if not self.course.prerequisites.exists():
            return True

        completed_courses = Enrollment.objects.filter(
            student=self.student,
            status='completed',
            is_active=True
        ).values_list('course_id', flat=True)

        required_prerequisites = self.course.prerequisites.values_list('id', flat=True)
        return all(prereq in completed_courses for prereq in required_prerequisites)

    def calculate_gpa_points(self):
        """Calculate GPA points based on letter grade and credit hours"""
        if not self.letter_grade:
            return None

        grade_points = {
            'A+': 4.0, 'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'F': 0.0
        }

        points = grade_points.get(self.letter_grade, 0.0)
        return points * self.course.credit_hours

    def get_attempt_display(self):
        """Get display text for attempt number (e.g., 'R1', 'R2')"""
        if self.attempt_number == 1:
            return ""
        return f"R{self.attempt_number - 1}"

    def get_previous_attempts(self):
        """Get all previous attempts for this student-course combination"""
        return Enrollment.objects.filter(
            student=self.student,
            course=self.course,
            attempt_number__lt=self.attempt_number
        ).order_by('attempt_number')

    def get_latest_attempt(self):
        """Get the latest attempt for this student-course combination"""
        return Enrollment.objects.filter(
            student=self.student,
            course=self.course
        ).order_by('-attempt_number').first()

    @classmethod
    def get_student_course_history(cls, student, course):
        """Get complete enrollment history for a student-course combination"""
        return cls.objects.filter(
            student=student,
            course=course
        ).order_by('attempt_number')

    def save(self, *args, **kwargs):
        # Auto-calculate attempt number for retakes
        if not self.pk:  # New enrollment
            previous_attempts = Enrollment.objects.filter(
                student=self.student,
                course=self.course
            ).count()

            if previous_attempts > 0:
                self.attempt_number = previous_attempts + 1
                self.is_retake = True
            else:
                self.attempt_number = 1
                self.is_retake = False

        # Check prerequisites if not overridden
        if not self.prerequisite_override:
            self.prerequisites_met = self.check_prerequisites()

        # Calculate GPA points
        if self.letter_grade:
            self.gpa_points = self.calculate_gpa_points()

        super().save(*args, **kwargs)


class Waitlist(models.Model):
    """
    Course waitlist management
    """
    class WaitlistStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        ENROLLED = 'enrolled', _('Enrolled')
        DROPPED = 'dropped', _('Dropped')
        EXPIRED = 'expired', _('Expired')

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='waitlists',
        limit_choices_to={'role': 'student'}
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='waitlists')
    position = models.PositiveIntegerField(_('position'))
    status = models.CharField(_('status'), max_length=20, choices=WaitlistStatus.choices, default=WaitlistStatus.ACTIVE)

    # Timestamps
    added_at = models.DateTimeField(_('added at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    enrolled_at = models.DateTimeField(_('enrolled at'), null=True, blank=True)

    # Notification settings
    notify_when_available = models.BooleanField(_('notify when available'), default=True)
    notification_sent = models.BooleanField(_('notification sent'), default=False)

    class Meta:
        verbose_name = _('Waitlist Entry')
        verbose_name_plural = _('Waitlist Entries')
        unique_together = ['student', 'course']
        ordering = ['course', 'position']

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.course.code} (Position: {self.position})"

    def move_up(self):
        """Move student up one position in waitlist"""
        if self.position > 1:
            # Find the student above and swap positions
            above_student = Waitlist.objects.filter(
                course=self.course,
                position=self.position - 1,
                status='active'
            ).first()

            if above_student:
                above_student.position = self.position
                self.position -= 1
                above_student.save()
                self.save()

    def move_down(self):
        """Move student down one position in waitlist"""
        max_position = Waitlist.objects.filter(
            course=self.course,
            status='active'
        ).count()

        if self.position < max_position:
            # Find the student below and swap positions
            below_student = Waitlist.objects.filter(
                course=self.course,
                position=self.position + 1,
                status='active'
            ).first()

            if below_student:
                below_student.position = self.position
                self.position += 1
                below_student.save()
                self.save()

    @classmethod
    def add_to_waitlist(cls, student, course):
        """Add a student to the course waitlist"""
        # Get next position
        max_position = cls.objects.filter(
            course=course,
            status='active'
        ).aggregate(max_pos=models.Max('position'))['max_pos'] or 0

        waitlist_entry, created = cls.objects.get_or_create(
            student=student,
            course=course,
            defaults={
                'position': max_position + 1,
                'status': cls.WaitlistStatus.ACTIVE
            }
        )

        return waitlist_entry

    @classmethod
    def promote_from_waitlist(cls, course, count=1):
        """Promote students from waitlist to enrolled when spots become available"""
        promoted_students = []

        for _ in range(count):
            # Get next student in line
            next_student = cls.objects.filter(
                course=course,
                status=cls.WaitlistStatus.ACTIVE
            ).order_by('position').first()

            if next_student and not course.is_full:
                # Create enrollment
                enrollment = Enrollment.objects.create(
                    student=next_student.student,
                    course=course,
                    status=Enrollment.EnrollmentStatus.ENROLLED
                )

                # Update waitlist status
                next_student.status = cls.WaitlistStatus.ENROLLED
                next_student.enrolled_at = timezone.now()
                next_student.save()

                promoted_students.append(next_student.student)

                # Update positions for remaining students
                cls.objects.filter(
                    course=course,
                    status=cls.WaitlistStatus.ACTIVE,
                    position__gt=next_student.position
                ).update(position=models.F('position') - 1)

        return promoted_students

    def calculate_letter_grade(self):
        """Calculate letter grade based on total grade"""
        if self.total_grade is None:
            return ''

        grade = float(self.total_grade)
        if grade >= 90:
            return 'A+'
        elif grade >= 85:
            return 'A'
        elif grade >= 80:
            return 'B+'
        elif grade >= 75:
            return 'B'
        elif grade >= 70:
            return 'C+'
        elif grade >= 65:
            return 'C'
        elif grade >= 60:
            return 'D'
        else:
            return 'F'

    def save(self, *args, **kwargs):
        # Auto-calculate letter grade
        if self.total_grade is not None:
            self.letter_grade = self.calculate_letter_grade()
        super().save(*args, **kwargs)


class SecurityAuditLog(models.Model):
    """
    Comprehensive security audit logging
    """
    class EventType(models.TextChoices):
        LOGIN_SUCCESS = 'login_success', _('Login Success')
        LOGIN_FAILURE = 'login_failure', _('Login Failure')
        LOGOUT = 'logout', _('Logout')
        PASSWORD_CHANGE = 'password_change', _('Password Change')
        PERMISSION_DENIED = 'permission_denied', _('Permission Denied')
        SUSPICIOUS_ACTIVITY = 'suspicious_activity', _('Suspicious Activity')
        DATA_ACCESS = 'data_access', _('Data Access')
        DATA_MODIFICATION = 'data_modification', _('Data Modification')
        RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded', _('Rate Limit Exceeded')
        SECURITY_VIOLATION = 'security_violation', _('Security Violation')

    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    event_type = models.CharField(max_length=50, choices=EventType.choices, db_index=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(db_index=True)
    user_agent = models.TextField()
    request_path = models.CharField(max_length=500)
    request_method = models.CharField(max_length=10)
    details = models.JSONField(default=dict)
    risk_score = models.IntegerField(default=0, db_index=True)  # 0-100 risk score

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp', 'event_type']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['user', 'event_type', 'timestamp']),
            models.Index(fields=['risk_score', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.event_type} - {self.user} - {self.timestamp}"
