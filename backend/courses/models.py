from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class Department(models.Model):
    """
    Academic departments in the university
    """
    name = models.Char<PERSON><PERSON>(_('name'), max_length=100)
    name_ar = models.CharField(_('name (Arabic)'), max_length=100, blank=True)
    code = models.CharField(_('department code'), max_length=10, unique=True)
    description = models.TextField(_('description'), blank=True)
    description_ar = models.TextField(_('description (Arabic)'), blank=True)
    head = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        limit_choices_to={'role': 'teacher'}
    )
    is_active = models.BooleanField(_('active'), default=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Course(models.Model):
    """
    University courses
    """
    class CourseLevel(models.TextChoices):
        UNDERGRADUATE = 'undergraduate', _('Undergraduate')
        GRADUATE = 'graduate', _('Graduate')
        POSTGRADUATE = 'postgraduate', _('Postgraduate')

    class Semester(models.TextChoices):
        FALL = 'fall', _('Fall')
        SPRING = 'spring', _('Spring')
        SUMMER = 'summer', _('Summer')

    # Basic Information
    title = models.CharField(_('title'), max_length=200)
    title_ar = models.CharField(_('title (Arabic)'), max_length=200, blank=True)
    code = models.CharField(_('course code'), max_length=20, unique=True)
    description = models.TextField(_('description'))
    description_ar = models.TextField(_('description (Arabic)'), blank=True)

    # Academic Details
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='courses')
    level = models.CharField(_('level'), max_length=20, choices=CourseLevel.choices)
    credit_hours = models.PositiveIntegerField(_('credit hours'), default=3)
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='prerequisite_for')

    # Scheduling
    semester = models.CharField(_('semester'), max_length=10, choices=Semester.choices)
    year = models.PositiveIntegerField(_('academic year'))
    max_students = models.PositiveIntegerField(_('maximum students'), default=30)

    # Instructors
    instructor = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='taught_courses',
        limit_choices_to={'role': 'teacher'}
    )
    assistant_instructors = models.ManyToManyField(
        User,
        blank=True,
        related_name='assisted_courses',
        limit_choices_to={'role': 'teacher'}
    )

    # Status
    is_active = models.BooleanField(_('active'), default=True)
    is_published = models.BooleanField(_('published'), default=False)

    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    start_date = models.DateField(_('start date'))
    end_date = models.DateField(_('end date'))

    class Meta:
        verbose_name = _('Course')
        verbose_name_plural = _('Courses')
        ordering = ['-year', 'semester', 'code']
        unique_together = ['code', 'semester', 'year']

    def __str__(self):
        return f"{self.code} - {self.title} ({self.semester} {self.year})"

    @property
    def enrolled_students_count(self):
        return self.enrollments.filter(
            status=Enrollment.EnrollmentStatus.ENROLLED,
            is_active=True
        ).count()

    @property
    def waitlisted_students_count(self):
        return self.waitlists.filter(status='active').count()

    @property
    def is_full(self):
        return self.enrolled_students_count >= self.max_students

    @property
    def available_spots(self):
        return max(0, self.max_students - self.enrolled_students_count)

    @property
    def enrollment_status(self):
        """Get current enrollment status for the course"""
        if not self.is_published:
            return 'closed'
        elif self.is_full:
            return 'waitlist' if self.waitlisted_students_count < 20 else 'closed'  # Max 20 on waitlist
        else:
            return 'open'

    def can_enroll(self, student, admin_override=False, override_reason=None):
        """
        SECURE ENROLLMENT VALIDATION with Defense-in-Depth Architecture
        Uses comprehensive security validation system
        """
        from .enrollment_security import EnrollmentSecurityManager

        # Use comprehensive security validation
        security_manager = EnrollmentSecurityManager(student, self)
        security_result = security_manager.validate_enrollment(
            admin_override=admin_override,
            override_reason=override_reason
        )

        return security_result.allowed, security_result.message

    def enroll_student(self, student, enrollment_type='regular', admin_override=False, override_reason=None):
        """
        SECURE ENROLLMENT METHOD with Comprehensive Validation
        All enrollments must pass through security validation
        """
        from django.utils import timezone
        from django.db import transaction
        from .enrollment_security import EnrollmentSecurityManager

        # CRITICAL: Use comprehensive security validation
        security_manager = EnrollmentSecurityManager(student, self)
        security_result = security_manager.validate_enrollment(
            enrollment_type=enrollment_type,
            admin_override=admin_override,
            override_reason=override_reason
        )

        if not security_result.allowed:
            raise ValueError(f"ENROLLMENT BLOCKED: {security_result.message}")

        # Use database transaction for atomicity
        with transaction.atomic():
            if self.is_full:
                # Add to waitlist
                from .models import Waitlist  # Avoid circular import
                waitlist_entry = Waitlist.add_to_waitlist(student, self)
                return None, waitlist_entry
            else:
                # CRITICAL: Deactivate any previous enrollments before creating new one
                previous_enrollments = Enrollment.objects.filter(
                    student=student,
                    course=self,
                    is_active=True
                )

                if previous_enrollments.exists():
                    previous_enrollments.update(
                        is_active=False,
                        updated_at=timezone.now()
                    )

                # Create new secure enrollment
                enrollment = Enrollment.objects.create(
                    student=student,
                    course=self,
                    enrollment_type=enrollment_type,
                    status=Enrollment.EnrollmentStatus.ENROLLED,
                    prerequisite_override=admin_override,
                    override_reason=override_reason if admin_override else None
                )

                # Log successful enrollment for audit and monitoring
                import logging
                from .monitoring import enrollment_monitor

                logger = logging.getLogger(__name__)
                logger.info(f"Secure enrollment created: Student {student.id} in Course {self.id}")

                # Monitor enrollment for security patterns
                enrollment_monitor.monitor_enrollment_attempt(
                    student=student,
                    course=self,
                    attempt_type='enrollment',
                    enrollment_status='enrolled',
                    risk_score=security_result.risk_score,
                    ip_address=None,  # Will be set by API view
                    user_agent=None   # Will be set by API view
                )

                return enrollment, None


class Enrollment(models.Model):
    """
    Student course enrollments
    """
    class EnrollmentStatus(models.TextChoices):
        ENROLLED = 'enrolled', _('Enrolled')
        WAITLISTED = 'waitlisted', _('Waitlisted')
        DROPPED = 'dropped', _('Dropped')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='enrollments',
        limit_choices_to={'role': 'student'}
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='enrollments')
    status = models.CharField(_('status'), max_length=20, choices=EnrollmentStatus.choices, default=EnrollmentStatus.ENROLLED)

    # Enrollment Details
    enrollment_type = models.CharField(
        _('enrollment type'),
        max_length=20,
        choices=[
            ('regular', _('Regular')),
            ('audit', _('Audit')),
            ('credit_no_credit', _('Credit/No Credit'))
        ],
        default='regular'
    )
    waitlist_position = models.PositiveIntegerField(_('waitlist position'), null=True, blank=True)

    # Prerequisites tracking
    prerequisites_met = models.BooleanField(_('prerequisites met'), default=True)
    prerequisite_override = models.BooleanField(_('prerequisite override'), default=False)
    override_reason = models.TextField(_('override reason'), blank=True)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_enrollments',
        limit_choices_to={'role__in': ['admin', 'super_admin']}
    )

    # Grades
    midterm_grade = models.DecimalField(_('midterm grade'), max_digits=5, decimal_places=2, null=True, blank=True)
    final_grade = models.DecimalField(_('final grade'), max_digits=5, decimal_places=2, null=True, blank=True)
    total_grade = models.DecimalField(_('total grade'), max_digits=5, decimal_places=2, null=True, blank=True)
    letter_grade = models.CharField(_('letter grade'), max_length=2, blank=True)
    gpa_points = models.DecimalField(_('GPA points'), max_digits=3, decimal_places=2, null=True, blank=True)

    # Timestamps
    enrolled_at = models.DateTimeField(_('enrolled at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    completed_at = models.DateTimeField(_('completed at'), null=True, blank=True)
    dropped_at = models.DateTimeField(_('dropped at'), null=True, blank=True)

    # Flags
    is_active = models.BooleanField(_('active'), default=True)
    is_audit = models.BooleanField(_('audit enrollment'), default=False)

    # Retake tracking
    attempt_number = models.PositiveIntegerField(_('attempt number'), default=1)
    is_retake = models.BooleanField(_('is retake'), default=False)

    class Meta:
        verbose_name = _('Enrollment')
        verbose_name_plural = _('Enrollments')
        ordering = ['-enrolled_at', '-attempt_number']

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.course.code}"

    def check_prerequisites(self):
        """Check if student has completed all prerequisites for this course"""
        if not self.course.prerequisites.exists():
            return True

        completed_courses = Enrollment.objects.filter(
            student=self.student,
            status='completed',
            is_active=True
        ).values_list('course_id', flat=True)

        required_prerequisites = self.course.prerequisites.values_list('id', flat=True)
        return all(prereq in completed_courses for prereq in required_prerequisites)

    def calculate_gpa_points(self):
        """Calculate GPA points based on letter grade and credit hours"""
        if not self.letter_grade:
            return None

        grade_points = {
            'A+': 4.0, 'A': 4.0, 'A-': 3.7,
            'B+': 3.3, 'B': 3.0, 'B-': 2.7,
            'C+': 2.3, 'C': 2.0, 'C-': 1.7,
            'D+': 1.3, 'D': 1.0, 'F': 0.0
        }

        points = grade_points.get(self.letter_grade, 0.0)
        return points * self.course.credit_hours

    def get_attempt_display(self):
        """Get display text for attempt number (e.g., 'R1', 'R2')"""
        if self.attempt_number == 1:
            return ""
        return f"R{self.attempt_number - 1}"

    def get_previous_attempts(self):
        """Get all previous attempts for this student-course combination"""
        return Enrollment.objects.filter(
            student=self.student,
            course=self.course,
            attempt_number__lt=self.attempt_number
        ).order_by('attempt_number')

    def get_latest_attempt(self):
        """Get the latest attempt for this student-course combination"""
        return Enrollment.objects.filter(
            student=self.student,
            course=self.course
        ).order_by('-attempt_number').first()

    @classmethod
    def get_student_course_history(cls, student, course):
        """Get complete enrollment history for a student-course combination"""
        return cls.objects.filter(
            student=student,
            course=course
        ).order_by('attempt_number')

    def calculate_letter_grade(self):
        """Calculate letter grade based on total grade"""
        if self.total_grade is None:
            return ''

        grade = float(self.total_grade)
        if grade >= 90:
            return 'A+'
        elif grade >= 85:
            return 'A'
        elif grade >= 80:
            return 'B+'
        elif grade >= 75:
            return 'B'
        elif grade >= 70:
            return 'C+'
        elif grade >= 65:
            return 'C'
        elif grade >= 60:
            return 'D'
        else:
            return 'F'

    def save(self, *args, **kwargs):
        # Auto-calculate attempt number for retakes
        if not self.pk:  # New enrollment
            previous_attempts = Enrollment.objects.filter(
                student=self.student,
                course=self.course
            ).count()

            if previous_attempts > 0:
                self.attempt_number = previous_attempts + 1
                self.is_retake = True
            else:
                self.attempt_number = 1
                self.is_retake = False

        # Check prerequisites if not overridden
        if not self.prerequisite_override:
            self.prerequisites_met = self.check_prerequisites()

        # Auto-calculate letter grade
        if self.total_grade is not None:
            self.letter_grade = self.calculate_letter_grade()

        # Calculate GPA points
        if self.letter_grade:
            self.gpa_points = self.calculate_gpa_points()

        super().save(*args, **kwargs)


class Waitlist(models.Model):
    """
    Course waitlist management
    """
    class WaitlistStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        ENROLLED = 'enrolled', _('Enrolled')
        DROPPED = 'dropped', _('Dropped')
        EXPIRED = 'expired', _('Expired')

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='waitlists',
        limit_choices_to={'role': 'student'}
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='waitlists')
    position = models.PositiveIntegerField(_('position'))
    status = models.CharField(_('status'), max_length=20, choices=WaitlistStatus.choices, default=WaitlistStatus.ACTIVE)

    # Timestamps
    added_at = models.DateTimeField(_('added at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    enrolled_at = models.DateTimeField(_('enrolled at'), null=True, blank=True)

    # Notification settings
    notify_when_available = models.BooleanField(_('notify when available'), default=True)
    notification_sent = models.BooleanField(_('notification sent'), default=False)

    class Meta:
        verbose_name = _('Waitlist Entry')
        verbose_name_plural = _('Waitlist Entries')
        unique_together = ['student', 'course']
        ordering = ['course', 'position']

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.course.code} (Position: {self.position})"

    def move_up(self):
        """Move student up one position in waitlist"""
        if self.position > 1:
            # Find the student above and swap positions
            above_student = Waitlist.objects.filter(
                course=self.course,
                position=self.position - 1,
                status='active'
            ).first()

            if above_student:
                above_student.position = self.position
                self.position -= 1
                above_student.save()
                self.save()

    def move_down(self):
        """Move student down one position in waitlist"""
        max_position = Waitlist.objects.filter(
            course=self.course,
            status='active'
        ).count()

        if self.position < max_position:
            # Find the student below and swap positions
            below_student = Waitlist.objects.filter(
                course=self.course,
                position=self.position + 1,
                status='active'
            ).first()

            if below_student:
                below_student.position = self.position
                self.position += 1
                below_student.save()
                self.save()

    @classmethod
    def add_to_waitlist(cls, student, course):
        """Add a student to the course waitlist"""
        # Get next position
        max_position = cls.objects.filter(
            course=course,
            status='active'
        ).aggregate(max_pos=models.Max('position'))['max_pos'] or 0

        waitlist_entry, created = cls.objects.get_or_create(
            student=student,
            course=course,
            defaults={
                'position': max_position + 1,
                'status': cls.WaitlistStatus.ACTIVE
            }
        )

        return waitlist_entry

    @classmethod
    def promote_from_waitlist(cls, course, count=1):
        """Promote students from waitlist to enrolled when spots become available"""
        promoted_students = []

        for _ in range(count):
            # Get next student in line
            next_student = cls.objects.filter(
                course=course,
                status=cls.WaitlistStatus.ACTIVE
            ).order_by('position').first()

            if next_student and not course.is_full:
                # Create enrollment
                enrollment = Enrollment.objects.create(
                    student=next_student.student,
                    course=course,
                    status=Enrollment.EnrollmentStatus.ENROLLED
                )

                # Update waitlist status
                next_student.status = cls.WaitlistStatus.ENROLLED
                next_student.enrolled_at = timezone.now()
                next_student.save()

                promoted_students.append(next_student.student)

                # Update positions for remaining students
                cls.objects.filter(
                    course=course,
                    status=cls.WaitlistStatus.ACTIVE,
                    position__gt=next_student.position
                ).update(position=models.F('position') - 1)

        return promoted_students


class EnrollmentAttemptLog(models.Model):
    """
    Comprehensive tracking of all enrollment attempts for security and audit
    Tracks every enrollment action to prevent abuse and ensure compliance
    """
    class AttemptType(models.TextChoices):
        ENROLLMENT = 'enrollment', _('Enrollment')
        DROP = 'drop', _('Drop')
        WITHDRAWAL = 'withdrawal', _('Withdrawal')
        COMPLETION = 'completion', _('Completion')
        FAILURE = 'failure', _('Failure')

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='enrollment_attempts',
        limit_choices_to={'role': 'student'}
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='enrollment_attempts')
    attempt_type = models.CharField(_('attempt type'), max_length=20, choices=AttemptType.choices)
    attempt_number = models.PositiveIntegerField(_('attempt number'))
    enrollment_status = models.CharField(_('enrollment status'), max_length=20)

    # Security tracking
    risk_score = models.PositiveIntegerField(_('security risk score'), default=0)
    admin_override = models.BooleanField(_('admin override used'), default=False)
    override_reason = models.TextField(_('override reason'), blank=True)

    # Audit trail
    ip_address = models.GenericIPAddressField(_('IP address'), null=True, blank=True)
    user_agent = models.TextField(_('user agent'), blank=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)

    class Meta:
        verbose_name = _('Enrollment Attempt Log')
        verbose_name_plural = _('Enrollment Attempt Logs')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.course.code} ({self.attempt_type})"

    @classmethod
    def log_attempt(cls, student, course, attempt_type, enrollment_status,
                   risk_score=0, admin_override=False, override_reason=None,
                   ip_address=None, user_agent=None):
        """Log an enrollment attempt for security tracking"""
        # Calculate attempt number
        attempt_number = cls.objects.filter(
            student=student,
            course=course
        ).count() + 1

        return cls.objects.create(
            student=student,
            course=course,
            attempt_type=attempt_type,
            attempt_number=attempt_number,
            enrollment_status=enrollment_status,
            risk_score=risk_score,
            admin_override=admin_override,
            override_reason=override_reason,
            ip_address=ip_address,
            user_agent=user_agent
        )

    @classmethod
    def get_total_attempts(cls, student, course):
        """Get total number of attempts for a student-course combination"""
        return cls.objects.filter(
            student=student,
            course=course,
            attempt_type__in=['enrollment', 'drop', 'withdrawal']
        ).count()

    @classmethod
    def get_suspicious_patterns(cls, student, hours=24):
        """Detect suspicious enrollment patterns for a student"""
        from django.utils import timezone

        recent_attempts = cls.objects.filter(
            student=student,
            created_at__gte=timezone.now() - timezone.timedelta(hours=hours)
        ).count()

        return {
            'recent_attempts': recent_attempts,
            'is_suspicious': recent_attempts > 10,  # More than 10 attempts in 24 hours
            'risk_level': 'high' if recent_attempts > 10 else 'medium' if recent_attempts > 5 else 'low'
        }




class SecurityAuditLog(models.Model):
    """
    Comprehensive security audit logging
    """
    class EventType(models.TextChoices):
        LOGIN_SUCCESS = 'login_success', _('Login Success')
        LOGIN_FAILURE = 'login_failure', _('Login Failure')
        LOGOUT = 'logout', _('Logout')
        PASSWORD_CHANGE = 'password_change', _('Password Change')
        PERMISSION_DENIED = 'permission_denied', _('Permission Denied')
        SUSPICIOUS_ACTIVITY = 'suspicious_activity', _('Suspicious Activity')
        DATA_ACCESS = 'data_access', _('Data Access')
        DATA_MODIFICATION = 'data_modification', _('Data Modification')
        RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded', _('Rate Limit Exceeded')
        SECURITY_VIOLATION = 'security_violation', _('Security Violation')

    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    event_type = models.CharField(max_length=50, choices=EventType.choices, db_index=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(db_index=True)
    user_agent = models.TextField()
    request_path = models.CharField(max_length=500)
    request_method = models.CharField(max_length=10)
    details = models.JSONField(default=dict)
    risk_score = models.IntegerField(default=0, db_index=True)  # 0-100 risk score

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp', 'event_type']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['user', 'event_type', 'timestamp']),
            models.Index(fields=['risk_score', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.event_type} - {self.user} - {self.timestamp}"
