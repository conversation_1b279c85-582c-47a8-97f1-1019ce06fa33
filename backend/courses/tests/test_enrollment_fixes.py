"""
Comprehensive tests for course enrollment system fixes
Tests enrollment status logic, retake scenarios, and course history tracking
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from datetime import date, timedelta

from courses.models import Course, Enrollment, Department
from courses.serializers import CourseSerializer

User = get_user_model()


class CourseEnrollmentFixesTestCase(APITestCase):
    """Test course enrollment system fixes"""

    def setUp(self):
        """Set up test data"""
        # Create test users
        self.student = User.objects.create_user(
            username='student1',
            email='<EMAIL>',
            password='testpass123',
            role='student',
            student_id='STU001'
        )
        
        self.teacher = User.objects.create_user(
            username='teacher1',
            email='<EMAIL>',
            password='testpass123',
            role='teacher',
            employee_id='TEA001'
        )
        
        # Create test department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS',
            description='Computer Science Department'
        )
        
        # Create test course
        self.course = Course.objects.create(
            title='Introduction to Programming',
            code='CS101',
            description='Basic programming concepts',
            department=self.department,
            instructor=self.teacher,
            level='undergraduate',
            credit_hours=3,
            semester='fall',
            year=2024,
            max_students=30,
            start_date=date.today() + timedelta(days=30),
            end_date=date.today() + timedelta(days=120),
            is_active=True,
            is_published=True
        )
        
        # Create prerequisite course
        self.prereq_course = Course.objects.create(
            title='Mathematics Fundamentals',
            code='MATH101',
            description='Basic mathematics',
            department=self.department,
            instructor=self.teacher,
            level='undergraduate',
            credit_hours=3,
            semester='fall',
            year=2024,
            max_students=30,
            start_date=date.today() + timedelta(days=30),
            end_date=date.today() + timedelta(days=120),
            is_active=True,
            is_published=True
        )
        
        # Set prerequisite
        self.course.prerequisites.add(self.prereq_course)

    def test_first_enrollment_status(self):
        """Test enrollment status for first-time enrollment"""
        self.client.force_authenticate(user=self.student)
        
        # Get course details
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        course_data = response.data
        
        # Should show as available (no enrollment history)
        self.assertEqual(course_data['user_enrollment_status'], 'available')
        self.assertEqual(course_data['user_current_attempt'], 1)
        self.assertFalse(course_data['user_can_enroll'])  # Due to prerequisites
        self.assertIn('prerequisites', course_data['user_enrollment_message'].lower())

    def test_enrollment_after_prerequisite_completion(self):
        """Test enrollment after completing prerequisites"""
        # Complete prerequisite
        prereq_enrollment = Enrollment.objects.create(
            student=self.student,
            course=self.prereq_course,
            status=Enrollment.EnrollmentStatus.COMPLETED,
            final_grade=85.0,
            letter_grade='B',
            is_active=True
        )
        
        self.client.force_authenticate(user=self.student)
        
        # Get course details
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data
        
        # Should be able to enroll now
        self.assertEqual(course_data['user_enrollment_status'], 'available')
        self.assertTrue(course_data['user_can_enroll'])
        self.assertEqual(course_data['user_enrollment_message'], 'Can enroll')

    def test_failed_course_retake_status(self):
        """Test enrollment status after failing a course"""
        # Complete prerequisite first
        Enrollment.objects.create(
            student=self.student,
            course=self.prereq_course,
            status=Enrollment.EnrollmentStatus.COMPLETED,
            final_grade=85.0,
            letter_grade='B',
            is_active=True
        )
        
        # Create failed enrollment
        failed_enrollment = Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status=Enrollment.EnrollmentStatus.FAILED,
            final_grade=45.0,
            letter_grade='F',
            attempt_number=1,
            is_retake=False,
            is_active=False
        )
        
        self.client.force_authenticate(user=self.student)
        
        # Get course details
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data
        
        # Should show as retakeable
        self.assertEqual(course_data['user_enrollment_status'], 'retakeable')
        self.assertTrue(course_data['user_can_enroll'])
        self.assertIn('retake', course_data['user_enrollment_message'].lower())
        self.assertEqual(course_data['user_current_attempt'], 2)

    def test_multiple_retake_attempts(self):
        """Test multiple retake attempts and limits"""
        # Complete prerequisite
        Enrollment.objects.create(
            student=self.student,
            course=self.prereq_course,
            status=Enrollment.EnrollmentStatus.COMPLETED,
            final_grade=85.0,
            letter_grade='B',
            is_active=True
        )
        
        # Create 3 failed attempts
        for attempt in range(1, 4):
            Enrollment.objects.create(
                student=self.student,
                course=self.course,
                status=Enrollment.EnrollmentStatus.FAILED,
                final_grade=45.0,
                letter_grade='F',
                attempt_number=attempt,
                is_retake=attempt > 1,
                is_active=False
            )
        
        self.client.force_authenticate(user=self.student)
        
        # Get course details
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data
        
        # Should not be able to retake (exceeded limit)
        self.assertEqual(course_data['user_enrollment_status'], 'retakeable')
        self.assertFalse(course_data['user_can_enroll'])
        self.assertIn('maximum', course_data['user_enrollment_message'].lower())

    def test_completed_course_status(self):
        """Test status for completed course"""
        # Complete prerequisite
        Enrollment.objects.create(
            student=self.student,
            course=self.prereq_course,
            status=Enrollment.EnrollmentStatus.COMPLETED,
            final_grade=85.0,
            letter_grade='B',
            is_active=True
        )
        
        # Complete the course
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status=Enrollment.EnrollmentStatus.COMPLETED,
            final_grade=88.0,
            letter_grade='B+',
            attempt_number=1,
            is_active=True
        )
        
        self.client.force_authenticate(user=self.student)
        
        # Get course details
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data
        
        # Should show as completed and not retakeable
        self.assertEqual(course_data['user_enrollment_status'], 'completed')
        self.assertFalse(course_data['user_can_enroll'])
        self.assertIn('completed', course_data['user_enrollment_message'].lower())

    def test_active_enrollment_status(self):
        """Test status for currently enrolled student"""
        # Complete prerequisite
        Enrollment.objects.create(
            student=self.student,
            course=self.prereq_course,
            status=Enrollment.EnrollmentStatus.COMPLETED,
            final_grade=85.0,
            letter_grade='B',
            is_active=True
        )
        
        # Create active enrollment
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status=Enrollment.EnrollmentStatus.ENROLLED,
            attempt_number=1,
            is_active=True
        )
        
        self.client.force_authenticate(user=self.student)
        
        # Get course details
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data
        
        # Should show as enrolled
        self.assertEqual(course_data['user_enrollment_status'], 'enrolled')
        self.assertFalse(course_data['user_can_enroll'])
        self.assertIn('already enrolled', course_data['user_enrollment_message'].lower())
        self.assertEqual(course_data['user_current_attempt'], 1)

    def test_enrollment_details_method(self):
        """Test the new user_enrollment_details method"""
        # Create a failed enrollment
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status=Enrollment.EnrollmentStatus.FAILED,
            final_grade=45.0,
            letter_grade='F',
            attempt_number=1,
            is_active=False
        )
        
        self.client.force_authenticate(user=self.student)
        
        # Get course details
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data
        
        details = course_data['user_enrollment_details']
        
        # Verify enrollment details
        self.assertTrue(details['has_enrollment_history'])
        self.assertEqual(details['total_attempts'], 1)
        self.assertEqual(details['latest_status'], 'failed')
        self.assertEqual(details['latest_grade'], 'F')
        self.assertTrue(details['is_retake_eligible'])
        self.assertEqual(details['retake_count'], 1)
        self.assertFalse(details['is_active'])

    def test_retake_enrollment_creation(self):
        """Test creating a retake enrollment"""
        # Complete prerequisite
        Enrollment.objects.create(
            student=self.student,
            course=self.prereq_course,
            status=Enrollment.EnrollmentStatus.COMPLETED,
            final_grade=85.0,
            letter_grade='B',
            is_active=True
        )
        
        # Create failed enrollment
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status=Enrollment.EnrollmentStatus.FAILED,
            final_grade=45.0,
            letter_grade='F',
            attempt_number=1,
            is_retake=False,
            is_active=False
        )
        
        self.client.force_authenticate(user=self.student)
        
        # Attempt to enroll again (retake)
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        
        # Should succeed
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['type'], 'enrollment')
        
        # Verify retake enrollment was created
        retake_enrollment = Enrollment.objects.filter(
            student=self.student,
            course=self.course,
            is_active=True
        ).first()
        
        self.assertIsNotNone(retake_enrollment)
        self.assertEqual(retake_enrollment.attempt_number, 2)
        self.assertTrue(retake_enrollment.is_retake)
        self.assertEqual(retake_enrollment.status, Enrollment.EnrollmentStatus.ENROLLED)

    def test_enrollment_lifecycle_management_fix(self):
        """Test the critical fix: previous enrollments are properly deactivated during retakes"""
        # First enrollment
        self.client.force_authenticate(user=self.student)
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Get first enrollment
        first_enrollment = Enrollment.objects.get(student=self.student, course=self.course)
        first_enrollment_id = first_enrollment.id

        # Simulate course failure
        first_enrollment.status = 'failed'
        first_enrollment.is_active = False
        first_enrollment.save()

        # Second enrollment (retake) - this should deactivate any previous active enrollments
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # CRITICAL TEST: Verify only one active enrollment exists
        active_enrollments = Enrollment.objects.filter(
            student=self.student,
            course=self.course,
            is_active=True
        )
        self.assertEqual(active_enrollments.count(), 1,
                        "Should have exactly one active enrollment after retake")

        # Verify the new enrollment is the active one
        new_enrollment = active_enrollments.first()
        self.assertNotEqual(new_enrollment.id, first_enrollment_id)
        self.assertEqual(new_enrollment.attempt_number, 2)
        self.assertTrue(new_enrollment.is_retake)

        # Verify first enrollment remains inactive
        first_enrollment.refresh_from_db()
        self.assertFalse(first_enrollment.is_active)
        self.assertEqual(first_enrollment.status, 'failed')

    def test_status_determination_fix(self):
        """Test the critical fix: status determination prioritizes active enrollments"""
        # Enroll student
        self.client.force_authenticate(user=self.student)
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Get course details to check status
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data

        # CRITICAL TEST: Status should be 'enrolled' for active enrollment
        self.assertEqual(course_data['user_enrollment_status'], 'enrolled',
                        "Active enrollment should show status as 'enrolled'")

        # Simulate course failure
        enrollment = Enrollment.objects.get(student=self.student, course=self.course)
        enrollment.status = 'failed'
        enrollment.is_active = False
        enrollment.save()

        # Check status after failure
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data

        # Should now show as retakeable
        self.assertEqual(course_data['user_enrollment_status'], 'retakeable',
                        "Failed course should show status as 'retakeable'")

        # Enroll again (retake)
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check status after retake enrollment
        response = self.client.get(f'/api/courses/{self.course.id}/')
        course_data = response.data

        # Should show as enrolled again
        self.assertEqual(course_data['user_enrollment_status'], 'enrolled',
                        "New enrollment should show status as 'enrolled'")
