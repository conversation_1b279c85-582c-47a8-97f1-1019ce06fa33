"""
Test cases for enrollment status fixes
Verifies that the enrollment system properly handles status updates and cache invalidation
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.core.cache import cache
from unittest.mock import patch

from courses.models import Course, Department, Enrollment
from courses.serializers import CourseSerializer
from backend.cache_config import cache_manager

User = get_user_model()


class EnrollmentStatusFixesTestCase(APITestCase):
    """Test enrollment status fixes"""
    
    def setUp(self):
        """Set up test data"""
        # Clear cache before each test
        cache.clear()
        cache_manager.cache_stats = {'hits': 0, 'misses': 0, 'invalidations': 0, 'errors': 0}
        
        # Create test users
        self.student = User.objects.create_user(
            username='student',
            email='<EMAIL>',
            password='testpass123',
            role='student',
            student_id='STU001'
        )

        self.teacher = User.objects.create_user(
            username='teacher',
            email='<EMAIL>',
            password='testpass123',
            role='teacher',
            employee_id='TEA001'
        )
        
        # Create test department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS',
            head=self.teacher
        )
        
        # Create test course
        self.course = Course.objects.create(
            title='Introduction to Programming',
            code='CS101',
            description='Basic programming concepts',
            department=self.department,
            instructor=self.teacher,
            credit_hours=3,
            semester='fall',
            year=2024,
            max_students=30,
            start_date='2024-09-01',
            end_date='2024-12-15',
            is_active=True,
            is_published=True
        )
        
        self.client = APIClient()
    
    def test_enrollment_status_no_caching(self):
        """Test that enrollment status is not cached and updates immediately"""
        self.client.force_authenticate(user=self.student)
        
        # Initial status should be 'available'
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_enrollment_status'], 'available')
        
        # Enroll student
        enroll_response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(enroll_response.status_code, status.HTTP_201_CREATED)
        
        # Status should immediately update to 'enrolled' (no caching)
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_enrollment_status'], 'enrolled')
    
    def test_retake_status_logic(self):
        """Test retake status determination"""
        self.client.force_authenticate(user=self.student)
        
        # Create a failed enrollment
        failed_enrollment = Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status='failed',
            attempt_number=1,
            is_active=False
        )
        
        # Status should be 'retakeable'
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_enrollment_status'], 'retakeable')
        self.assertTrue(response.data['user_can_enroll'])
    
    def test_retake_limit_exceeded(self):
        """Test retake limit exceeded status"""
        self.client.force_authenticate(user=self.student)
        
        # Create 3 failed enrollments (max limit)
        for i in range(3):
            Enrollment.objects.create(
                student=self.student,
                course=self.course,
                status='failed',
                attempt_number=i + 1,
                is_active=False
            )
        
        # Status should be 'retake_limit_exceeded'
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_enrollment_status'], 'retake_limit_exceeded')
        self.assertFalse(response.data['user_can_enroll'])
    
    def test_completed_course_status(self):
        """Test completed course status"""
        self.client.force_authenticate(user=self.student)
        
        # Create a completed enrollment
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status='completed',
            attempt_number=1,
            is_active=False,
            letter_grade='A'
        )
        
        # Status should be 'completed'
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_enrollment_status'], 'completed')
        self.assertFalse(response.data['user_can_enroll'])
    
    def test_cache_invalidation_on_enrollment(self):
        """Test that cache is properly invalidated when enrollment changes"""
        self.client.force_authenticate(user=self.student)
        
        # Make initial request to potentially cache data
        response1 = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response1.data['user_enrollment_status'], 'available')
        
        # Enroll student (this should trigger cache invalidation)
        enroll_response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(enroll_response.status_code, status.HTTP_201_CREATED)
        
        # Subsequent request should show updated status
        response2 = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response2.data['user_enrollment_status'], 'enrolled')
    
    def test_enrollment_message_accuracy(self):
        """Test that enrollment messages are accurate"""
        self.client.force_authenticate(user=self.student)
        
        # Test available course message
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertIn('Available', response.data['user_enrollment_message'])
        
        # Enroll student
        self.client.post(f'/api/courses/{self.course.id}/enroll/')
        
        # Test enrolled message
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertIn('enrolled', response.data['user_enrollment_message'].lower())
        self.assertIn('Attempt #1', response.data['user_enrollment_message'])
    
    def test_current_attempt_calculation(self):
        """Test current attempt number calculation"""
        self.client.force_authenticate(user=self.student)
        
        # No enrollment - should be attempt 1
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.data['user_current_attempt'], 1)
        
        # Create failed enrollment
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status='failed',
            attempt_number=1,
            is_active=False
        )
        
        # Next attempt should be 2
        response = self.client.get(f'/api/courses/{self.course.id}/')
        self.assertEqual(response.data['user_current_attempt'], 2)
    
    def test_enrollment_history_tracking(self):
        """Test enrollment history is properly tracked"""
        self.client.force_authenticate(user=self.student)
        
        # Create multiple enrollments
        enrollments = []
        for i in range(2):
            enrollment = Enrollment.objects.create(
                student=self.student,
                course=self.course,
                status='failed' if i == 0 else 'enrolled',
                attempt_number=i + 1,
                is_active=i == 1  # Only latest is active
            )
            enrollments.append(enrollment)
        
        response = self.client.get(f'/api/courses/{self.course.id}/')
        history = response.data['user_enrollment_history']
        
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]['attempt_number'], 2)  # Latest first
        self.assertEqual(history[1]['attempt_number'], 1)
    
    def tearDown(self):
        """Clean up after each test"""
        cache.clear()


class CourseSerializerTestCase(TestCase):
    """Test CourseSerializer without caching"""
    
    def setUp(self):
        self.student = User.objects.create_user(
            username='student2',
            email='<EMAIL>',
            password='testpass123',
            role='student'
        )

        self.teacher = User.objects.create_user(
            username='teacher2',
            email='<EMAIL>',
            password='testpass123',
            role='teacher'
        )
        
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS',
            head=self.teacher
        )
        
        self.course = Course.objects.create(
            title='Test Course',
            code='TEST101',
            description='Test course',
            department=self.department,
            instructor=self.teacher,
            credit_hours=3,
            semester='fall',
            year=2024,
            max_students=30,
            start_date='2024-09-01',
            end_date='2024-12-15',
            is_active=True,
            is_published=True
        )
    
    def test_serializer_no_caching_decorator(self):
        """Test that serializer methods don't use caching decorator"""
        from courses.serializers import CourseSerializer
        
        # Check that get_user_enrollment_status doesn't have cache decorator
        method = CourseSerializer.get_user_enrollment_status
        self.assertFalse(hasattr(method, 'invalidate_cache'))
    
    def test_serializer_consistent_status_determination(self):
        """Test that serializer consistently determines enrollment status"""
        class MockRequest:
            def __init__(self, user):
                self.user = user
        
        mock_request = MockRequest(self.student)
        serializer = CourseSerializer(self.course, context={'request': mock_request})
        
        # Test available status
        status = serializer.get_user_enrollment_status(self.course)
        self.assertEqual(status, 'available')
        
        # Create enrollment and test again
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            status='enrolled',
            is_active=True
        )
        
        status = serializer.get_user_enrollment_status(self.course)
        self.assertEqual(status, 'enrolled')
