"""
Comprehensive Security Testing for Enrollment System
Tests all security vulnerabilities and business rule violations
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from django.db import IntegrityError, transaction
from rest_framework.test import APITestCase
from rest_framework import status

from courses.models import Course, Enrollment, Department, EnrollmentAttemptLog
from courses.enrollment_security import EnrollmentSecurityManager, EnrollmentViolationType

User = get_user_model()


class EnrollmentSecurityTestCase(TestCase):
    """Test enrollment security validation system"""
    
    def setUp(self):
        # Clear cache
        cache.clear()
        
        # Create test data
        self.department = Department.objects.create(name="Security Test", code="SEC")
        self.teacher = User.objects.create_user(
            username="security_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        self.student = User.objects.create_user(
            username="security_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        self.admin = User.objects.create_user(
            username="security_admin", email="<EMAIL>", 
            password="password123", role="admin"
        )
        self.course = Course.objects.create(
            title="Security Test Course", code="SEC101", description="Test",
            department=self.department, instructor=self.teacher, 
            level="undergraduate", credit_hours=3, semester="spring", year=2025,
            max_students=30, is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
    
    def test_completed_course_protection(self):
        """CRITICAL: Test ironclad protection against re-enrolling in completed courses"""
        # Complete the course
        enrollment = Enrollment.objects.create(
            student=self.student, course=self.course, status='completed'
        )
        
        # Try to enroll again - should be blocked
        security_manager = EnrollmentSecurityManager(self.student, self.course)
        result = security_manager.validate_enrollment()
        
        self.assertFalse(result.allowed)
        self.assertEqual(result.violation_type, EnrollmentViolationType.COMPLETED_COURSE_RETRY)
        self.assertIn("SECURITY VIOLATION", result.message)
        self.assertGreaterEqual(result.risk_score, 90)
    
    def test_admin_override_completed_course(self):
        """Test that even admin override cannot bypass completed course protection"""
        # Complete the course
        enrollment = Enrollment.objects.create(
            student=self.student, course=self.course, status='completed'
        )
        
        # Try admin override - should still be blocked for completed courses
        security_manager = EnrollmentSecurityManager(self.student, self.course)
        result = security_manager.validate_enrollment(
            admin_override=True, 
            override_reason="Admin trying to override completed course"
        )
        
        # Even admin override should not allow re-enrollment in completed courses
        self.assertFalse(result.allowed)
        self.assertEqual(result.violation_type, EnrollmentViolationType.COMPLETED_COURSE_RETRY)
    
    def test_total_attempt_limits(self):
        """Test comprehensive attempt limits (not just failed attempts)"""
        # Create 3 enrollment attempts (mix of statuses)
        for i in range(3):
            enrollment = Enrollment.objects.create(
                student=self.student, course=self.course, 
                status='dropped' if i < 2 else 'failed'
            )
            EnrollmentAttemptLog.log_attempt(
                student=self.student, course=self.course,
                attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                enrollment_status=enrollment.status
            )
        
        # 4th attempt should be blocked
        security_manager = EnrollmentSecurityManager(self.student, self.course)
        result = security_manager.validate_enrollment()
        
        self.assertFalse(result.allowed)
        self.assertEqual(result.violation_type, EnrollmentViolationType.EXCESSIVE_ATTEMPTS)
        self.assertIn("Maximum total attempts exceeded", result.message)
    
    def test_rate_limiting(self):
        """Test rate limiting prevents rapid enrollment attempts"""
        security_manager = EnrollmentSecurityManager(self.student, self.course)
        
        # First attempt should pass
        result1 = security_manager.validate_enrollment()
        self.assertTrue(result1.allowed)
        
        # Immediate second attempt should be blocked
        result2 = security_manager.validate_enrollment()
        self.assertFalse(result2.allowed)
        self.assertEqual(result2.violation_type, EnrollmentViolationType.RAPID_ENROLLMENT)
    
    def test_suspicious_pattern_detection(self):
        """Test detection of suspicious enrollment patterns"""
        # Create many recent attempts to trigger suspicious pattern detection
        for i in range(12):
            EnrollmentAttemptLog.log_attempt(
                student=self.student, course=self.course,
                attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                enrollment_status='blocked'
            )
        
        security_manager = EnrollmentSecurityManager(self.student, self.course)
        result = security_manager.validate_enrollment()
        
        self.assertFalse(result.allowed)
        self.assertEqual(result.violation_type, EnrollmentViolationType.RAPID_ENROLLMENT)
        self.assertIn("Suspicious enrollment pattern", result.message)
        self.assertGreaterEqual(result.risk_score, 90)
    
    def test_concurrent_enrollment_limits(self):
        """Test limits on concurrent enrollments per semester"""
        # Create maximum concurrent enrollments
        for i in range(8):
            other_course = Course.objects.create(
                title=f"Course {i}", code=f"TEST{i:03d}", description="Test",
                department=self.department, instructor=self.teacher,
                level="undergraduate", credit_hours=3, semester="spring", year=2025,
                max_students=30, is_active=True, is_published=True,
                start_date=timezone.now().date(),
                end_date=timezone.now().date() + timezone.timedelta(days=90)
            )
            Enrollment.objects.create(
                student=self.student, course=other_course, 
                status='enrolled', is_active=True
            )
        
        # 9th enrollment should be blocked
        security_manager = EnrollmentSecurityManager(self.student, self.course)
        result = security_manager.validate_enrollment()
        
        self.assertFalse(result.allowed)
        self.assertIn("Maximum concurrent enrollments exceeded", result.message)


class EnrollmentAPISecurityTestCase(APITestCase):
    """Test API security for enrollment endpoints"""
    
    def setUp(self):
        cache.clear()
        
        self.department = Department.objects.create(name="API Security", code="API")
        self.teacher = User.objects.create_user(
            username="api_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        self.student = User.objects.create_user(
            username="api_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        self.course = Course.objects.create(
            title="API Security Course", code="API101", description="Test",
            department=self.department, instructor=self.teacher,
            level="undergraduate", credit_hours=3, semester="spring", year=2025,
            max_students=30, is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
    
    def test_api_rate_limiting(self):
        """Test API rate limiting prevents spam"""
        self.client.force_authenticate(user=self.student)
        
        # Make 5 rapid requests (should hit rate limit)
        for i in range(6):
            response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
            if i < 5:
                # First 5 should either succeed or fail with business logic
                self.assertIn(response.status_code, [201, 400])
            else:
                # 6th should be rate limited
                self.assertEqual(response.status_code, 429)
                self.assertIn("Too many enrollment attempts", response.data['error'])
    
    def test_completed_course_api_protection(self):
        """Test API blocks re-enrollment in completed courses"""
        # Complete the course
        Enrollment.objects.create(
            student=self.student, course=self.course, status='completed'
        )
        
        self.client.force_authenticate(user=self.student)
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        
        self.assertEqual(response.status_code, 400)
        self.assertIn("ENROLLMENT BLOCKED", response.data['error'])
        self.assertIn("completed course", response.data['error'])
    
    def test_unauthorized_enrollment_blocked(self):
        """Test unauthorized users cannot enroll"""
        # Unauthenticated request
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(response.status_code, 401)
        
        # Non-student user
        teacher = User.objects.create_user(
            username="non_student", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        self.client.force_authenticate(user=teacher)
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        self.assertEqual(response.status_code, 403)
    
    def test_audit_trail_logging(self):
        """Test that all enrollment attempts are logged for audit"""
        self.client.force_authenticate(user=self.student)
        
        # Make enrollment attempt
        response = self.client.post(f'/api/courses/{self.course.id}/enroll/')
        
        # Check audit log was created
        log_entry = EnrollmentAttemptLog.objects.filter(
            student=self.student, course=self.course
        ).first()
        
        self.assertIsNotNone(log_entry)
        self.assertEqual(log_entry.attempt_type, EnrollmentAttemptLog.AttemptType.ENROLLMENT)
        self.assertIsNotNone(log_entry.ip_address)


class DatabaseConstraintTestCase(TransactionTestCase):
    """Test database-level security constraints"""
    
    def setUp(self):
        self.department = Department.objects.create(name="DB Test", code="DB")
        self.teacher = User.objects.create_user(
            username="db_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        self.student = User.objects.create_user(
            username="db_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        self.course = Course.objects.create(
            title="DB Test Course", code="DB101", description="Test",
            department=self.department, instructor=self.teacher,
            level="undergraduate", credit_hours=3, semester="spring", year=2025,
            max_students=30, is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
    
    def test_database_prevents_multiple_active_enrollments(self):
        """Test database constraint prevents multiple active enrollments"""
        # Create first active enrollment
        Enrollment.objects.create(
            student=self.student, course=self.course, 
            status='enrolled', is_active=True
        )
        
        # Try to create second active enrollment - should fail at database level
        with self.assertRaises(IntegrityError):
            with transaction.atomic():
                Enrollment.objects.create(
                    student=self.student, course=self.course, 
                    status='enrolled', is_active=True
                )
    
    def test_database_prevents_excessive_attempts(self):
        """Test database constraint prevents excessive enrollment attempts"""
        # Create maximum allowed attempts
        for i in range(5):
            EnrollmentAttemptLog.log_attempt(
                student=self.student, course=self.course,
                attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                enrollment_status='blocked'
            )
        
        # 6th attempt should fail at database level
        with self.assertRaises(Exception):  # Database constraint violation
            with transaction.atomic():
                EnrollmentAttemptLog.log_attempt(
                    student=self.student, course=self.course,
                    attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                    enrollment_status='blocked'
                )
