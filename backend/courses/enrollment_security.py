"""
ROBUST ENROLLMENT SECURITY SYSTEM
Software Architecture for Scalable Educational Platforms

This module implements a comprehensive enrollment validation system with:
- Defense-in-depth security
- Ironclad business rule enforcement
- Audit trails and monitoring
- Performance optimization for scale
"""

from django.db import transaction, models
from django.core.cache import cache
from django.utils import timezone
from django.core.exceptions import ValidationError
from typing import Tuple, Dict, List, Optional
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class EnrollmentViolationType(Enum):
    """Types of enrollment violations for security monitoring"""
    COMPLETED_COURSE_RETRY = "completed_course_retry"
    EXCESSIVE_ATTEMPTS = "excessive_attempts"
    RAPID_ENROLLMENT = "rapid_enrollment"
    PREREQUISITE_BYPASS = "prerequisite_bypass"
    CAPACITY_VIOLATION = "capacity_violation"
    INVALID_ACADEMIC_PERIOD = "invalid_academic_period"


@dataclass
class EnrollmentSecurityResult:
    """Result of enrollment security validation"""
    allowed: bool
    violation_type: Optional[EnrollmentViolationType]
    message: str
    risk_score: int  # 0-100, higher = more suspicious
    audit_data: Dict


class EnrollmentSecurityManager:
    """
    Comprehensive enrollment security and validation manager
    Implements defense-in-depth approach with multiple validation layers
    """
    
    # Business Rules Configuration
    MAX_TOTAL_ATTEMPTS_PER_COURSE = 3  # Total attempts (including drops)
    MAX_FAILED_ATTEMPTS = 3  # Failed attempts only
    MIN_TIME_BETWEEN_ENROLLMENTS = 10  # 10 seconds (reduced for testing)
    MAX_CONCURRENT_ENROLLMENTS = 8  # Per semester
    COMPLETION_PROTECTION_ENABLED = True  # Ironclad completion protection
    
    def __init__(self, student, course):
        self.student = student
        self.course = course
        self.cache_key_prefix = f"enrollment_security:{student.id}:{course.id}"
        
    def validate_enrollment(self, enrollment_type='regular', 
                          admin_override=False, override_reason=None) -> EnrollmentSecurityResult:
        """
        MASTER VALIDATION METHOD - All enrollments must pass through here
        Implements comprehensive security validation with audit trails
        """
        audit_data = {
            'student_id': self.student.id,
            'course_id': self.course.id,
            'enrollment_type': enrollment_type,
            'admin_override': admin_override,
            'override_reason': override_reason,
            'timestamp': timezone.now().isoformat(),
            'validation_layers': []
        }
        
        try:
            # Layer 1: Basic Eligibility
            result = self._validate_basic_eligibility()
            audit_data['validation_layers'].append(('basic_eligibility', result.allowed, result.message))
            if not result.allowed:
                return self._create_security_result(False, result.violation_type, result.message, 90, audit_data)
            
            # Layer 2: Course Completion Protection (IRONCLAD)
            if self.COMPLETION_PROTECTION_ENABLED:
                result = self._validate_completion_protection()
                audit_data['validation_layers'].append(('completion_protection', result.allowed, result.message))
                if not result.allowed and not self._is_valid_admin_override(admin_override, 'completion_override'):
                    return self._create_security_result(False, result.violation_type, result.message, 95, audit_data)
            
            # Layer 3: Attempt Limits
            result = self._validate_attempt_limits()
            audit_data['validation_layers'].append(('attempt_limits', result.allowed, result.message))
            if not result.allowed and not self._is_valid_admin_override(admin_override, 'attempt_override'):
                return self._create_security_result(False, result.violation_type, result.message, 85, audit_data)
            
            # Layer 4: Rate Limiting & Anti-Spam
            result = self._validate_rate_limits()
            audit_data['validation_layers'].append(('rate_limits', result.allowed, result.message))
            if not result.allowed:
                return self._create_security_result(False, result.violation_type, result.message, 80, audit_data)
            
            # Layer 5: Academic Integrity
            result = self._validate_academic_integrity()
            audit_data['validation_layers'].append(('academic_integrity', result.allowed, result.message))
            if not result.allowed:
                return self._create_security_result(False, result.violation_type, result.message, 75, audit_data)
            
            # Layer 6: Prerequisites & Dependencies
            result = self._validate_prerequisites()
            audit_data['validation_layers'].append(('prerequisites', result.allowed, result.message))
            if not result.allowed and not self._is_valid_admin_override(admin_override, 'prerequisite_override'):
                return self._create_security_result(False, result.violation_type, result.message, 70, audit_data)
            
            # Layer 7: Capacity & Scheduling
            result = self._validate_capacity_and_scheduling()
            audit_data['validation_layers'].append(('capacity_scheduling', result.allowed, result.message))
            if not result.allowed:
                return self._create_security_result(False, result.violation_type, result.message, 60, audit_data)
            
            # All validations passed
            return self._create_security_result(True, None, "Enrollment approved", 10, audit_data)
            
        except Exception as e:
            logger.error(f"Enrollment validation error: {e}", exc_info=True)
            audit_data['error'] = str(e)
            return self._create_security_result(False, None, "System validation error", 100, audit_data)
    
    def _validate_basic_eligibility(self) -> EnrollmentSecurityResult:
        """Layer 1: Basic student and course eligibility"""
        # Check if student is active
        if not self.student.is_active:
            return EnrollmentSecurityResult(False, None, "Student account is inactive", 90, {})
        
        # Check if course is published and active
        if not self.course.is_published or not self.course.is_active:
            return EnrollmentSecurityResult(False, None, "Course is not available for enrollment", 80, {})
        
        # Check if student role is valid
        if self.student.role != 'student':
            return EnrollmentSecurityResult(False, None, "Only students can enroll in courses", 95, {})
        
        return EnrollmentSecurityResult(True, None, "Basic eligibility passed", 0, {})
    
    def _validate_completion_protection(self) -> EnrollmentSecurityResult:
        """Layer 2: IRONCLAD protection against re-enrolling in completed courses"""
        from .models import Enrollment
        
        # Check ALL enrollment history for ANY completion
        completed_enrollments = Enrollment.objects.filter(
            student=self.student,
            course=self.course,
            status='completed'
        ).exists()
        
        if completed_enrollments:
            return EnrollmentSecurityResult(
                False, 
                EnrollmentViolationType.COMPLETED_COURSE_RETRY,
                "SECURITY VIOLATION: Cannot re-enroll in successfully completed course",
                95, 
                {'completed_enrollments_found': True}
            )
        
        return EnrollmentSecurityResult(True, None, "Completion protection passed", 0, {})
    
    def _validate_attempt_limits(self) -> EnrollmentSecurityResult:
        """Layer 3: Comprehensive attempt limit validation using audit logs"""
        from .models import Enrollment

        try:
            # Try to use comprehensive attempt tracking if available
            from .models import EnrollmentAttemptLog
            total_attempts = EnrollmentAttemptLog.get_total_attempts(self.student, self.course)

            if total_attempts >= self.MAX_TOTAL_ATTEMPTS_PER_COURSE:
                return EnrollmentSecurityResult(
                    False,
                    EnrollmentViolationType.EXCESSIVE_ATTEMPTS,
                    f"SECURITY VIOLATION: Maximum total attempts exceeded ({total_attempts}/{self.MAX_TOTAL_ATTEMPTS_PER_COURSE})",
                    90,
                    {'total_attempts': total_attempts, 'limit': self.MAX_TOTAL_ATTEMPTS_PER_COURSE}
                )

            # Check for suspicious patterns
            suspicious_patterns = EnrollmentAttemptLog.get_suspicious_patterns(self.student)
            if suspicious_patterns['is_suspicious']:
                return EnrollmentSecurityResult(
                    False,
                    EnrollmentViolationType.RAPID_ENROLLMENT,
                    f"SECURITY VIOLATION: Suspicious enrollment pattern detected ({suspicious_patterns['recent_attempts']} attempts in 24h)",
                    95,
                    suspicious_patterns
                )

        except Exception as e:
            # Fallback to basic enrollment counting if EnrollmentAttemptLog is not available
            logger.warning(f"EnrollmentAttemptLog not available, using fallback validation: {e}")

            # Count total enrollments as proxy for attempts
            total_attempts = Enrollment.objects.filter(
                student=self.student,
                course=self.course
            ).count()

            if total_attempts >= self.MAX_TOTAL_ATTEMPTS_PER_COURSE:
                return EnrollmentSecurityResult(
                    False,
                    EnrollmentViolationType.EXCESSIVE_ATTEMPTS,
                    f"SECURITY VIOLATION: Maximum total attempts exceeded ({total_attempts}/{self.MAX_TOTAL_ATTEMPTS_PER_COURSE})",
                    85,
                    {'total_attempts': total_attempts, 'limit': self.MAX_TOTAL_ATTEMPTS_PER_COURSE, 'fallback_mode': True}
                )

        # Count failed attempts specifically
        failed_attempts = Enrollment.objects.filter(
            student=self.student,
            course=self.course,
            status='failed'
        ).count()

        if failed_attempts >= self.MAX_FAILED_ATTEMPTS:
            return EnrollmentSecurityResult(
                False,
                EnrollmentViolationType.EXCESSIVE_ATTEMPTS,
                f"SECURITY VIOLATION: Maximum failed attempts exceeded ({failed_attempts}/{self.MAX_FAILED_ATTEMPTS})",
                85,
                {'failed_attempts': failed_attempts, 'limit': self.MAX_FAILED_ATTEMPTS}
            )

        return EnrollmentSecurityResult(True, None, "Attempt limits passed", 0, {})
    
    def _validate_rate_limits(self) -> EnrollmentSecurityResult:
        """Layer 4: Rate limiting and anti-spam protection"""
        # Check for rapid enrollment attempts
        cache_key = f"{self.cache_key_prefix}:last_attempt"
        last_attempt = cache.get(cache_key)
        
        if last_attempt:
            time_since_last = (timezone.now() - last_attempt).total_seconds()
            if time_since_last < self.MIN_TIME_BETWEEN_ENROLLMENTS:
                return EnrollmentSecurityResult(
                    False,
                    EnrollmentViolationType.RAPID_ENROLLMENT,
                    f"Too many enrollment attempts. Wait {self.MIN_TIME_BETWEEN_ENROLLMENTS - time_since_last:.0f} seconds",
                    80,
                    {'time_since_last': time_since_last, 'min_required': self.MIN_TIME_BETWEEN_ENROLLMENTS}
                )
        
        # Set rate limit cache
        cache.set(cache_key, timezone.now(), self.MIN_TIME_BETWEEN_ENROLLMENTS)
        
        return EnrollmentSecurityResult(True, None, "Rate limits passed", 0, {})
    
    def _validate_academic_integrity(self) -> EnrollmentSecurityResult:
        """Layer 5: Academic integrity and concurrent enrollment limits"""
        from .models import Enrollment
        
        # Check concurrent enrollments in same semester
        current_semester_enrollments = Enrollment.objects.filter(
            student=self.student,
            course__semester=self.course.semester,
            course__year=self.course.year,
            status='enrolled',
            is_active=True
        ).count()
        
        if current_semester_enrollments >= self.MAX_CONCURRENT_ENROLLMENTS:
            return EnrollmentSecurityResult(
                False,
                None,
                f"Maximum concurrent enrollments exceeded ({current_semester_enrollments}/{self.MAX_CONCURRENT_ENROLLMENTS})",
                75,
                {'concurrent_enrollments': current_semester_enrollments, 'limit': self.MAX_CONCURRENT_ENROLLMENTS}
            )
        
        return EnrollmentSecurityResult(True, None, "Academic integrity passed", 0, {})
    
    def _validate_prerequisites(self) -> EnrollmentSecurityResult:
        """Layer 6: Prerequisites and dependencies validation"""
        if not self.course.prerequisites.exists():
            return EnrollmentSecurityResult(True, None, "No prerequisites required", 0, {})
        
        from .models import Enrollment
        
        # Get completed courses
        completed_courses = Enrollment.objects.filter(
            student=self.student,
            status='completed'
        ).values_list('course_id', flat=True)
        
        # Check missing prerequisites
        missing_prerequisites = self.course.prerequisites.exclude(id__in=completed_courses)
        
        if missing_prerequisites.exists():
            prereq_codes = list(missing_prerequisites.values_list('code', flat=True))
            return EnrollmentSecurityResult(
                False,
                EnrollmentViolationType.PREREQUISITE_BYPASS,
                f"Missing prerequisites: {', '.join(prereq_codes)}",
                70,
                {'missing_prerequisites': prereq_codes}
            )
        
        return EnrollmentSecurityResult(True, None, "Prerequisites satisfied", 0, {})
    
    def _validate_capacity_and_scheduling(self) -> EnrollmentSecurityResult:
        """Layer 7: Course capacity and scheduling validation"""
        if self.course.is_full:
            return EnrollmentSecurityResult(
                False,
                EnrollmentViolationType.CAPACITY_VIOLATION,
                "Course is at maximum capacity",
                60,
                {'enrolled_count': self.course.enrolled_students_count, 'max_capacity': self.course.max_students}
            )
        
        return EnrollmentSecurityResult(True, None, "Capacity validation passed", 0, {})
    
    def _is_valid_admin_override(self, admin_override: bool, override_type: str) -> bool:
        """Validate admin override permissions and logging"""
        if not admin_override:
            return False
        
        # Log admin override for audit
        logger.warning(f"Admin override used: {override_type} for student {self.student.id} in course {self.course.id}")
        
        return True
    
    def _create_security_result(self, allowed: bool, violation_type: Optional[EnrollmentViolationType], 
                              message: str, risk_score: int, audit_data: Dict) -> EnrollmentSecurityResult:
        """Create standardized security result with audit logging"""
        result = EnrollmentSecurityResult(allowed, violation_type, message, risk_score, audit_data)
        
        # Log security events
        if not allowed and risk_score >= 80:
            logger.warning(f"High-risk enrollment attempt blocked: {message}", extra=audit_data)
        elif not allowed:
            logger.info(f"Enrollment attempt blocked: {message}", extra=audit_data)
        
        return result
