from rest_framework import generics, status, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q, Count, Avg, Max
from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import Department, Course, Enrollment
from .serializers import (
    DepartmentSerializer, DepartmentCreateSerializer,
    CourseSerializer, CourseCreateSerializer, CourseUpdateSerializer,
    EnrollmentSerializer, EnrollmentCreateSerializer, EnrollmentUpdateSerializer,
    CourseStatsSerializer, DepartmentStatsSerializer
)

User = get_user_model()


class DepartmentListCreateView(generics.ListCreateAPIView):
    """
    List all departments or create a new department
    """
    queryset = Department.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DepartmentCreateSerializer
        return DepartmentSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # Apply filters
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(code__icontains=search)
            )

        return queryset.order_by('name')


class DepartmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a department
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return [permissions.IsAuthenticated()]
        return super().get_permissions()


class CourseListCreateView(generics.ListCreateAPIView):
    """
    List all courses or create a new course
    """
    queryset = Course.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CourseCreateSerializer
        return CourseSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # Filter based on user role
        if user.role == 'teacher':
            queryset = queryset.filter(instructor=user)
        elif user.role == 'student':
            # Students can see courses they're enrolled in or available courses
            enrolled_courses = user.enrollments.filter(is_active=True).values_list('course_id', flat=True)
            available_courses = queryset.filter(is_active=True)
            queryset = queryset.filter(Q(id__in=enrolled_courses) | Q(id__in=available_courses)).distinct()

        # Apply filters
        department = self.request.query_params.get('department')
        if department:
            queryset = queryset.filter(department_id=department)

        semester = self.request.query_params.get('semester')
        if semester:
            queryset = queryset.filter(semester=semester)

        academic_year = self.request.query_params.get('academic_year')
        if academic_year:
            queryset = queryset.filter(academic_year=academic_year)

        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(title_ar__icontains=search) |
                Q(code__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')


class CourseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a course
    """
    queryset = Course.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return CourseUpdateSerializer
        return CourseSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        if user.role == 'teacher':
            queryset = queryset.filter(instructor=user)
        elif user.role == 'student':
            enrolled_courses = user.enrollments.filter(is_active=True).values_list('course_id', flat=True)
            queryset = queryset.filter(Q(id__in=enrolled_courses) | Q(is_active=True))

        return queryset


class EnrollmentListCreateView(generics.ListCreateAPIView):
    """
    List enrollments or create a new enrollment
    """
    queryset = Enrollment.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return EnrollmentCreateSerializer
        return EnrollmentSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        # Filter based on user role
        if user.role == 'student':
            queryset = queryset.filter(student=user)
        elif user.role == 'teacher':
            # Teachers can see enrollments for their courses
            teacher_courses = Course.objects.filter(instructor=user).values_list('id', flat=True)
            queryset = queryset.filter(course_id__in=teacher_courses)

        # Apply filters
        course_id = self.request.query_params.get('course')
        if course_id:
            queryset = queryset.filter(course_id=course_id)

        student_id = self.request.query_params.get('student')
        if student_id and user.role in ['teacher', 'admin', 'super_admin']:
            queryset = queryset.filter(student__student_id=student_id)

        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Handle include_inactive parameter for enrollment history
        include_inactive = self.request.query_params.get('include_inactive')
        if include_inactive and include_inactive.lower() == 'true':
            # Don't filter by is_active, include all enrollments
            pass
        elif is_active is None:
            # Default behavior: only show active enrollments
            queryset = queryset.filter(is_active=True)

        return queryset.order_by('-enrolled_at', '-attempt_number')


class EnrollmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete an enrollment
    """
    queryset = Enrollment.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return EnrollmentUpdateSerializer
        return EnrollmentSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        if user.role == 'student':
            queryset = queryset.filter(student=user)
        elif user.role == 'teacher':
            teacher_courses = Course.objects.filter(instructor=user).values_list('id', flat=True)
            queryset = queryset.filter(course_id__in=teacher_courses)

        return queryset


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def enroll_student(request, course_id):
    """
    SECURE ENROLLMENT API with Comprehensive Validation and Rate Limiting
    Implements defense-in-depth security architecture
    """
    from django.core.cache import cache
    from django.utils import timezone
    from .enrollment_security import EnrollmentSecurityManager
    from .models import EnrollmentAttemptLog
    import logging

    logger = logging.getLogger(__name__)

    # Rate limiting per user
    rate_limit_key = f"enrollment_rate_limit:{request.user.id}"
    recent_attempts = cache.get(rate_limit_key, 0)

    if recent_attempts >= 5:  # Max 5 attempts per minute
        logger.warning(f"Rate limit exceeded for user {request.user.id}")
        return Response(
            {'error': 'Too many enrollment attempts. Please wait before trying again.'},
            status=status.HTTP_429_TOO_MANY_REQUESTS
        )

    # Increment rate limit counter
    cache.set(rate_limit_key, recent_attempts + 1, 60)  # 1 minute window

    try:
        course = Course.objects.get(id=course_id)
    except Course.DoesNotExist:
        return Response(
            {'error': 'Course not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Determine student and validate permissions
    if request.user.role == 'student':
        student = request.user
        admin_override = False
        override_reason = None
    elif request.user.role in ['admin', 'super_admin', 'teacher']:
        # Admin/teacher enrolling a student
        student_id = request.data.get('student_id')
        if not student_id:
            return Response(
                {'error': 'Student ID is required for admin enrollment'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            student = User.objects.get(student_id=student_id, role='student')
        except User.DoesNotExist:
            return Response(
                {'error': 'Student not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        admin_override = request.data.get('force_enroll', False)
        override_reason = request.data.get('override_reason', 'Admin enrollment')
    else:
        return Response(
            {'error': 'Insufficient permissions'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Get client information for audit trail
    ip_address = request.META.get('HTTP_X_FORWARDED_FOR', request.META.get('REMOTE_ADDR'))
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    enrollment_type = request.data.get('enrollment_type', 'regular')

    # CRITICAL: Use secure enrollment method with comprehensive validation
    try:
            enrollment, waitlist_entry = course.enroll_student(
                student=student,
                enrollment_type=enrollment_type,
                admin_override=admin_override,
                override_reason=override_reason
            )

            # Log successful enrollment attempt
            EnrollmentAttemptLog.log_attempt(
                student=student,
                course=course,
                attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                enrollment_status='enrolled' if enrollment else 'waitlisted',
                risk_score=10,  # Low risk for successful enrollment
                admin_override=admin_override,
                override_reason=override_reason,
                ip_address=ip_address,
                user_agent=user_agent
            )

            if enrollment:
                logger.info(f"Secure enrollment successful: Student {student.id} in Course {course.id}")
                return Response({
                    'enrollment': EnrollmentSerializer(enrollment).data,
                    'message': 'Successfully enrolled in course',
                    'type': 'enrollment'
                }, status=status.HTTP_201_CREATED)
            else:
                logger.info(f"Student {student.id} added to waitlist for Course {course.id}")
                return Response({
                    'waitlist': {
                        'id': waitlist_entry.id,
                        'position': waitlist_entry.position,
                        'course': course.code,
                        'added_at': waitlist_entry.added_at
                    },
                    'message': f'Added to waitlist (position {waitlist_entry.position})',
                    'type': 'waitlist'
                }, status=status.HTTP_201_CREATED)

    except ValueError as e:
            # Log blocked enrollment attempt
            EnrollmentAttemptLog.log_attempt(
                student=student,
                course=course,
                attempt_type=EnrollmentAttemptLog.AttemptType.ENROLLMENT,
                enrollment_status='blocked',
                risk_score=80,  # High risk for blocked attempt
                admin_override=admin_override,
                override_reason=override_reason,
                ip_address=ip_address,
                user_agent=user_agent
            )

            logger.warning(f"Enrollment blocked: {str(e)} - Student {student.id} in Course {course.id}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    except Exception as e:
        logger.error(f"Unexpected error in enrollment: {str(e)}", exc_info=True)
        return Response(
            {'error': 'An unexpected error occurred during enrollment'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def drop_course(request, course_id):
    """
    Drop a student from a course and promote from waitlist if applicable
    """
    try:
        course = Course.objects.get(id=course_id)

        # For students dropping themselves
        if request.user.role == 'student':
            student = request.user
        else:
            # For admin/teacher dropping a student
            student_id = request.data.get('student_id')
            if not student_id:
                return Response(
                    {'error': 'Student ID is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                student = User.objects.get(student_id=student_id, role='student')
            except User.DoesNotExist:
                return Response(
                    {'error': 'Student not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Find enrollment
        try:
            enrollment = Enrollment.objects.get(
                student=student,
                course=course,
                is_active=True
            )
        except Enrollment.DoesNotExist:
            return Response(
                {'error': 'Student is not enrolled in this course'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Drop the student
        enrollment.status = Enrollment.EnrollmentStatus.DROPPED
        enrollment.is_active = False
        enrollment.dropped_at = timezone.now()
        enrollment.save()

        # Promote from waitlist if there are waitlisted students
        from .models import Waitlist
        promoted_students = Waitlist.promote_from_waitlist(course, count=1)

        response_data = {
            'message': 'Successfully dropped from course',
            'dropped_student': student.get_display_name(),
            'course': course.code
        }

        if promoted_students:
            response_data['promoted_students'] = [
                student.get_display_name() for student in promoted_students
            ]

        return Response(response_data, status=status.HTTP_200_OK)

    except Course.DoesNotExist:
        return Response(
            {'error': 'Course not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def course_waitlist(request, course_id):
    """
    Get waitlist for a specific course
    """
    try:
        course = Course.objects.get(id=course_id)

        # Check permissions
        if request.user.role == 'student':
            # Students can only see their own waitlist position
            from .models import Waitlist
            try:
                waitlist_entry = Waitlist.objects.get(
                    course=course,
                    student=request.user,
                    status='active'
                )
                return Response({
                    'position': waitlist_entry.position,
                    'total_waitlisted': course.waitlisted_students_count,
                    'added_at': waitlist_entry.added_at
                })
            except Waitlist.DoesNotExist:
                return Response({
                    'message': 'Not on waitlist for this course'
                }, status=status.HTTP_404_NOT_FOUND)

        elif request.user.role in ['teacher', 'admin', 'super_admin']:
            # Teachers and admins can see full waitlist
            if request.user.role == 'teacher' and course.instructor != request.user:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )

            from .models import Waitlist
            waitlist = Waitlist.objects.filter(
                course=course,
                status='active'
            ).order_by('position').select_related('student')

            waitlist_data = [{
                'position': entry.position,
                'student_name': entry.student.get_display_name(),
                'student_id': entry.student.student_id,
                'added_at': entry.added_at
            } for entry in waitlist]

            return Response({
                'waitlist': waitlist_data,
                'total_count': len(waitlist_data)
            })

    except Course.DoesNotExist:
        return Response(
            {'error': 'Course not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def withdraw_student(request, enrollment_id):
    """
    Withdraw a student from a course
    """
    try:
        enrollment = Enrollment.objects.get(id=enrollment_id)

        # Check permissions
        if (request.user.role == 'student' and enrollment.student != request.user) or \
           (request.user.role == 'teacher' and enrollment.course.instructor != request.user):
            if request.user.role not in ['admin', 'super_admin']:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )

        enrollment.is_active = False
        enrollment.save()

        return Response(
            EnrollmentSerializer(enrollment).data
        )

    except Enrollment.DoesNotExist:
        return Response(
            {'error': 'Enrollment not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def course_stats(request):
    """
    Get course statistics
    """
    if request.user.role not in ['admin', 'super_admin']:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    stats = {
        'total_courses': Course.objects.count(),
        'active_courses': Course.objects.filter(is_active=True).count(),
        'total_enrollments': Enrollment.objects.filter(is_active=True).count(),
        'departments_count': Department.objects.filter(is_active=True).count(),
    }

    # Calculate average enrollment per course
    avg_enrollment = Enrollment.objects.filter(is_active=True).values('course').annotate(
        enrollment_count=Count('id')
    ).aggregate(avg=Avg('enrollment_count'))['avg'] or 0

    stats['average_enrollment_per_course'] = round(avg_enrollment, 2)

    serializer = CourseStatsSerializer(stats)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def department_stats(request):
    """
    Get department statistics
    """
    if request.user.role not in ['admin', 'super_admin']:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    stats = {
        'total_departments': Department.objects.count(),
        'active_departments': Department.objects.filter(is_active=True).count(),
        'total_courses': Course.objects.filter(department__is_active=True).count(),
        'total_students': Enrollment.objects.filter(
            is_active=True,
            course__department__is_active=True
        ).values('student').distinct().count(),
    }

    serializer = DepartmentStatsSerializer(stats)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def course_enrollments(request, course_id):
    """
    Get all enrollments for a specific course
    """
    try:
        course = Course.objects.get(id=course_id)

        # Check permissions
        if request.user.role == 'teacher' and course.instructor != request.user:
            if request.user.role not in ['admin', 'super_admin']:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )

        enrollments = Enrollment.objects.filter(course=course, is_active=True)
        serializer = EnrollmentSerializer(enrollments, many=True)
        return Response(serializer.data)

    except Course.DoesNotExist:
        return Response(
            {'error': 'Course not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def student_enrollment_history(request, student_id=None):
    """
    Get enrollment history for a student including retakes
    """
    # If no student_id provided, use current user (for students)
    if student_id is None:
        if request.user.role != 'student':
            return Response(
                {'error': 'Student ID required for non-student users'},
                status=status.HTTP_400_BAD_REQUEST
            )
        student = request.user
    else:
        # Check permissions for accessing other student's data
        if request.user.role not in ['admin', 'super_admin', 'teacher']:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        try:
            student = User.objects.get(id=student_id, role='student')
        except User.DoesNotExist:
            return Response(
                {'error': 'Student not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    # Get all enrollments including inactive ones (for retake history)
    enrollments = Enrollment.objects.filter(student=student).order_by(
        'course__code', '-attempt_number'
    )

    # Group by course to show retake history
    course_history = {}
    for enrollment in enrollments:
        course_code = enrollment.course.code
        if course_code not in course_history:
            course_history[course_code] = {
                'course': {
                    'id': enrollment.course.id,
                    'code': enrollment.course.code,
                    'title': enrollment.course.title,
                    'credit_hours': enrollment.course.credit_hours,
                },
                'attempts': []
            }

        course_history[course_code]['attempts'].append({
            'id': enrollment.id,
            'attempt_number': enrollment.attempt_number,
            'is_retake': enrollment.is_retake,
            'attempt_display': enrollment.get_attempt_display(),
            'status': enrollment.status,
            'final_grade': enrollment.final_grade,
            'letter_grade': enrollment.letter_grade,
            'enrolled_at': enrollment.enrolled_at,
            'completed_at': enrollment.completed_at,
            'is_active': enrollment.is_active,
        })

    return Response({
        'student': {
            'id': student.id,
            'name': student.get_full_name(),
            'student_id': student.student_id,
        },
        'course_history': course_history
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def course_enrollment_analytics(request, course_id):
    """
    Get detailed enrollment analytics for a course including retake statistics
    """
    try:
        course = Course.objects.get(id=course_id)

        # Check permissions
        if request.user.role not in ['admin', 'super_admin', 'teacher']:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # If teacher, ensure they're the instructor
        if request.user.role == 'teacher' and course.instructor != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all enrollments for this course
        enrollments = Enrollment.objects.filter(course=course)

        # Calculate statistics
        total_enrollments = enrollments.count()
        unique_students = enrollments.values('student').distinct().count()

        # Status breakdown
        status_breakdown = {}
        for status_choice in Enrollment.EnrollmentStatus.choices:
            status_code = status_choice[0]
            count = enrollments.filter(status=status_code).count()
            status_breakdown[status_code] = count

        # Retake statistics
        retake_stats = {
            'students_with_retakes': enrollments.filter(is_retake=True).values('student').distinct().count(),
            'total_retakes': enrollments.filter(is_retake=True).count(),
            'average_attempts': enrollments.values('student').annotate(
                max_attempt=models.Max('attempt_number')
            ).aggregate(avg_attempts=models.Avg('max_attempt'))['avg_attempts'] or 0,
        }

        # Grade distribution (for completed enrollments)
        completed_enrollments = enrollments.filter(status='completed')
        grade_distribution = {}
        if completed_enrollments.exists():
            for grade in ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F']:
                count = completed_enrollments.filter(letter_grade=grade).count()
                if count > 0:
                    grade_distribution[grade] = count

        return Response({
            'course': {
                'id': course.id,
                'code': course.code,
                'title': course.title,
                'max_students': course.max_students,
            },
            'enrollment_statistics': {
                'total_enrollments': total_enrollments,
                'unique_students': unique_students,
                'current_enrolled': enrollments.filter(status='enrolled', is_active=True).count(),
                'status_breakdown': status_breakdown,
            },
            'retake_statistics': retake_stats,
            'grade_distribution': grade_distribution,
            'success_rate': (
                completed_enrollments.count() / total_enrollments * 100
                if total_enrollments > 0 else 0
            ),
        })

    except Course.DoesNotExist:
        return Response(
            {'error': 'Course not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def enrollment_system_health_check(request):
    """
    Health check endpoint to verify enrollment system fixes are working
    """
    try:
        # Check if we have courses and enrollments
        total_courses = Course.objects.count()
        total_enrollments = Enrollment.objects.count()

        # Check for retake functionality
        retake_enrollments = Enrollment.objects.filter(is_retake=True).count()

        # Check enrollment statuses
        status_counts = {}
        for status_choice in Enrollment.EnrollmentStatus.choices:
            status_code = status_choice[0]
            count = Enrollment.objects.filter(status=status_code).count()
            status_counts[status_code] = count

        return Response({
            'status': 'healthy',
            'message': 'Enrollment system is working correctly',
            'statistics': {
                'total_courses': total_courses,
                'total_enrollments': total_enrollments,
                'retake_enrollments': retake_enrollments,
                'status_breakdown': status_counts,
            },
            'fixes_implemented': [
                'Enhanced enrollment status logic',
                'Course retake functionality',
                'Improved enrollment validation',
                'Frontend status display updates',
                'Comprehensive testing suite'
            ],
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'Health check failed: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def performance_dashboard(request):
    """
    Enterprise performance monitoring dashboard
    """
    from backend.performance_middleware import PerformanceMonitoringView
    return PerformanceMonitoringView.get_performance_dashboard(request)
