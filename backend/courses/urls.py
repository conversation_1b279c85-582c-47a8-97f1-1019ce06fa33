from django.urls import path
from . import views

urlpatterns = [
    # Departments
    path('departments/', views.DepartmentListCreateView.as_view(), name='department-list-create'),
    path('departments/<int:pk>/', views.DepartmentDetailView.as_view(), name='department-detail'),
    path('departments/stats/', views.department_stats, name='department-stats'),
    
    # Courses
    path('', views.CourseListCreateView.as_view(), name='course-list-create'),
    path('<int:pk>/', views.CourseDetailView.as_view(), name='course-detail'),
    path('<int:course_id>/enroll/', views.enroll_student, name='enroll-student'),
    path('<int:course_id>/drop/', views.drop_course, name='drop-course'),
    path('<int:course_id>/waitlist/', views.course_waitlist, name='course-waitlist'),
    path('<int:course_id>/enrollments/', views.course_enrollments, name='course-enrollments'),
    path('<int:course_id>/analytics/', views.course_enrollment_analytics, name='course-enrollment-analytics'),
    path('stats/', views.course_stats, name='course-stats'),
    path('health/', views.enrollment_system_health_check, name='enrollment-health-check'),
    
    # Enrollments
    path('enrollments/', views.EnrollmentListCreateView.as_view(), name='enrollment-list-create'),
    path('enrollments/<int:pk>/', views.EnrollmentDetailView.as_view(), name='enrollment-detail'),
    path('enrollments/<int:enrollment_id>/withdraw/', views.withdraw_student, name='withdraw-student'),
    path('enrollments/history/', views.student_enrollment_history, name='student-enrollment-history'),
    path('enrollments/history/<int:student_id>/', views.student_enrollment_history, name='student-enrollment-history-detail'),
]
