"""
INTELLIGENT CACHING STRATEGIES FOR <PERSON>NR<PERSON>LMENT SYSTEM
Multi-layer caching with smart invalidation and performance optimization
"""

from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key
from django.utils import timezone
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from functools import wraps
import hashlib
import json
import logging
from typing import Any, Dict, List, Optional, Callable

logger = logging.getLogger(__name__)


class CacheKeyGenerator:
    """Generate consistent cache keys for different data types"""
    
    @staticmethod
    def student_enrollment_key(student_id: int) -> str:
        return f"enrollment:student:{student_id}"
    
    @staticmethod
    def course_details_key(course_id: int) -> str:
        return f"course:details:{course_id}"
    
    @staticmethod
    def course_enrollment_stats_key(course_id: int) -> str:
        return f"course:stats:{course_id}"
    
    @staticmethod
    def student_dashboard_key(student_id: int) -> str:
        return f"dashboard:student:{student_id}"
    
    @staticmethod
    def department_courses_key(department_id: int) -> str:
        return f"department:courses:{department_id}"
    
    @staticmethod
    def enrollment_validation_key(student_id: int, course_id: int) -> str:
        return f"validation:enrollment:{student_id}:{course_id}"
    
    @staticmethod
    def user_permissions_key(user_id: int) -> str:
        return f"permissions:user:{user_id}"
    
    @staticmethod
    def course_capacity_key(course_id: int) -> str:
        return f"capacity:course:{course_id}"
    
    @staticmethod
    def security_rate_limit_key(user_id: int, action: str) -> str:
        return f"rate_limit:{action}:{user_id}"


class CacheTimeouts:
    """Centralized cache timeout configuration"""
    
    # Short-term caches (frequently changing data)
    ENROLLMENT_STATUS = 300          # 5 minutes
    COURSE_CAPACITY = 180           # 3 minutes
    USER_PERMISSIONS = 600          # 10 minutes
    
    # Medium-term caches (moderately changing data)
    COURSE_DETAILS = 1800           # 30 minutes
    STUDENT_DASHBOARD = 900         # 15 minutes
    ENROLLMENT_STATS = 1200         # 20 minutes
    
    # Long-term caches (rarely changing data)
    DEPARTMENT_COURSES = 3600       # 1 hour
    COURSE_PREREQUISITES = 7200     # 2 hours
    ACADEMIC_CALENDAR = 86400       # 24 hours
    
    # Security and rate limiting
    RATE_LIMIT_WINDOW = 300         # 5 minutes
    SECURITY_VALIDATION = 60        # 1 minute


def cache_result(timeout: int, key_func: Callable = None, version: int = 1):
    """Decorator for caching function results with intelligent key generation"""
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Generate key from function name and arguments
                key_data = f"{func.__name__}:{str(args)}:{str(sorted(kwargs.items()))}"
                cache_key = hashlib.md5(key_data.encode()).hexdigest()
            
            # Try to get from cache
            cached_result = cache.get(cache_key, version=version)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout, version=version)
            logger.debug(f"Cache set for key: {cache_key}")
            
            return result
        
        return wrapper
    return decorator


class EnrollmentCacheManager:
    """Intelligent cache management for enrollment operations"""
    
    @staticmethod
    @cache_result(
        timeout=CacheTimeouts.ENROLLMENT_STATUS,
        key_func=lambda student_id: CacheKeyGenerator.student_enrollment_key(student_id)
    )
    def get_student_enrollments(student_id: int):
        """Get student enrollments with caching"""
        from .models import Enrollment
        
        enrollments = Enrollment.objects.filter(
            student_id=student_id,
            is_active=True
        ).select_related('course', 'course__department').values(
            'id', 'course__id', 'course__code', 'course__title',
            'course__credit_hours', 'status', 'enrollment_date'
        )
        
        return list(enrollments)
    
    @staticmethod
    @cache_result(
        timeout=CacheTimeouts.COURSE_DETAILS,
        key_func=lambda course_id: CacheKeyGenerator.course_details_key(course_id)
    )
    def get_course_details(course_id: int):
        """Get course details with caching"""
        from .models import Course
        
        try:
            course = Course.objects.select_related(
                'department', 'instructor'
            ).get(id=course_id)
            
            return {
                'id': course.id,
                'code': course.code,
                'title': course.title,
                'description': course.description,
                'credit_hours': course.credit_hours,
                'max_students': course.max_students,
                'department': course.department.name,
                'instructor': course.instructor.get_full_name(),
                'is_active': course.is_active,
                'is_published': course.is_published
            }
        except Course.DoesNotExist:
            return None
    
    @staticmethod
    @cache_result(
        timeout=CacheTimeouts.COURSE_CAPACITY,
        key_func=lambda course_id: CacheKeyGenerator.course_capacity_key(course_id)
    )
    def get_course_capacity_info(course_id: int):
        """Get course capacity information with caching"""
        from .models import Course, Enrollment
        
        try:
            course = Course.objects.get(id=course_id)
            enrolled_count = Enrollment.objects.filter(
                course_id=course_id,
                status='enrolled',
                is_active=True
            ).count()
            
            return {
                'max_students': course.max_students,
                'enrolled_count': enrolled_count,
                'available_spots': course.max_students - enrolled_count,
                'is_full': enrolled_count >= course.max_students,
                'utilization_percent': (enrolled_count / course.max_students * 100) if course.max_students > 0 else 0
            }
        except Course.DoesNotExist:
            return None
    
    @staticmethod
    def invalidate_student_cache(student_id: int):
        """Invalidate all cache entries for a student"""
        keys_to_invalidate = [
            CacheKeyGenerator.student_enrollment_key(student_id),
            CacheKeyGenerator.student_dashboard_key(student_id),
            CacheKeyGenerator.user_permissions_key(student_id)
        ]
        
        cache.delete_many(keys_to_invalidate)
        logger.info(f"Invalidated cache for student {student_id}")
    
    @staticmethod
    def invalidate_course_cache(course_id: int):
        """Invalidate all cache entries for a course"""
        keys_to_invalidate = [
            CacheKeyGenerator.course_details_key(course_id),
            CacheKeyGenerator.course_enrollment_stats_key(course_id),
            CacheKeyGenerator.course_capacity_key(course_id)
        ]
        
        cache.delete_many(keys_to_invalidate)
        logger.info(f"Invalidated cache for course {course_id}")


class SecurityCacheManager:
    """Cache management for security and validation operations"""
    
    @staticmethod
    def check_rate_limit(user_id: int, action: str, limit: int = 5) -> bool:
        """Check rate limit with caching"""
        cache_key = CacheKeyGenerator.security_rate_limit_key(user_id, action)
        
        current_count = cache.get(cache_key, 0)
        if current_count >= limit:
            return False
        
        # Increment counter
        cache.set(cache_key, current_count + 1, CacheTimeouts.RATE_LIMIT_WINDOW)
        return True
    
    @staticmethod
    @cache_result(
        timeout=CacheTimeouts.SECURITY_VALIDATION,
        key_func=lambda student_id, course_id: CacheKeyGenerator.enrollment_validation_key(student_id, course_id)
    )
    def get_enrollment_validation_cache(student_id: int, course_id: int):
        """Cache enrollment validation results for short periods"""
        from .enrollment_security import EnrollmentSecurityManager
        
        security_manager = EnrollmentSecurityManager(
            student_id=student_id, 
            course_id=course_id
        )
        
        # Only cache basic validation, not full security check
        basic_result = security_manager._validate_basic_eligibility()
        
        return {
            'allowed': basic_result.allowed,
            'message': basic_result.message,
            'cached_at': timezone.now().isoformat()
        }


class PerformanceCacheManager:
    """High-performance caching for frequently accessed data"""
    
    @staticmethod
    def warm_popular_courses_cache(course_ids: List[int]):
        """Pre-warm cache for popular courses"""
        logger.info(f"Warming cache for {len(course_ids)} popular courses")
        
        for course_id in course_ids:
            # Warm course details
            EnrollmentCacheManager.get_course_details(course_id)
            # Warm capacity info
            EnrollmentCacheManager.get_course_capacity_info(course_id)
    
    @staticmethod
    def get_cache_statistics() -> Dict[str, Any]:
        """Get cache performance statistics"""
        # This would need to be implemented based on your cache backend
        # For Redis, you could use INFO commands
        # For Memcached, you could use stats commands
        
        return {
            'cache_backend': cache.__class__.__name__,
            'timestamp': timezone.now().isoformat(),
            'note': 'Detailed stats depend on cache backend implementation'
        }
    
    @staticmethod
    def clear_expired_cache_entries():
        """Clear expired cache entries (if supported by backend)"""
        try:
            # This is backend-specific
            if hasattr(cache, 'clear'):
                # Be careful with this in production
                logger.warning("Clearing all cache entries")
                cache.clear()
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")


# Signal handlers for automatic cache invalidation
@receiver(post_save, sender='courses.Enrollment')
def invalidate_enrollment_cache(sender, instance, **kwargs):
    """Automatically invalidate cache when enrollment changes"""
    EnrollmentCacheManager.invalidate_student_cache(instance.student_id)
    EnrollmentCacheManager.invalidate_course_cache(instance.course_id)


@receiver(post_delete, sender='courses.Enrollment')
def invalidate_enrollment_cache_on_delete(sender, instance, **kwargs):
    """Automatically invalidate cache when enrollment is deleted"""
    EnrollmentCacheManager.invalidate_student_cache(instance.student_id)
    EnrollmentCacheManager.invalidate_course_cache(instance.course_id)


@receiver(post_save, sender='courses.Course')
def invalidate_course_cache_on_update(sender, instance, **kwargs):
    """Automatically invalidate cache when course is updated"""
    EnrollmentCacheManager.invalidate_course_cache(instance.id)
    
    # Also invalidate department cache
    dept_key = CacheKeyGenerator.department_courses_key(instance.department_id)
    cache.delete(dept_key)


class CacheHealthMonitor:
    """Monitor cache health and performance"""
    
    @staticmethod
    def check_cache_health() -> Dict[str, Any]:
        """Check cache system health"""
        try:
            # Test cache write/read
            test_key = "health_check_test"
            test_value = {"timestamp": timezone.now().isoformat()}
            
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            
            cache.delete(test_key)
            
            is_healthy = retrieved_value == test_value
            
            return {
                'status': 'healthy' if is_healthy else 'unhealthy',
                'cache_backend': cache.__class__.__name__,
                'test_successful': is_healthy,
                'timestamp': timezone.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': timezone.now().isoformat()
            }
    
    @staticmethod
    def get_cache_usage_report() -> Dict[str, Any]:
        """Generate cache usage report"""
        return {
            'cache_timeouts': {
                'enrollment_status': CacheTimeouts.ENROLLMENT_STATUS,
                'course_details': CacheTimeouts.COURSE_DETAILS,
                'student_dashboard': CacheTimeouts.STUDENT_DASHBOARD,
                'security_validation': CacheTimeouts.SECURITY_VALIDATION
            },
            'cache_keys_pattern': {
                'student_enrollments': 'enrollment:student:{id}',
                'course_details': 'course:details:{id}',
                'course_capacity': 'capacity:course:{id}',
                'rate_limits': 'rate_limit:{action}:{user_id}'
            },
            'recommendations': [
                'Monitor cache hit rates for optimization',
                'Adjust timeouts based on data change frequency',
                'Use cache warming for popular courses',
                'Implement cache versioning for schema changes'
            ],
            'timestamp': timezone.now().isoformat()
        }
