"""
DATABASE PERFORMANCE OPTIMIZATIONS FOR ENROLLMENT SYSTEM
Strategic indexes, query optimization, and performance enhancements
"""

from django.db import models, connection
from django.core.cache import cache
from django.utils import timezone
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)


class OptimizedEnrollmentQuerySet(models.QuerySet):
    """Optimized queryset for enrollment operations"""
    
    def with_student_and_course(self):
        """Optimize enrollment queries with student and course data"""
        return self.select_related('student', 'course', 'course__department')
    
    def active_enrollments(self):
        """Get only active enrollments with optimized query"""
        return self.filter(is_active=True).with_student_and_course()
    
    def for_student_courses(self, student_id):
        """Optimized query for student's enrolled courses"""
        return self.filter(
            student_id=student_id,
            is_active=True
        ).select_related('course', 'course__department', 'course__instructor')
    
    def course_enrollment_stats(self, course_id):
        """Optimized query for course enrollment statistics"""
        return self.filter(course_id=course_id).aggregate(
            total_enrolled=models.Count('id', filter=models.Q(status='enrolled')),
            total_completed=models.Count('id', filter=models.Q(status='completed')),
            total_failed=models.Count('id', filter=models.Q(status='failed')),
            total_dropped=models.Count('id', filter=models.Q(status='dropped'))
        )
    
    def bulk_enrollment_check(self, student_ids, course_id):
        """Bulk check enrollment status for multiple students"""
        return self.filter(
            student_id__in=student_ids,
            course_id=course_id,
            is_active=True
        ).values('student_id', 'status')


class OptimizedCourseQuerySet(models.QuerySet):
    """Optimized queryset for course operations"""
    
    def with_enrollment_counts(self):
        """Annotate courses with enrollment counts"""
        return self.annotate(
            enrolled_count=models.Count(
                'enrollments',
                filter=models.Q(enrollments__status='enrolled', enrollments__is_active=True)
            ),
            waitlisted_count=models.Count(
                'waitlist_entries',
                filter=models.Q(waitlist_entries__is_active=True)
            )
        )
    
    def available_for_enrollment(self):
        """Get courses available for enrollment with optimized query"""
        return self.filter(
            is_active=True,
            is_published=True
        ).select_related('department', 'instructor').with_enrollment_counts()
    
    def by_department_with_stats(self, department_id):
        """Get courses by department with enrollment statistics"""
        return self.filter(
            department_id=department_id
        ).with_enrollment_counts().select_related('instructor')


class PerformanceOptimizedManager:
    """Manager for performance-optimized enrollment operations"""
    
    @staticmethod
    def get_student_enrollment_summary(student_id, use_cache=True):
        """Get comprehensive enrollment summary for a student with caching"""
        cache_key = f"student_enrollment_summary:{student_id}"
        
        if use_cache:
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
        
        from .models import Enrollment
        
        # Single optimized query for all enrollment data
        enrollments = Enrollment.objects.filter(
            student_id=student_id
        ).select_related(
            'course', 'course__department'
        ).values(
            'course__id', 'course__code', 'course__title',
            'course__credit_hours', 'status', 'grade',
            'enrollment_date', 'completion_date'
        )
        
        # Aggregate statistics
        stats = Enrollment.objects.filter(student_id=student_id).aggregate(
            total_courses=models.Count('id'),
            completed_courses=models.Count('id', filter=models.Q(status='completed')),
            current_enrollments=models.Count('id', filter=models.Q(status='enrolled', is_active=True)),
            total_credits=models.Sum('course__credit_hours', filter=models.Q(status='completed'))
        )
        
        result = {
            'enrollments': list(enrollments),
            'statistics': stats,
            'last_updated': timezone.now().isoformat()
        }
        
        if use_cache:
            cache.set(cache_key, result, 300)  # Cache for 5 minutes
        
        return result
    
    @staticmethod
    def get_course_enrollment_details(course_id, use_cache=True):
        """Get detailed enrollment information for a course with caching"""
        cache_key = f"course_enrollment_details:{course_id}"
        
        if use_cache:
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
        
        from .models import Enrollment, Course
        
        try:
            course = Course.objects.select_related('department', 'instructor').get(id=course_id)
        except Course.DoesNotExist:
            return None
        
        # Optimized enrollment queries
        enrollment_stats = Enrollment.objects.filter(course_id=course_id).aggregate(
            total_enrolled=models.Count('id', filter=models.Q(status='enrolled', is_active=True)),
            total_completed=models.Count('id', filter=models.Q(status='completed')),
            total_failed=models.Count('id', filter=models.Q(status='failed')),
            total_dropped=models.Count('id', filter=models.Q(status='dropped')),
            avg_grade=models.Avg('grade', filter=models.Q(status='completed'))
        )
        
        # Recent enrollments
        recent_enrollments = Enrollment.objects.filter(
            course_id=course_id,
            enrollment_date__gte=timezone.now() - timezone.timedelta(days=30)
        ).select_related('student').values(
            'student__username', 'student__email', 'status', 'enrollment_date'
        )[:20]
        
        result = {
            'course': {
                'id': course.id,
                'code': course.code,
                'title': course.title,
                'max_students': course.max_students,
                'department': course.department.name,
                'instructor': course.instructor.get_full_name()
            },
            'statistics': enrollment_stats,
            'recent_enrollments': list(recent_enrollments),
            'capacity_utilization': (enrollment_stats['total_enrolled'] / course.max_students * 100) if course.max_students > 0 else 0,
            'last_updated': timezone.now().isoformat()
        }
        
        if use_cache:
            cache.set(cache_key, result, 600)  # Cache for 10 minutes
        
        return result
    
    @staticmethod
    def bulk_enrollment_validation(student_course_pairs):
        """Efficiently validate multiple enrollment requests"""
        from .models import Enrollment
        
        # Extract student and course IDs
        student_ids = [pair[0] for pair in student_course_pairs]
        course_ids = [pair[1] for pair in student_course_pairs]
        
        # Single query to get existing enrollments
        existing_enrollments = Enrollment.objects.filter(
            student_id__in=student_ids,
            course_id__in=course_ids,
            is_active=True
        ).values('student_id', 'course_id', 'status')
        
        # Create lookup dictionary
        enrollment_lookup = {
            (e['student_id'], e['course_id']): e['status']
            for e in existing_enrollments
        }
        
        # Validate each pair
        results = []
        for student_id, course_id in student_course_pairs:
            existing_status = enrollment_lookup.get((student_id, course_id))
            
            if existing_status == 'completed':
                results.append({
                    'student_id': student_id,
                    'course_id': course_id,
                    'can_enroll': False,
                    'reason': 'Course already completed'
                })
            elif existing_status in ['enrolled', 'waitlisted']:
                results.append({
                    'student_id': student_id,
                    'course_id': course_id,
                    'can_enroll': False,
                    'reason': f'Already {existing_status}'
                })
            else:
                results.append({
                    'student_id': student_id,
                    'course_id': course_id,
                    'can_enroll': True,
                    'reason': 'Eligible for enrollment'
                })
        
        return results
    
    @staticmethod
    def optimize_database_queries():
        """Run database optimization queries"""
        with connection.cursor() as cursor:
            # Analyze tables for query optimization
            cursor.execute("ANALYZE;")
            
            # Update table statistics (PostgreSQL)
            try:
                cursor.execute("UPDATE pg_stat_user_tables SET n_tup_ins = 0, n_tup_upd = 0, n_tup_del = 0;")
            except:
                pass  # Skip for non-PostgreSQL databases
        
        logger.info("Database optimization completed")
    
    @staticmethod
    def get_performance_metrics():
        """Get database performance metrics"""
        with connection.cursor() as cursor:
            # Get query performance data
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_queries,
                    AVG(CASE WHEN query LIKE 'SELECT%' THEN 1 ELSE 0 END) as select_ratio
                FROM django_admin_log 
                WHERE action_time >= NOW() - INTERVAL '1 hour'
            """)
            
            try:
                result = cursor.fetchone()
                return {
                    'total_queries': result[0] if result else 0,
                    'select_ratio': result[1] if result else 0,
                    'timestamp': timezone.now().isoformat()
                }
            except:
                return {
                    'total_queries': 0,
                    'select_ratio': 0,
                    'timestamp': timezone.now().isoformat()
                }


class CacheManager:
    """Intelligent caching for enrollment operations"""
    
    CACHE_TIMEOUTS = {
        'student_enrollment': 300,      # 5 minutes
        'course_details': 600,          # 10 minutes
        'enrollment_stats': 900,        # 15 minutes
        'department_courses': 1800,     # 30 minutes
    }
    
    @classmethod
    def invalidate_student_cache(cls, student_id):
        """Invalidate all cache entries for a student"""
        patterns = [
            f"student_enrollment_summary:{student_id}",
            f"student_courses:{student_id}",
            f"student_dashboard:{student_id}"
        ]
        
        for pattern in patterns:
            cache.delete(pattern)
    
    @classmethod
    def invalidate_course_cache(cls, course_id):
        """Invalidate all cache entries for a course"""
        patterns = [
            f"course_enrollment_details:{course_id}",
            f"course_stats:{course_id}",
            f"course_availability:{course_id}"
        ]
        
        for pattern in patterns:
            cache.delete(pattern)
    
    @classmethod
    def warm_cache_for_popular_courses(cls, course_ids):
        """Pre-warm cache for popular courses"""
        for course_id in course_ids:
            PerformanceOptimizedManager.get_course_enrollment_details(course_id, use_cache=True)
    
    @classmethod
    def get_cache_statistics(cls):
        """Get cache performance statistics"""
        # This would need to be implemented based on your cache backend
        return {
            'cache_backend': cache.__class__.__name__,
            'timestamp': timezone.now().isoformat()
        }
