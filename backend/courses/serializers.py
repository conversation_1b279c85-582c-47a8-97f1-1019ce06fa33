from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Department, Course, Enrollment
from backend.cache_config import cache_result, cache_manager, invalidate_enrollment_cache

User = get_user_model()


class DepartmentSerializer(serializers.ModelSerializer):
    courses_count = serializers.SerializerMethodField()
    head_name = serializers.CharField(source='head.get_display_name', read_only=True)

    class Meta:
        model = Department
        fields = [
            'id', 'name', 'name_ar', 'code', 'description', 'description_ar',
            'head', 'head_name', 'is_active', 'courses_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'courses_count', 'created_at', 'updated_at']
    
    def get_courses_count(self, obj):
        return obj.courses.filter(is_active=True).count()


class DepartmentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = [
            'name', 'name_ar', 'code', 'description', 'description_ar',
            'head', 'is_active'
        ]


class CourseSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    instructor_name = serializers.CharField(source='instructor.get_display_name', read_only=True)
    enrolled_students_count = serializers.SerializerMethodField()
    prerequisites_names = serializers.SerializerMethodField()
    # User-specific fields for enrollment status
    user_enrollment_status = serializers.SerializerMethodField()
    user_can_enroll = serializers.SerializerMethodField()
    user_enrollment_message = serializers.SerializerMethodField()
    user_enrollment_history = serializers.SerializerMethodField()
    user_current_attempt = serializers.SerializerMethodField()
    user_enrollment_details = serializers.SerializerMethodField()

    class Meta:
        model = Course
        fields = [
            'id', 'title', 'title_ar', 'code', 'description', 'description_ar',
            'department', 'department_name', 'instructor', 'instructor_name',
            'credit_hours', 'max_students', 'enrolled_students_count',
            'prerequisites', 'prerequisites_names', 'semester', 'year',
            'start_date', 'end_date', 'is_active', 'created_at', 'updated_at',
            # User-specific fields
            'user_enrollment_status', 'user_can_enroll', 'user_enrollment_message',
            'user_enrollment_history', 'user_current_attempt', 'user_enrollment_details'
        ]
        read_only_fields = ['id', 'enrolled_students_count', 'created_at', 'updated_at']

    def get_enrolled_students_count(self, obj):
        return obj.enrollments.filter(is_active=True).count()

    def get_prerequisites_names(self, obj):
        return [course.title for course in obj.prerequisites.all()]

    def get_user_enrollment_status(self, obj):
        """Get current user's enrollment status for this course - FIXED VERSION"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return 'not_available'

        # CRITICAL FIX: Check for active enrollment first - this is the current state
        active_enrollment = obj.enrollments.filter(
            student=request.user,
            is_active=True
        ).first()

        if active_enrollment:
            # Student has an active enrollment - return its status
            return active_enrollment.status

        # No active enrollment - check enrollment history for retake scenarios
        latest_enrollment = obj.enrollments.filter(
            student=request.user
        ).order_by('-attempt_number', '-enrolled_at').first()

        if not latest_enrollment:
            # No enrollment history - course is available
            return 'available'

        # Handle completed courses - cannot retake successfully completed courses
        if latest_enrollment.status == 'completed':
            return 'completed'

        # Handle failed/dropped courses - check retake eligibility
        if latest_enrollment.status in ['failed', 'dropped']:
            # Check retake limit (max 3 failed attempts)
            failed_attempts = obj.enrollments.filter(
                student=request.user,
                status='failed'
            ).count()

            if failed_attempts >= 3:
                return 'retake_limit_exceeded'
            else:
                return 'retakeable'

        # Default case - course is available
        return 'available'

    def get_user_can_enroll(self, obj):
        """Check if current user can enroll in this course"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return False

        # Get current enrollment status
        enrollment_status = self.get_user_enrollment_status(obj)

        # Cannot enroll if already enrolled or waitlisted
        if enrollment_status in ['enrolled', 'waitlisted']:
            return False

        # Cannot enroll if completed successfully
        if enrollment_status == 'completed':
            return False

        # Cannot enroll if retake limit exceeded
        if enrollment_status == 'retake_limit_exceeded':
            return False

        # Use course's can_enroll method for all other cases
        can_enroll, _ = obj.can_enroll(request.user)
        return can_enroll

    def get_user_enrollment_message(self, obj):
        """Get enrollment message for current user"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return ''

        # Get current enrollment status
        enrollment_status = self.get_user_enrollment_status(obj)

        # Get latest enrollment for additional context
        latest_enrollment = obj.enrollments.filter(
            student=request.user
        ).order_by('-attempt_number', '-enrolled_at').first()

        if enrollment_status == 'enrolled':
            attempt_num = latest_enrollment.attempt_number if latest_enrollment else 1
            return f"Already enrolled (Attempt #{attempt_num})"
        elif enrollment_status == 'waitlisted':
            position = latest_enrollment.waitlist_position if latest_enrollment else 'TBD'
            return f"On waitlist (Position #{position})"
        elif enrollment_status == 'completed':
            grade = latest_enrollment.letter_grade if latest_enrollment else 'N/A'
            return f"Already completed with grade {grade}"
        elif enrollment_status == 'retakeable':
            attempt_num = latest_enrollment.attempt_number if latest_enrollment else 1
            return f"Available for retake (Previous attempt #{attempt_num} {latest_enrollment.status})"
        elif enrollment_status == 'retake_limit_exceeded':
            return "Maximum retake attempts exceeded (3 attempts)"
        elif enrollment_status == 'available':
            # Use course's can_enroll method for detailed message
            can_enroll, message = obj.can_enroll(request.user)
            return message if message else "Available for enrollment"

        return "Status unknown"

    def get_user_enrollment_history(self, obj):
        """Get user's enrollment history for this course"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return []

        enrollments = obj.enrollments.filter(student=request.user).order_by('-enrolled_at')
        return [{
            'id': enrollment.id,
            'status': enrollment.status,
            'attempt_number': enrollment.attempt_number,
            'is_retake': enrollment.is_retake,
            'enrolled_at': enrollment.enrolled_at,
            'completed_at': enrollment.completed_at,
            'dropped_at': enrollment.dropped_at,
            'final_grade': enrollment.final_grade,
            'letter_grade': enrollment.letter_grade,
            'is_active': enrollment.is_active
        } for enrollment in enrollments]

    def get_user_current_attempt(self, obj):
        """Get user's current attempt number for this course"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return 1

        # Get latest enrollment (active or inactive)
        latest_enrollment = obj.enrollments.filter(
            student=request.user
        ).order_by('-attempt_number', '-enrolled_at').first()

        if latest_enrollment:
            # If active enrollment, return current attempt
            if latest_enrollment.is_active:
                return latest_enrollment.attempt_number
            # If inactive, return next attempt number
            else:
                return latest_enrollment.attempt_number + 1

        return 1  # First attempt

    def get_user_enrollment_details(self, obj):
        """Get detailed enrollment information for current user"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return None

        # Get current active enrollment
        active_enrollment = obj.enrollments.filter(
            student=request.user,
            is_active=True
        ).first()

        # Get latest enrollment (active or inactive)
        latest_enrollment = obj.enrollments.filter(
            student=request.user
        ).order_by('-attempt_number', '-enrolled_at').first()

        # Count total attempts
        total_attempts = obj.enrollments.filter(student=request.user).count()

        details = {
            'has_enrollment_history': total_attempts > 0,
            'total_attempts': total_attempts,
            'current_attempt': None,
            'latest_status': None,
            'latest_grade': None,
            'is_retake_eligible': False,
            'retake_count': 0,
        }

        if active_enrollment:
            details.update({
                'current_attempt': active_enrollment.attempt_number,
                'latest_status': active_enrollment.status,
                'latest_grade': active_enrollment.letter_grade,
                'enrollment_date': active_enrollment.enrolled_at,
                'is_active': True,
            })

        elif latest_enrollment:
            details.update({
                'latest_status': latest_enrollment.status,
                'latest_grade': latest_enrollment.letter_grade,
                'latest_attempt': latest_enrollment.attempt_number,
                'enrollment_date': latest_enrollment.enrolled_at,
                'completed_date': latest_enrollment.completed_at,
                'dropped_date': latest_enrollment.dropped_at,
                'is_active': False,
            })

            # Check if eligible for retake
            if latest_enrollment.status in ['failed', 'dropped']:
                details['is_retake_eligible'] = True
                details['retake_count'] = obj.enrollments.filter(
                    student=request.user,
                    status__in=['failed', 'dropped']
                ).count()

        return details


class CourseCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Course
        fields = [
            'title', 'title_ar', 'code', 'description', 'description_ar',
            'department', 'instructor', 'credit_hours', 'max_students',
            'prerequisites', 'semester', 'year', 'start_date',
            'end_date', 'is_active'
        ]
    
    def validate_code(self, value):
        if Course.objects.filter(code=value).exists():
            raise serializers.ValidationError("Course with this code already exists.")
        return value


class CourseUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Course
        fields = [
            'title', 'title_ar', 'description', 'description_ar',
            'instructor', 'credit_hours', 'max_students', 'prerequisites',
            'semester', 'year', 'start_date', 'end_date', 'is_active'
        ]


class EnrollmentSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.display_name', read_only=True)
    student_id = serializers.CharField(source='student.student_id', read_only=True)
    course_name = serializers.CharField(source='course.title', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    attempt_display = serializers.CharField(source='get_attempt_display', read_only=True)

    class Meta:
        model = Enrollment
        fields = [
            'id', 'student', 'student_name', 'student_id', 'course',
            'course_name', 'course_code', 'enrolled_at', 'is_active',
            'status', 'final_grade', 'completed_at', 'updated_at',
            'attempt_number', 'is_retake', 'attempt_display', 'letter_grade'
        ]
        read_only_fields = ['id', 'enrolled_at', 'updated_at', 'attempt_number', 'is_retake', 'attempt_display']


class EnrollmentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Enrollment
        fields = ['student', 'course']
    
    def validate(self, data):
        student = data['student']
        course = data['course']

        # Check if student has an active enrollment (enrolled or waitlisted)
        active_enrollment = Enrollment.objects.filter(
            student=student,
            course=course,
            is_active=True,
            status__in=['enrolled', 'waitlisted']
        ).first()

        if active_enrollment:
            raise serializers.ValidationError(
                f"Student is already {active_enrollment.status} in this course."
            )

        # Use the course's can_enroll method which handles retake logic
        can_enroll, message = course.can_enroll(student)
        if not can_enroll and "waitlist" not in message:
            raise serializers.ValidationError(message)

        return data


class EnrollmentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Enrollment
        fields = ['is_active', 'status', 'final_grade', 'completed_at']


class CourseStatsSerializer(serializers.Serializer):
    total_courses = serializers.IntegerField()
    active_courses = serializers.IntegerField()
    total_enrollments = serializers.IntegerField()
    average_enrollment_per_course = serializers.DecimalField(max_digits=5, decimal_places=2)
    departments_count = serializers.IntegerField()


class DepartmentStatsSerializer(serializers.Serializer):
    total_departments = serializers.IntegerField()
    active_departments = serializers.IntegerField()
    total_courses = serializers.IntegerField()
    total_students = serializers.IntegerField()
