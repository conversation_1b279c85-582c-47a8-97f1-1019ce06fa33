from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Department, Course, Enrollment

User = get_user_model()


class DepartmentSerializer(serializers.ModelSerializer):
    courses_count = serializers.SerializerMethodField()
    head_of_department_name = serializers.CharField(source='head_of_department.get_display_name', read_only=True)
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'name_ar', 'code', 'description', 'description_ar',
            'head_of_department', 'head_of_department_name', 'established_date',
            'is_active', 'courses_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'courses_count', 'created_at', 'updated_at']
    
    def get_courses_count(self, obj):
        return obj.courses.filter(is_active=True).count()


class DepartmentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = [
            'name', 'name_ar', 'code', 'description', 'description_ar',
            'head_of_department', 'established_date', 'is_active'
        ]


class CourseSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    instructor_name = serializers.CharField(source='instructor.get_display_name', read_only=True)
    enrolled_students_count = serializers.SerializerMethodField()
    prerequisites_names = serializers.SerializerMethodField()
    # User-specific fields for enrollment status
    user_enrollment_status = serializers.SerializerMethodField()
    user_can_enroll = serializers.SerializerMethodField()
    user_enrollment_message = serializers.SerializerMethodField()
    user_enrollment_history = serializers.SerializerMethodField()
    user_current_attempt = serializers.SerializerMethodField()

    class Meta:
        model = Course
        fields = [
            'id', 'title', 'title_ar', 'code', 'description', 'description_ar',
            'department', 'department_name', 'instructor', 'instructor_name',
            'credit_hours', 'max_students', 'enrolled_students_count',
            'prerequisites', 'prerequisites_names', 'semester', 'year',
            'start_date', 'end_date', 'is_active', 'created_at', 'updated_at',
            # User-specific fields
            'user_enrollment_status', 'user_can_enroll', 'user_enrollment_message',
            'user_enrollment_history', 'user_current_attempt'
        ]
        read_only_fields = ['id', 'enrolled_students_count', 'created_at', 'updated_at']

    def get_enrolled_students_count(self, obj):
        return obj.enrollments.filter(is_active=True).count()

    def get_prerequisites_names(self, obj):
        return [course.title for course in obj.prerequisites.all()]

    def get_user_enrollment_status(self, obj):
        """Get current user's enrollment status for this course"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return None

        # Get current active enrollment
        current_enrollment = obj.enrollments.filter(
            student=request.user,
            is_active=True
        ).first()

        if current_enrollment:
            return current_enrollment.status

        return 'not_enrolled'

    def get_user_can_enroll(self, obj):
        """Check if current user can enroll in this course"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return None

        can_enroll, message = obj.can_enroll(request.user)
        return can_enroll

    def get_user_enrollment_message(self, obj):
        """Get enrollment message for current user"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return None

        can_enroll, message = obj.can_enroll(request.user)
        return message

    def get_user_enrollment_history(self, obj):
        """Get user's enrollment history for this course"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return []

        enrollments = obj.enrollments.filter(student=request.user).order_by('-enrolled_at')
        return [{
            'id': enrollment.id,
            'status': enrollment.status,
            'attempt_number': enrollment.attempt_number,
            'is_retake': enrollment.is_retake,
            'enrolled_at': enrollment.enrolled_at,
            'completed_at': enrollment.completed_at,
            'dropped_at': enrollment.dropped_at,
            'final_grade': enrollment.final_grade,
            'letter_grade': enrollment.letter_grade,
            'is_active': enrollment.is_active
        } for enrollment in enrollments]

    def get_user_current_attempt(self, obj):
        """Get user's current attempt number for this course"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated or request.user.role != 'student':
            return None

        # Count all previous enrollments (including inactive ones)
        total_attempts = obj.enrollments.filter(student=request.user).count()
        return total_attempts + 1 if total_attempts > 0 else 1


class CourseCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Course
        fields = [
            'title', 'title_ar', 'code', 'description', 'description_ar',
            'department', 'instructor', 'credit_hours', 'max_students',
            'prerequisites', 'semester', 'year', 'start_date',
            'end_date', 'is_active'
        ]
    
    def validate_code(self, value):
        if Course.objects.filter(code=value).exists():
            raise serializers.ValidationError("Course with this code already exists.")
        return value


class CourseUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Course
        fields = [
            'title', 'title_ar', 'description', 'description_ar',
            'instructor', 'credit_hours', 'max_students', 'prerequisites',
            'semester', 'year', 'start_date', 'end_date', 'is_active'
        ]


class EnrollmentSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.display_name', read_only=True)
    student_id = serializers.CharField(source='student.student_id', read_only=True)
    course_name = serializers.CharField(source='course.title', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    attempt_display = serializers.CharField(source='get_attempt_display', read_only=True)

    class Meta:
        model = Enrollment
        fields = [
            'id', 'student', 'student_name', 'student_id', 'course',
            'course_name', 'course_code', 'enrolled_at', 'is_active',
            'status', 'final_grade', 'completed_at', 'updated_at',
            'attempt_number', 'is_retake', 'attempt_display', 'letter_grade'
        ]
        read_only_fields = ['id', 'enrolled_at', 'updated_at', 'attempt_number', 'is_retake', 'attempt_display']


class EnrollmentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Enrollment
        fields = ['student', 'course']
    
    def validate(self, data):
        student = data['student']
        course = data['course']
        
        # Check if student is already enrolled
        if Enrollment.objects.filter(student=student, course=course, is_active=True).exists():
            raise serializers.ValidationError("Student is already enrolled in this course.")
        
        # Check if course has capacity
        enrolled_count = course.enrollments.filter(is_active=True).count()
        if enrolled_count >= course.max_students:
            raise serializers.ValidationError("Course has reached maximum capacity.")
        
        # Check prerequisites
        if course.prerequisites.exists():
            completed_courses = Enrollment.objects.filter(
                student=student,
                is_active=False,
                grade__in=['A+', 'A', 'B+', 'B', 'C+', 'C']
            ).values_list('course_id', flat=True)
            
            missing_prerequisites = course.prerequisites.exclude(id__in=completed_courses)
            if missing_prerequisites.exists():
                missing_names = [course.title for course in missing_prerequisites]
                raise serializers.ValidationError(
                    f"Student has not completed prerequisites: {', '.join(missing_names)}"
                )
        
        return data


class EnrollmentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Enrollment
        fields = ['is_active', 'status', 'final_grade', 'completed_at']


class CourseStatsSerializer(serializers.Serializer):
    total_courses = serializers.IntegerField()
    active_courses = serializers.IntegerField()
    total_enrollments = serializers.IntegerField()
    average_enrollment_per_course = serializers.DecimalField(max_digits=5, decimal_places=2)
    departments_count = serializers.IntegerField()


class DepartmentStatsSerializer(serializers.Serializer):
    total_departments = serializers.IntegerField()
    active_departments = serializers.IntegerField()
    total_courses = serializers.IntegerField()
    total_students = serializers.IntegerField()
