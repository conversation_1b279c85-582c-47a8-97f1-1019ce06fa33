#!/usr/bin/env python3
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from users.models import User
from courses.models import Course, Enrollment
from django.db.models import Count

def check_enrollment_issues():
    print("=== ENROLLMENT ISSUES INVESTIGATION ===\n")
    
    # Check the current student
    try:
        student = User.objects.get(email='<EMAIL>')
        print(f"Student: {student.get_display_name()}")
        print(f"Student ID: {student.id}")
    except User.DoesNotExist:
        print("ERROR: Student not found!")
        return
    
    # Check student's enrollments
    enrollments = Enrollment.objects.filter(student=student)
    print(f"\nStudent enrollments: {enrollments.count()}")
    for enrollment in enrollments:
        print(f"- {enrollment.course.code}: {enrollment.course.title} (Status: {enrollment.status})")
    
    # Check FINAL courses specifically
    final_courses = Course.objects.filter(code__startswith='FINAL').annotate(
        enrollment_count=Count('enrollments')
    ).order_by('code')
    
    print(f"\nFINAL courses enrollment:")
    for course in final_courses:
        print(f"- {course.code}: {course.enrollment_count} students enrolled")
    
    # Check if student is enrolled in any FINAL courses
    student_final_enrollments = Enrollment.objects.filter(
        student=student, 
        course__code__startswith='FINAL'
    )
    print(f"\nStudent enrolled in FINAL courses: {student_final_enrollments.count()}")
    for enrollment in student_final_enrollments:
        print(f"- {enrollment.course.code}: {enrollment.status}")
    
    # Check enrollment status counts
    print(f"\nEnrollment status breakdown:")
    active_enrollments = Enrollment.objects.filter(student=student, status='enrolled')
    print(f"- Active enrollments: {active_enrollments.count()}")
    
    failed_enrollments = Enrollment.objects.filter(student=student, status='failed')
    print(f"- Failed enrollments: {failed_enrollments.count()}")
    
    # Check for data inconsistencies
    print(f"\n=== ISSUES IDENTIFIED ===")
    
    # Issue 1: Dashboard shows 5 courses but student has different enrollments
    dashboard_count = active_enrollments.count()
    print(f"1. Dashboard should show {dashboard_count} active enrollments, not 5")
    
    # Issue 2: FINAL courses show 0 students but might have enrollments
    for course in final_courses:
        actual_enrollments = Enrollment.objects.filter(course=course, status='enrolled').count()
        if course.enrollment_count != actual_enrollments:
            print(f"2. {course.code} shows {course.enrollment_count} total enrollments but {actual_enrollments} active")
    
    # Issue 3: Check for duplicate enrollments
    duplicate_courses = []
    for enrollment in enrollments:
        course_enrollments = enrollments.filter(course=enrollment.course)
        if course_enrollments.count() > 1:
            if enrollment.course.code not in duplicate_courses:
                duplicate_courses.append(enrollment.course.code)
                print(f"3. Duplicate enrollments found for {enrollment.course.code}")

if __name__ == "__main__":
    check_enrollment_issues()
