#!/usr/bin/env python
"""
Test API integration for enrollment fixes
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from courses.models import Course, Enrollment, Department

User = get_user_model()

def test_api_integration():
    """Test API integration with enrollment fixes"""
    print("🧪 Testing API Integration...")
    
    # Clean up
    User.objects.filter(username__startswith='api_test_').delete()
    Department.objects.filter(code='API').delete()
    
    try:
        # Create test data
        department = Department.objects.create(name="API Test", code="API")
        teacher = User.objects.create_user(
            username="api_test_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        student = User.objects.create_user(
            username="api_test_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        course = Course.objects.create(
            title="API Test Course", code="API101", description="Test",
            department=department, instructor=teacher, level="undergraduate", 
            credit_hours=3, semester="spring", year=2025, max_students=30, 
            is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
        
        print("✅ Test data created")
        
        # Test 1: Login and get token
        print("\n🔍 Test 1: Authentication")
        
        login_data = {
            'username': 'api_test_student',
            'password': 'password123'
        }
        
        try:
            response = requests.post('http://localhost:8000/api/auth/login/', json=login_data)
            if response.status_code == 200:
                token = response.json().get('access')
                headers = {'Authorization': f'Bearer {token}'}
                print("  ✅ Authentication successful")
            else:
                print(f"  ❌ Authentication failed: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("  ⚠️  Server not running, testing with direct model calls")
            headers = None
        
        # Test 2: Course listing with enrollment status
        print("\n🔍 Test 2: Course Listing with Enrollment Status")
        
        if headers:
            try:
                response = requests.get(f'http://localhost:8000/api/courses/{course.id}/', headers=headers)
                if response.status_code == 200:
                    course_data = response.json()
                    status = course_data.get('user_enrollment_status')
                    print(f"  ✅ Course status: {status}")
                    assert status == 'available', f"Expected 'available', got '{status}'"
                else:
                    print(f"  ❌ Course listing failed: {response.status_code}")
            except requests.exceptions.ConnectionError:
                print("  ⚠️  Using direct model test")
                # Direct test
                from courses.serializers import CourseSerializer
                from django.test import RequestFactory
                
                factory = RequestFactory()
                request = factory.get('/')
                request.user = student
                
                serializer = CourseSerializer(course, context={'request': request})
                status = serializer.get_user_enrollment_status(course)
                assert status == 'available'
                print(f"  ✅ Course status (direct): {status}")
        
        # Test 3: Enrollment via API
        print("\n🔍 Test 3: Course Enrollment")
        
        if headers:
            try:
                response = requests.post(f'http://localhost:8000/api/courses/{course.id}/enroll/', headers=headers)
                if response.status_code == 201:
                    enrollment_data = response.json()
                    print("  ✅ Enrollment successful via API")
                    
                    # Check status after enrollment
                    response = requests.get(f'http://localhost:8000/api/courses/{course.id}/', headers=headers)
                    if response.status_code == 200:
                        course_data = response.json()
                        status = course_data.get('user_enrollment_status')
                        print(f"  ✅ Status after enrollment: {status}")
                        assert status == 'enrolled', f"Expected 'enrolled', got '{status}'"
                else:
                    print(f"  ❌ Enrollment failed: {response.status_code} - {response.text}")
            except requests.exceptions.ConnectionError:
                print("  ⚠️  Using direct model test")
                # Direct test
                enrollment, _ = course.enroll_student(student)
                assert enrollment.status == 'enrolled'
                print("  ✅ Enrollment successful (direct)")
        
        # Test 4: Retake scenario
        print("\n🔍 Test 4: Retake Scenario")
        
        # Simulate course failure
        enrollment = Enrollment.objects.filter(student=student, course=course).first()
        if enrollment:
            enrollment.status = 'failed'
            enrollment.is_active = False
            enrollment.save()
            print("  ✅ Course marked as failed")
            
            # Check retakeable status
            if headers:
                try:
                    response = requests.get(f'http://localhost:8000/api/courses/{course.id}/', headers=headers)
                    if response.status_code == 200:
                        course_data = response.json()
                        status = course_data.get('user_enrollment_status')
                        print(f"  ✅ Status after failure: {status}")
                        assert status == 'retakeable', f"Expected 'retakeable', got '{status}'"
                except requests.exceptions.ConnectionError:
                    # Direct test
                    from courses.serializers import CourseSerializer
                    from django.test import RequestFactory
                    
                    factory = RequestFactory()
                    request = factory.get('/')
                    request.user = student
                    
                    serializer = CourseSerializer(course, context={'request': request})
                    status = serializer.get_user_enrollment_status(course)
                    assert status == 'retakeable'
                    print(f"  ✅ Status after failure (direct): {status}")
        
        print("\n🎉 API INTEGRATION TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        User.objects.filter(username__startswith='api_test_').delete()
        Department.objects.filter(code='API').delete()

if __name__ == "__main__":
    success = test_api_integration()
    sys.exit(0 if success else 1)
