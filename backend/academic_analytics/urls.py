from django.urls import path
from . import views
from . import learning_experience_views

app_name = 'academic_analytics'

urlpatterns = [
    # Analytics Reports
    path('reports/', views.AnalyticsReportListCreateView.as_view(), name='report-list-create'),
    path('reports/<int:pk>/', views.AnalyticsReportDetailView.as_view(), name='report-detail'),
    path('reports/generate/', views.generate_report, name='generate-report'),
    
    # Dashboard and Metrics
    path('dashboard-metrics/', views.dashboard_metrics, name='dashboard-metrics'),
    
    # Specific Analytics
    path('student-performance/', views.student_performance_analytics, name='student-performance'),
    path('course-analytics/', views.course_analytics, name='course-analytics'),
    path('attendance-analytics/', views.attendance_analytics, name='attendance-analytics'),
    path('grade-analytics/', views.grade_analytics, name='grade-analytics'),
    path('enrollment-trends/', views.enrollment_trends, name='enrollment-trends'),
    path('communication-analytics/', views.communication_analytics, name='communication-analytics'),
    
    # Export and Scheduling
    path('export/', views.export_analytics_data, name='export-data'),
    path('schedule-report/', views.schedule_report, name='schedule-report'),
    path('scheduled-reports/', views.scheduled_reports, name='scheduled-reports'),
    
    # User Activity
    path('user-activity/', views.user_activity, name='user-activity'),
    path('system-metrics/', views.system_metrics, name='system-metrics'),
    
    # Advanced Analytics
    path('insights/', views.analytics_insights, name='analytics-insights'),
    path('predictions/', views.predictive_analytics, name='predictive-analytics'),
    path('benchmarks/', views.benchmarks, name='benchmarks'),
    path('alerts/', views.analytics_alerts, name='analytics-alerts'),
    
    # Configuration
    path('config/', views.analytics_config, name='analytics-config'),
    path('data-quality/', views.data_quality_report, name='data-quality'),

    # Learning Experience APIs
    path('student-journey/<int:student_id>/', learning_experience_views.student_learning_journey, name='student-learning-journey'),
    path('learning-progress/<int:student_id>/', learning_experience_views.learning_progress, name='learning-progress'),
    path('recommendations/<int:student_id>/', learning_experience_views.personalized_recommendations, name='personalized-recommendations'),
    path('academic-plan/<int:student_id>/', learning_experience_views.update_academic_plan, name='update-academic-plan'),
]
