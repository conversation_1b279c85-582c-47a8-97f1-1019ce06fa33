from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q, Count, Avg, Sum, F, Case, When, Value, IntegerField
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
import json

from courses.models import Course, Enrollment, Department
from grades.models import Grade
from assignments.models import Assignment, Submission
from notifications.models import Notification
from .models import AnalyticsReport

User = get_user_model()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def student_learning_journey(request, student_id):
    """
    Get comprehensive learning journey data for a student
    """
    try:
        student = User.objects.get(id=student_id, role='student')
        
        # Get current enrollments
        current_enrollments = Enrollment.objects.filter(
            student=student,
            is_active=True
        ).select_related('course', 'course__department')
        
        # Calculate progress
        total_credits_required = 120  # This should come from degree program
        completed_credits = Enrollment.objects.filter(
            student=student,
            status='completed'
        ).aggregate(total=Sum('course__credit_hours'))['total'] or 0
        
        current_credits = current_enrollments.filter(
            status='enrolled'
        ).aggregate(total=Sum('course__credit_hours'))['total'] or 0
        
        # Get learning path steps
        learning_path_steps = []
        
        # Foundation courses (completed)
        foundation_courses = Enrollment.objects.filter(
            student=student,
            course__level='undergraduate',
            course__code__startswith='MATH',
            status='completed'
        ).select_related('course')
        
        if foundation_courses.exists():
            learning_path_steps.append({
                'id': 1,
                'order': 1,
                'title': 'Foundation Courses',
                'description': 'Complete your core requirements',
                'status': 'completed',
                'courses': [
                    {'id': e.course.id, 'code': e.course.code, 'title': e.course.title}
                    for e in foundation_courses
                ]
            })
        
        # Current courses (active)
        if current_enrollments.exists():
            learning_path_steps.append({
                'id': 2,
                'order': 2,
                'title': 'Current Semester',
                'description': 'Focus on your current courses',
                'status': 'active',
                'courses': [
                    {'id': e.course.id, 'code': e.course.code, 'title': e.course.title}
                    for e in current_enrollments
                ]
            })
        
        # Next recommended courses
        recommended_courses = get_recommended_courses(student)
        if recommended_courses:
            learning_path_steps.append({
                'id': 3,
                'order': 3,
                'title': 'Recommended Next',
                'description': 'Courses that align with your goals',
                'status': 'next',
                'courses': [
                    {'id': c.id, 'code': c.code, 'title': c.title}
                    for c in recommended_courses[:3]
                ]
            })
        
        # Get personalized recommendations
        recommendations = get_personalized_recommendations(student)
        
        journey_data = {
            'learning_path': {
                'steps': learning_path_steps
            },
            'progress': {
                'completion_percentage': round((completed_credits / total_credits_required) * 100, 1),
                'credits_earned': completed_credits,
                'credits_in_progress': current_credits,
                'total_credits_required': total_credits_required,
                'courses_completed': foundation_courses.count(),
                'current_gpa': calculate_student_gpa(student)
            },
            'recommendations': recommendations,
            'current_semester': {
                'courses': [
                    {
                        'id': e.course.id,
                        'code': e.course.code,
                        'title': e.course.title,
                        'credit_hours': e.course.credit_hours,
                        'instructor': e.course.instructor.get_display_name(),
                        'status': e.status
                    }
                    for e in current_enrollments
                ]
            }
        }
        
        return Response(journey_data)
        
    except User.DoesNotExist:
        return Response(
            {'error': 'Student not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def learning_progress(request, student_id):
    """
    Get detailed learning progress metrics
    """
    try:
        student = User.objects.get(id=student_id, role='student')
        
        # Time range filter
        time_range = request.GET.get('time_range', 'semester')
        end_date = timezone.now()
        
        if time_range == 'week':
            start_date = end_date - timedelta(weeks=1)
        elif time_range == 'month':
            start_date = end_date - timedelta(days=30)
        elif time_range == 'year':
            start_date = end_date - timedelta(days=365)
        else:  # semester
            start_date = end_date - timedelta(days=120)
        
        # Get enrollments in time range
        enrollments = Enrollment.objects.filter(
            student=student,
            created_at__gte=start_date
        ).select_related('course')
        
        # Calculate metrics
        total_credits = enrollments.aggregate(
            total=Sum('course__credit_hours')
        )['total'] or 0
        
        completed_courses = enrollments.filter(status='completed').count()
        in_progress_courses = enrollments.filter(status='enrolled').count()
        
        # Get grades for GPA calculation
        current_gpa = calculate_student_gpa(student)
        
        # Get study activity (mock data for now)
        study_hours_per_week = 24  # This would come from activity tracking
        
        progress_data = {
            'completion_percentage': calculate_degree_completion_percentage(student),
            'credits_earned': get_total_credits_earned(student),
            'credits_in_progress': total_credits,
            'courses_completed': completed_courses,
            'courses_in_progress': in_progress_courses,
            'current_gpa': current_gpa,
            'study_hours_per_week': study_hours_per_week,
            'time_range': time_range
        }
        
        return Response(progress_data)
        
    except User.DoesNotExist:
        return Response(
            {'error': 'Student not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def personalized_recommendations(request, student_id):
    """
    Get personalized course and learning recommendations
    """
    try:
        student = User.objects.get(id=student_id, role='student')
        recommendation_type = request.GET.get('type', 'all')
        
        recommendations = []
        
        if recommendation_type in ['all', 'courses']:
            # Course recommendations
            recommended_courses = get_recommended_courses(student)
            for course in recommended_courses[:3]:
                recommendations.append({
                    'id': f'course_{course.id}',
                    'type': 'course',
                    'title': f'Consider {course.code}',
                    'description': f'{course.title} aligns with your academic goals',
                    'action': 'View Course Details',
                    'priority': 'high' if course.level == 'undergraduate' else 'medium',
                    'metadata': {
                        'course_id': course.id,
                        'course_code': course.code,
                        'credit_hours': course.credit_hours
                    }
                })
        
        if recommendation_type in ['all', 'study_habits']:
            # Study habit recommendations
            recommendations.extend([
                {
                    'id': 'study_groups',
                    'type': 'study_habit',
                    'title': 'Join Study Groups',
                    'description': 'Connect with peers in your courses for collaborative learning',
                    'action': 'Find Study Groups',
                    'priority': 'medium'
                },
                {
                    'id': 'time_management',
                    'type': 'study_habit',
                    'title': 'Optimize Study Schedule',
                    'description': 'Based on your patterns, 2-4 PM is your most productive time',
                    'action': 'Update Schedule',
                    'priority': 'low'
                }
            ])
        
        if recommendation_type in ['all', 'academic']:
            # Academic recommendations
            recommendations.append({
                'id': 'advisor_meeting',
                'type': 'academic',
                'title': 'Schedule Advisor Meeting',
                'description': 'Plan your next semester and discuss career goals',
                'action': 'Book Appointment',
                'priority': 'high'
            })
        
        return Response({
            'recommendations': recommendations,
            'total_count': len(recommendations),
            'type_filter': recommendation_type
        })
        
    except User.DoesNotExist:
        return Response(
            {'error': 'Student not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_academic_plan(request, student_id):
    """
    Update or create academic plan for student
    """
    try:
        student = User.objects.get(id=student_id, role='student')
        plan_data = request.data
        
        # Save academic plan (you might want to create a separate model for this)
        academic_plan = {
            'student_id': student_id,
            'goals': plan_data.get('goals', []),
            'timeline': plan_data.get('timeline', ''),
            'preferences': plan_data.get('preferences', {}),
            'selected_courses': plan_data.get('selectedCourses', []),
            'career_path': plan_data.get('careerPath', ''),
            'created_at': timezone.now().isoformat(),
            'updated_at': timezone.now().isoformat()
        }
        
        # For now, we'll store this in the user's profile or create a separate model
        # This is a simplified implementation
        
        return Response({
            'success': True,
            'message': 'Academic plan updated successfully',
            'plan_id': f'plan_{student_id}_{int(timezone.now().timestamp())}'
        })
        
    except User.DoesNotExist:
        return Response(
            {'error': 'Student not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Helper functions
def get_recommended_courses(student):
    """
    Get recommended courses based on student's academic history and goals
    """
    # Get completed courses
    completed_courses = Enrollment.objects.filter(
        student=student,
        status='completed'
    ).values_list('course_id', flat=True)
    
    # Get current enrollments
    current_courses = Enrollment.objects.filter(
        student=student,
        status='enrolled'
    ).values_list('course_id', flat=True)
    
    # Find courses where prerequisites are met
    available_courses = Course.objects.filter(
        is_active=True,
        is_published=True
    ).exclude(
        id__in=list(completed_courses) + list(current_courses)
    )
    
    # Filter by prerequisites (simplified logic)
    recommended = []
    for course in available_courses:
        prerequisites = course.prerequisites.all()
        if not prerequisites.exists() or all(
            prereq.id in completed_courses for prereq in prerequisites
        ):
            recommended.append(course)
    
    return recommended[:5]  # Return top 5 recommendations


def get_personalized_recommendations(student):
    """
    Generate personalized recommendations based on student data
    """
    recommendations = []
    
    # Check if student needs to meet with advisor
    last_enrollment = Enrollment.objects.filter(student=student).order_by('-created_at').first()
    if not last_enrollment or (timezone.now() - last_enrollment.created_at).days > 90:
        recommendations.append({
            'id': 'advisor_meeting',
            'title': 'Schedule Academic Advisor Meeting',
            'description': 'Plan your academic path and discuss career goals',
            'action': 'Book Appointment',
            'priority': 'high'
        })
    
    # Check for course recommendations
    recommended_courses = get_recommended_courses(student)
    if recommended_courses:
        recommendations.append({
            'id': 'course_recommendation',
            'title': f'Consider {recommended_courses[0].code}',
            'description': f'{recommended_courses[0].title} fits your learning path',
            'action': 'View Course',
            'priority': 'medium'
        })
    
    return recommendations


def calculate_student_gpa(student):
    """
    Calculate current GPA for student
    """
    grades = Grade.objects.filter(
        enrollment__student=student,
        enrollment__status='completed'
    ).aggregate(avg_grade=Avg('percentage'))
    
    avg_percentage = grades['avg_grade']
    if avg_percentage is None:
        return 0.0
    
    # Convert percentage to 4.0 scale
    if avg_percentage >= 97:
        return 4.0
    elif avg_percentage >= 93:
        return 3.7
    elif avg_percentage >= 90:
        return 3.3
    elif avg_percentage >= 87:
        return 3.0
    elif avg_percentage >= 83:
        return 2.7
    elif avg_percentage >= 80:
        return 2.3
    elif avg_percentage >= 77:
        return 2.0
    elif avg_percentage >= 73:
        return 1.7
    elif avg_percentage >= 70:
        return 1.3
    elif avg_percentage >= 67:
        return 1.0
    elif avg_percentage >= 65:
        return 0.7
    else:
        return 0.0


def calculate_degree_completion_percentage(student):
    """
    Calculate degree completion percentage
    """
    total_required_credits = 120  # This should come from degree program
    completed_credits = Enrollment.objects.filter(
        student=student,
        status='completed'
    ).aggregate(total=Sum('course__credit_hours'))['total'] or 0
    
    return round((completed_credits / total_required_credits) * 100, 1)


def get_total_credits_earned(student):
    """
    Get total credits earned by student
    """
    return Enrollment.objects.filter(
        student=student,
        status='completed'
    ).aggregate(total=Sum('course__credit_hours'))['total'] or 0
