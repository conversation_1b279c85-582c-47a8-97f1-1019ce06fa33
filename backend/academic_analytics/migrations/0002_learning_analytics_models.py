# Generated migration for learning analytics models

from django.db import migrations, models
import django.db.models.deletion
import django.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
        ('courses', '0007_merge_20250714_1217'),
        ('assignments', '0001_initial'),
        ('academic_analytics', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LearningActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[
                    ('login', 'Login'),
                    ('course_view', 'Course View'),
                    ('enrollment', 'Course Enrollment'),
                    ('assignment_view', 'Assignment View'),
                    ('assignment_submit', 'Assignment Submission'),
                    ('grade_view', 'Grade View'),
                    ('resource_download', 'Resource Download'),
                    ('discussion_post', 'Discussion Post'),
                    ('quiz_attempt', 'Quiz Attempt'),
                    ('video_watch', 'Video Watch'),
                    ('reading_time', 'Reading Time'),
                    ('search', 'Search Activity'),
                    ('navigation', 'Navigation'),
                    ('help_access', 'Help Access'),
                    ('profile_update', 'Profile Update'),
                    ('schedule_view', 'Schedule View'),
                    ('analytics_view', 'Analytics View'),
                    ('recommendation_click', 'Recommendation Click'),
                    ('goal_set', 'Goal Setting'),
                    ('plan_update', 'Academic Plan Update')
                ], max_length=50)),
                ('activity_data', models.JSONField(blank=True, default=dict)),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('duration_seconds', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('assignment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='learning_activities', to='assignments.assignment')),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='learning_activities', to='courses.course')),
                ('student', models.ForeignKey(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='learning_activities', to='users.user')),
            ],
            options={
                'verbose_name': 'Learning Activity',
                'verbose_name_plural': 'Learning Activities',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentLearningProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preferred_learning_style', models.CharField(blank=True, choices=[
                    ('visual', 'Visual'),
                    ('auditory', 'Auditory'),
                    ('kinesthetic', 'Kinesthetic'),
                    ('reading_writing', 'Reading/Writing'),
                    ('multimodal', 'Multimodal')
                ], max_length=20)),
                ('preferred_study_time', models.CharField(blank=True, max_length=50)),
                ('preferred_session_length', models.PositiveIntegerField(default=45)),
                ('preferred_difficulty_progression', models.CharField(default='gradual', max_length=20)),
                ('average_grade', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('current_gpa', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(4.0)])),
                ('completion_rate', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('average_study_hours_per_week', models.FloatField(default=0.0)),
                ('average_session_duration', models.PositiveIntegerField(default=0)),
                ('login_frequency_per_week', models.FloatField(default=0.0)),
                ('resource_usage_rate', models.FloatField(default=0.0)),
                ('most_active_day', models.CharField(blank=True, max_length=10)),
                ('most_active_time', models.CharField(blank=True, max_length=20)),
                ('peak_performance_time', models.CharField(blank=True, max_length=20)),
                ('strength_areas', models.JSONField(blank=True, default=list)),
                ('improvement_areas', models.JSONField(blank=True, default=list)),
                ('learning_goals', models.JSONField(blank=True, default=list)),
                ('degree_completion_percentage', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('projected_graduation_date', models.DateField(blank=True, null=True)),
                ('academic_plan_data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_analytics_update', models.DateTimeField(auto_now=True)),
                ('student', models.OneToOneField(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, related_name='learning_profile', to='users.user')),
            ],
            options={
                'verbose_name': 'Student Learning Profile',
                'verbose_name_plural': 'Student Learning Profiles',
                'ordering': ['student__last_name', 'student__first_name'],
            },
        ),
        migrations.AddIndex(
            model_name='learningactivity',
            index=models.Index(fields=['student', 'activity_type', 'created_at'], name='academic_ana_student_b8e8a5_idx'),
        ),
        migrations.AddIndex(
            model_name='learningactivity',
            index=models.Index(fields=['student', 'course', 'created_at'], name='academic_ana_student_4f8b2c_idx'),
        ),
        migrations.AddIndex(
            model_name='learningactivity',
            index=models.Index(fields=['activity_type', 'created_at'], name='academic_ana_activit_8c9d1e_idx'),
        ),
        migrations.AddIndex(
            model_name='learningactivity',
            index=models.Index(fields=['session_id', 'created_at'], name='academic_ana_session_7a6f3b_idx'),
        ),
    ]
