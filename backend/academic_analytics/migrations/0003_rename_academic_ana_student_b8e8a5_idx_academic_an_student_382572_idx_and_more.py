# Generated by Django 4.2.7 on 2025-07-14 11:16

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('academic_analytics', '0002_learning_analytics_models'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='learningactivity',
            new_name='academic_an_student_382572_idx',
            old_name='academic_ana_student_b8e8a5_idx',
        ),
        migrations.RenameIndex(
            model_name='learningactivity',
            new_name='academic_an_student_030ce4_idx',
            old_name='academic_ana_student_4f8b2c_idx',
        ),
        migrations.RenameIndex(
            model_name='learningactivity',
            new_name='academic_an_activit_d3b47f_idx',
            old_name='academic_ana_activit_8c9d1e_idx',
        ),
        migrations.RenameIndex(
            model_name='learningactivity',
            new_name='academic_an_session_ecc883_idx',
            old_name='academic_ana_session_7a6f3b_idx',
        ),
    ]
