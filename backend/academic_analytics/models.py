from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.core.validators import MinValueValidator, MaxValueValidator
import json

User = get_user_model()


class AnalyticsReport(models.Model):
    """
    Analytics reports for various metrics
    """
    class ReportType(models.TextChoices):
        STUDENT_PERFORMANCE = 'student_performance', _('Student Performance')
        COURSE_ANALYTICS = 'course_analytics', _('Course Analytics')
        ATTENDANCE_REPORT = 'attendance_report', _('Attendance Report')
        GRADE_DISTRIBUTION = 'grade_distribution', _('Grade Distribution')
        ENROLLMENT_TRENDS = 'enrollment_trends', _('Enrollment Trends')
        FINANCIAL_SUMMARY = 'financial_summary', _('Financial Summary')
        COMMUNICATION_STATS = 'communication_stats', _('Communication Statistics')
        SYSTEM_USAGE = 'system_usage', _('System Usage')
        CUSTOM = 'custom', _('Custom Report')

    class ReportStatus(models.TextChoices):
        PENDING = 'pending', _('Pending')
        GENERATING = 'generating', _('Generating')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')
        SCHEDULED = 'scheduled', _('Scheduled')

    # Basic information
    title = models.CharField(_('title'), max_length=200)
    title_ar = models.CharField(_('title (Arabic)'), max_length=200, blank=True)
    description = models.TextField(_('description'), blank=True)

    # Report configuration
    report_type = models.CharField(_('report type'), max_length=30, choices=ReportType.choices)
    status = models.CharField(_('status'), max_length=20, choices=ReportStatus.choices, default=ReportStatus.PENDING)

    # Date range
    start_date = models.DateField(_('start date'), null=True, blank=True)
    end_date = models.DateField(_('end date'), null=True, blank=True)

    # Filters and parameters
    filters = models.JSONField(_('filters'), default=dict, blank=True)
    parameters = models.JSONField(_('parameters'), default=dict, blank=True)

    # Report data
    data = models.JSONField(_('report data'), default=dict, blank=True)
    summary = models.JSONField(_('summary'), default=dict, blank=True)

    # File output
    file_path = models.CharField(_('file path'), max_length=500, blank=True)
    file_format = models.CharField(_('file format'), max_length=10, default='json')

    # Scheduling
    is_scheduled = models.BooleanField(_('scheduled'), default=False)
    schedule_frequency = models.CharField(
        _('schedule frequency'),
        max_length=20,
        choices=[
            ('daily', _('Daily')),
            ('weekly', _('Weekly')),
            ('monthly', _('Monthly')),
            ('quarterly', _('Quarterly')),
            ('yearly', _('Yearly'))
        ],
        blank=True
    )
    next_run_date = models.DateTimeField(_('next run date'), null=True, blank=True)

    # Access control
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_analytics_reports')
    shared_with = models.ManyToManyField(User, related_name='shared_analytics_reports', blank=True)
    is_public = models.BooleanField(_('public'), default=False)

    # Timestamps
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    generated_at = models.DateTimeField(_('generated at'), null=True, blank=True)

    class Meta:
        verbose_name = _('Analytics Report')
        verbose_name_plural = _('Analytics Reports')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['report_type', 'status']),
            models.Index(fields=['created_by', '-created_at']),
            models.Index(fields=['is_scheduled', 'next_run_date']),
        ]

    def __str__(self):
        return self.title

    def generate_report(self):
        """Generate the report based on type and parameters"""
        from .services import AnalyticsService

        self.status = self.ReportStatus.GENERATING
        self.save()

        try:
            service = AnalyticsService()

            if self.report_type == self.ReportType.STUDENT_PERFORMANCE:
                self.data = service.generate_student_performance_report(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    filters=self.filters
                )
            elif self.report_type == self.ReportType.COURSE_ANALYTICS:
                self.data = service.generate_course_analytics_report(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    filters=self.filters
                )
            elif self.report_type == self.ReportType.ATTENDANCE_REPORT:
                self.data = service.generate_attendance_report(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    filters=self.filters
                )
            elif self.report_type == self.ReportType.GRADE_DISTRIBUTION:
                self.data = service.generate_grade_distribution_report(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    filters=self.filters
                )
            elif self.report_type == self.ReportType.ENROLLMENT_TRENDS:
                self.data = service.generate_enrollment_trends_report(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    filters=self.filters
                )
            elif self.report_type == self.ReportType.COMMUNICATION_STATS:
                self.data = service.generate_communication_stats_report(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    filters=self.filters
                )

            self.status = self.ReportStatus.COMPLETED
            self.generated_at = timezone.now()

        except Exception as e:
            self.status = self.ReportStatus.FAILED
            self.data = {'error': str(e)}

        self.save()


class UserActivity(models.Model):
    """
    Track user activities for analytics
    """
    class ActivityType(models.TextChoices):
        LOGIN = 'login', _('Login')
        LOGOUT = 'logout', _('Logout')
        PAGE_VIEW = 'page_view', _('Page View')
        COURSE_ACCESS = 'course_access', _('Course Access')
        ASSIGNMENT_SUBMIT = 'assignment_submit', _('Assignment Submit')
        GRADE_VIEW = 'grade_view', _('Grade View')
        MESSAGE_SEND = 'message_send', _('Message Send')
        FILE_DOWNLOAD = 'file_download', _('File Download')
        SEARCH = 'search', _('Search')
        EXPORT = 'export', _('Export')
        REPORT_GENERATE = 'report_generate', _('Report Generate')

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(_('activity type'), max_length=30, choices=ActivityType.choices)

    # Activity details
    description = models.CharField(_('description'), max_length=500, blank=True)
    metadata = models.JSONField(_('metadata'), default=dict, blank=True)

    # Related object (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    related_object = GenericForeignKey('content_type', 'object_id')

    # Session and request info
    session_key = models.CharField(_('session key'), max_length=40, blank=True)
    ip_address = models.GenericIPAddressField(_('IP address'), null=True, blank=True)
    user_agent = models.TextField(_('user agent'), blank=True)

    # Timestamp
    timestamp = models.DateTimeField(_('timestamp'), auto_now_add=True)

    class Meta:
        verbose_name = _('User Activity')
        verbose_name_plural = _('User Activities')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['activity_type', '-timestamp']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"{self.user.get_display_name()} - {self.activity_type} at {self.timestamp}"


class LearningActivity(models.Model):
    """
    Track student learning activities for analytics
    """
    ACTIVITY_TYPES = [
        ('login', 'Login'),
        ('course_view', 'Course View'),
        ('enrollment', 'Course Enrollment'),
        ('assignment_view', 'Assignment View'),
        ('assignment_submit', 'Assignment Submission'),
        ('grade_view', 'Grade View'),
        ('resource_download', 'Resource Download'),
        ('discussion_post', 'Discussion Post'),
        ('quiz_attempt', 'Quiz Attempt'),
        ('video_watch', 'Video Watch'),
        ('reading_time', 'Reading Time'),
        ('search', 'Search Activity'),
        ('navigation', 'Navigation'),
        ('help_access', 'Help Access'),
        ('profile_update', 'Profile Update'),
        ('schedule_view', 'Schedule View'),
        ('analytics_view', 'Analytics View'),
        ('recommendation_click', 'Recommendation Click'),
        ('goal_set', 'Goal Setting'),
        ('plan_update', 'Academic Plan Update')
    ]

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='learning_activities',
        limit_choices_to={'role': 'student'}
    )
    activity_type = models.CharField(max_length=50, choices=ACTIVITY_TYPES)
    activity_data = models.JSONField(default=dict, blank=True)
    session_id = models.CharField(max_length=100, blank=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True)
    duration_seconds = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    # Learning context
    course = models.ForeignKey(
        'courses.Course',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='learning_activities'
    )
    assignment = models.ForeignKey(
        'assignments.Assignment',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='learning_activities'
    )

    class Meta:
        verbose_name = 'Learning Activity'
        verbose_name_plural = 'Learning Activities'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['student', 'activity_type', 'created_at']),
            models.Index(fields=['student', 'course', 'created_at']),
            models.Index(fields=['activity_type', 'created_at']),
            models.Index(fields=['session_id', 'created_at']),
        ]

    def __str__(self):
        return f"{self.student.get_display_name()} - {self.get_activity_type_display()} - {self.created_at}"


class StudentLearningProfile(models.Model):
    """
    Store aggregated learning analytics and insights for students
    """
    LEARNING_STYLES = [
        ('visual', 'Visual'),
        ('auditory', 'Auditory'),
        ('kinesthetic', 'Kinesthetic'),
        ('reading_writing', 'Reading/Writing'),
        ('multimodal', 'Multimodal')
    ]

    student = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='learning_profile',
        limit_choices_to={'role': 'student'}
    )

    # Learning preferences
    preferred_learning_style = models.CharField(
        max_length=20,
        choices=LEARNING_STYLES,
        blank=True
    )
    preferred_study_time = models.CharField(max_length=50, blank=True)
    preferred_session_length = models.PositiveIntegerField(default=45)  # minutes
    preferred_difficulty_progression = models.CharField(max_length=20, default='gradual')

    # Performance metrics
    average_grade = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    current_gpa = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(4.0)]
    )
    completion_rate = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )

    # Engagement metrics
    average_study_hours_per_week = models.FloatField(default=0.0)
    average_session_duration = models.PositiveIntegerField(default=0)  # seconds
    login_frequency_per_week = models.FloatField(default=0.0)
    resource_usage_rate = models.FloatField(default=0.0)

    # Learning patterns
    most_active_day = models.CharField(max_length=10, blank=True)
    most_active_time = models.CharField(max_length=20, blank=True)
    peak_performance_time = models.CharField(max_length=20, blank=True)

    # Strengths and weaknesses
    strength_areas = models.JSONField(default=list, blank=True)
    improvement_areas = models.JSONField(default=list, blank=True)
    learning_goals = models.JSONField(default=list, blank=True)

    # Academic planning
    degree_completion_percentage = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    projected_graduation_date = models.DateField(blank=True, null=True)
    academic_plan_data = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_analytics_update = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Student Learning Profile'
        verbose_name_plural = 'Student Learning Profiles'
        ordering = ['student__last_name', 'student__first_name']

    def __str__(self):
        return f"Learning Profile - {self.student.get_display_name()}"
