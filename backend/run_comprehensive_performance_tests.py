#!/usr/bin/env python
"""
COMPREHENSIVE PERFORMANCE TESTING SUITE RUNNER
Runs all performance tests and generates unified reports
"""

import os
import sys
import django
import time
import psutil
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from performance_tests.enrollment_performance_test import run_comprehensive_performance_tests
from performance_tests.concurrent_enrollment_test import run_concurrent_enrollment_tests
from performance_tests.realistic_load_test import run_realistic_load_tests


class ComprehensivePerformanceTester:
    """Master performance testing coordinator"""
    
    def __init__(self):
        self.test_results = {}
        self.system_metrics = {}
        
    def get_system_baseline(self):
        """Get baseline system metrics"""
        process = psutil.Process()
        
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_available_gb': psutil.virtual_memory().available / (1024**3),
            'disk_usage_percent': psutil.disk_usage('/').percent,
            'process_memory_mb': process.memory_info().rss / (1024**2),
            'timestamp': datetime.now().isoformat()
        }
    
    def run_all_performance_tests(self):
        """Run all performance test suites"""
        print("🚀 STARTING COMPREHENSIVE PERFORMANCE TESTING SUITE")
        print("="*80)
        
        # Get baseline metrics
        print("\n📊 Collecting baseline system metrics...")
        baseline_metrics = self.get_system_baseline()
        self.system_metrics['baseline'] = baseline_metrics
        
        print(f"  💾 Memory Available: {baseline_metrics['memory_available_gb']:.1f} GB")
        print(f"  🖥️  CPU Usage: {baseline_metrics['cpu_percent']:.1f}%")
        print(f"  💽 Disk Usage: {baseline_metrics['disk_usage_percent']:.1f}%")
        
        test_suites = [
            {
                'name': 'Basic Performance Tests',
                'function': run_comprehensive_performance_tests,
                'description': 'Single enrollment, bulk operations, security validation'
            },
            {
                'name': 'Concurrent Enrollment Tests', 
                'function': run_concurrent_enrollment_tests,
                'description': 'Race conditions, concurrent operations, data integrity'
            },
            {
                'name': 'Realistic Load Tests',
                'function': run_realistic_load_tests,
                'description': 'University scenarios, peak registration periods'
            }
        ]
        
        overall_success = True
        
        for i, test_suite in enumerate(test_suites, 1):
            print(f"\n{'='*80}")
            print(f"🧪 TEST SUITE {i}/{len(test_suites)}: {test_suite['name']}")
            print(f"📝 {test_suite['description']}")
            print(f"{'='*80}")
            
            # Get pre-test metrics
            pre_test_metrics = self.get_system_baseline()
            
            # Run test suite
            start_time = time.time()
            try:
                success = test_suite['function']()
                self.test_results[test_suite['name']] = {
                    'success': success,
                    'duration': time.time() - start_time,
                    'error': None
                }
                
                if success:
                    print(f"✅ {test_suite['name']} completed successfully")
                else:
                    print(f"❌ {test_suite['name']} failed")
                    overall_success = False
                    
            except Exception as e:
                duration = time.time() - start_time
                self.test_results[test_suite['name']] = {
                    'success': False,
                    'duration': duration,
                    'error': str(e)
                }
                print(f"❌ {test_suite['name']} failed with error: {e}")
                overall_success = False
            
            # Get post-test metrics
            post_test_metrics = self.get_system_baseline()
            self.system_metrics[f"{test_suite['name']}_pre"] = pre_test_metrics
            self.system_metrics[f"{test_suite['name']}_post"] = post_test_metrics
            
            # Brief cooldown between test suites
            if i < len(test_suites):
                print("\n💤 Cooldown period between test suites...")
                time.sleep(15)
        
        return overall_success
    
    def generate_unified_performance_report(self):
        """Generate comprehensive performance report"""
        print("\n" + "="*100)
        print("📊 UNIFIED COMPREHENSIVE PERFORMANCE TESTING REPORT")
        print("="*100)
        
        # Test Suite Results Summary
        print("\n🧪 TEST SUITE RESULTS SUMMARY:")
        print("-" * 50)
        
        total_duration = 0
        successful_suites = 0
        
        for suite_name, result in self.test_results.items():
            status = "✅ PASSED" if result['success'] else "❌ FAILED"
            duration = result['duration']
            total_duration += duration
            
            if result['success']:
                successful_suites += 1
            
            print(f"  {status} {suite_name}")
            print(f"    ⏱️  Duration: {duration:.1f}s")
            
            if result['error']:
                print(f"    ❌ Error: {result['error']}")
        
        print(f"\n📈 OVERALL RESULTS:")
        print(f"  🎯 Test Suites Passed: {successful_suites}/{len(self.test_results)}")
        print(f"  ⏱️  Total Testing Duration: {total_duration:.1f}s ({total_duration/60:.1f} minutes)")
        print(f"  📊 Success Rate: {(successful_suites/len(self.test_results)*100):.1f}%")
        
        # System Performance Analysis
        print("\n🖥️  SYSTEM PERFORMANCE ANALYSIS:")
        print("-" * 50)
        
        baseline = self.system_metrics.get('baseline', {})
        
        print(f"  💾 Baseline Memory Available: {baseline.get('memory_available_gb', 0):.1f} GB")
        print(f"  🖥️  Baseline CPU Usage: {baseline.get('cpu_percent', 0):.1f}%")
        
        # Analyze memory usage across tests
        memory_usage_changes = []
        cpu_usage_changes = []
        
        for suite_name in self.test_results.keys():
            pre_key = f"{suite_name}_pre"
            post_key = f"{suite_name}_post"
            
            if pre_key in self.system_metrics and post_key in self.system_metrics:
                pre_metrics = self.system_metrics[pre_key]
                post_metrics = self.system_metrics[post_key]
                
                memory_change = post_metrics['process_memory_mb'] - pre_metrics['process_memory_mb']
                cpu_change = post_metrics['cpu_percent'] - pre_metrics['cpu_percent']
                
                memory_usage_changes.append(memory_change)
                cpu_usage_changes.append(cpu_change)
                
                print(f"  📊 {suite_name}:")
                print(f"    💾 Memory Change: {memory_change:+.1f} MB")
                print(f"    🖥️  CPU Change: {cpu_change:+.1f}%")
        
        if memory_usage_changes:
            avg_memory_change = sum(memory_usage_changes) / len(memory_usage_changes)
            max_memory_change = max(memory_usage_changes)
            print(f"\n  📈 Average Memory Change: {avg_memory_change:+.1f} MB")
            print(f"  📈 Peak Memory Change: {max_memory_change:+.1f} MB")
        
        # Performance Recommendations
        print("\n💡 PERFORMANCE RECOMMENDATIONS:")
        print("-" * 50)
        
        recommendations = []
        
        # Analyze results and provide recommendations
        if successful_suites < len(self.test_results):
            recommendations.append("⚠️  Some test suites failed - investigate and fix issues")
        
        if max(memory_usage_changes, default=0) > 100:  # More than 100MB increase
            recommendations.append("⚠️  High memory usage detected - consider memory optimization")
        
        if max(cpu_usage_changes, default=0) > 50:  # More than 50% CPU increase
            recommendations.append("⚠️  High CPU usage detected - consider performance optimization")
        
        if total_duration > 1800:  # More than 30 minutes
            recommendations.append("⚠️  Long testing duration - consider test optimization")
        
        # Positive recommendations
        if successful_suites == len(self.test_results):
            recommendations.append("✅ All test suites passed - excellent system stability")
        
        if max(memory_usage_changes, default=0) < 50:
            recommendations.append("✅ Low memory usage - good memory management")
        
        if total_duration < 600:  # Less than 10 minutes
            recommendations.append("✅ Fast test execution - good system performance")
        
        for recommendation in recommendations:
            print(f"  {recommendation}")
        
        # Final Assessment
        print("\n🎯 FINAL PERFORMANCE ASSESSMENT:")
        print("-" * 50)
        
        if successful_suites == len(self.test_results):
            if max(memory_usage_changes, default=0) < 50 and total_duration < 900:
                assessment = "🌟 EXCELLENT - System performs exceptionally well under all test conditions"
            else:
                assessment = "✅ GOOD - System is stable but has room for optimization"
        elif successful_suites >= len(self.test_results) * 0.8:
            assessment = "⚠️  ACCEPTABLE - System mostly stable but needs attention to failed tests"
        else:
            assessment = "❌ NEEDS IMPROVEMENT - Multiple test failures indicate system issues"
        
        print(f"  {assessment}")
        
        # Database and Cache Recommendations
        print("\n🗄️  DATABASE & CACHE RECOMMENDATIONS:")
        print("-" * 50)
        print("  📊 Consider implementing the performance optimizations:")
        print("    - Strategic database indexes (see migrations/0011_performance_indexes.py)")
        print("    - Intelligent caching strategies (see caching_strategies.py)")
        print("    - Query optimization (see performance_optimizations.py)")
        print("    - Connection pooling for high-load scenarios")
        print("    - Redis/Memcached for production caching")
        
        print("\n🚀 NEXT STEPS:")
        print("-" * 50)
        print("  1. Apply database performance migrations")
        print("  2. Configure production caching (Redis recommended)")
        print("  3. Set up monitoring for production metrics")
        print("  4. Run these tests periodically to catch regressions")
        print("  5. Scale testing for your expected user load")
        
        return successful_suites == len(self.test_results)


def main():
    """Main performance testing entry point"""
    print("🎯 UMLS ENROLLMENT SYSTEM - COMPREHENSIVE PERFORMANCE TESTING")
    print("="*80)
    print("This suite will test:")
    print("  • Basic enrollment performance")
    print("  • Concurrent enrollment handling") 
    print("  • Realistic university load scenarios")
    print("  • System resource usage")
    print("  • Race condition detection")
    print("  • Data integrity under load")
    print("="*80)
    
    tester = ComprehensivePerformanceTester()
    
    try:
        # Run all performance tests
        overall_success = tester.run_all_performance_tests()
        
        # Generate unified report
        report_success = tester.generate_unified_performance_report()
        
        if overall_success and report_success:
            print("\n🎉 ALL PERFORMANCE TESTS COMPLETED SUCCESSFULLY!")
            return True
        else:
            print("\n⚠️  PERFORMANCE TESTING COMPLETED WITH ISSUES")
            return False
            
    except Exception as e:
        print(f"\n❌ PERFORMANCE TESTING FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
