#!/usr/bin/env python
"""
Test frontend integration scenarios for enrollment fixes
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.test import RequestFactory
from courses.models import Course, Enrollment, Department
from courses.serializers import CourseSerializer

User = get_user_model()

def test_frontend_integration():
    """Test frontend integration scenarios"""
    print("🧪 Testing Frontend Integration Scenarios...")
    
    # Clean up
    User.objects.filter(username__startswith='frontend_test_').delete()
    Department.objects.filter(code='FRONTEND').delete()
    
    try:
        # Create test data
        department = Department.objects.create(name="Frontend Test", code="FRONTEND")
        teacher = User.objects.create_user(
            username="frontend_test_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        student = User.objects.create_user(
            username="frontend_test_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        course = Course.objects.create(
            title="Frontend Test Course", code="FRONTEND101", description="Test",
            department=department, instructor=teacher, level="undergraduate", 
            credit_hours=3, semester="spring", year=2025, max_students=30, 
            is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
        
        print("✅ Test data created")
        
        # Create request factory for simulating frontend requests
        factory = RequestFactory()
        
        # Test 1: Initial course listing (frontend CourseCard display)
        print("\n🔍 Test 1: Initial Course Listing")
        
        request = factory.get('/')
        request.user = student
        
        serializer = CourseSerializer(course, context={'request': request})
        course_data = serializer.data
        
        # Check all the fields frontend expects
        assert 'user_enrollment_status' in course_data
        assert 'user_can_enroll' in course_data
        assert 'user_enrollment_message' in course_data
        assert course_data['user_enrollment_status'] == 'available'
        assert course_data['user_can_enroll'] == True
        
        print("  ✅ Course listing shows correct initial status")
        
        # Test 2: After enrollment (simulating frontend enrollment action)
        print("\n🔍 Test 2: After Enrollment")
        
        # Simulate enrollment (what happens when frontend calls enrollInCourse)
        enrollment, _ = course.enroll_student(student)
        
        # Simulate frontend refreshing course data
        serializer = CourseSerializer(course, context={'request': request})
        course_data = serializer.data
        
        assert course_data['user_enrollment_status'] == 'enrolled'
        assert course_data['user_can_enroll'] == False
        assert 'Already enrolled' in course_data['user_enrollment_message']
        
        print("  ✅ Course shows enrolled status after enrollment")
        
        # Test 3: After course failure (simulating grade update)
        print("\n🔍 Test 3: After Course Failure")
        
        # Simulate course failure
        enrollment.status = 'failed'
        enrollment.is_active = False
        enrollment.save()
        
        # Frontend refreshes course data
        serializer = CourseSerializer(course, context={'request': request})
        course_data = serializer.data
        
        assert course_data['user_enrollment_status'] == 'retakeable'
        assert course_data['user_can_enroll'] == True
        assert 'Available for retake' in course_data['user_enrollment_message']
        
        print("  ✅ Course shows retakeable status after failure")
        
        # Test 4: Retake enrollment
        print("\n🔍 Test 4: Retake Enrollment")
        
        # Simulate retake enrollment
        enrollment2, _ = course.enroll_student(student)
        
        # Frontend refreshes course data
        serializer = CourseSerializer(course, context={'request': request})
        course_data = serializer.data
        
        assert course_data['user_enrollment_status'] == 'enrolled'
        assert course_data['user_can_enroll'] == False
        assert 'Already enrolled' in course_data['user_enrollment_message']
        assert 'Attempt #2' in course_data['user_enrollment_message']
        
        print("  ✅ Course shows enrolled status for retake with attempt number")
        
        # Test 5: Multiple failures leading to limit
        print("\n🔍 Test 5: Retake Limit Scenario")
        
        # Simulate multiple failures
        enrollment2.status = 'failed'
        enrollment2.is_active = False
        enrollment2.save()
        
        # Third attempt
        enrollment3, _ = course.enroll_student(student)
        enrollment3.status = 'failed'
        enrollment3.is_active = False
        enrollment3.save()
        
        # Frontend refreshes course data (should show limit exceeded)
        serializer = CourseSerializer(course, context={'request': request})
        course_data = serializer.data
        
        assert course_data['user_enrollment_status'] == 'retake_limit_exceeded'
        assert course_data['user_can_enroll'] == False
        assert 'Maximum retake attempts exceeded' in course_data['user_enrollment_message']
        
        print("  ✅ Course shows retake limit exceeded correctly")
        
        # Test 6: Course completion scenario
        print("\n🔍 Test 6: Course Completion Scenario")
        
        # Create a new student for completion test
        student2 = User.objects.create_user(
            username="frontend_test_student2", email="<EMAIL>", 
            password="password123", role="student"
        )
        
        # Enroll and complete
        enrollment4, _ = course.enroll_student(student2)
        enrollment4.status = 'completed'
        enrollment4.is_active = False
        enrollment4.letter_grade = 'A'
        enrollment4.save()
        
        # Frontend request for student2
        request.user = student2
        serializer = CourseSerializer(course, context={'request': request})
        course_data = serializer.data
        
        assert course_data['user_enrollment_status'] == 'completed'
        assert course_data['user_can_enroll'] == False
        assert 'Already completed with grade A' in course_data['user_enrollment_message']
        
        print("  ✅ Course shows completed status correctly")
        
        # Test 7: Frontend data transformer compatibility
        print("\n🔍 Test 7: Frontend Data Transformer Compatibility")
        
        # Test that the data structure matches what frontend expects
        expected_fields = [
            'id', 'title', 'code', 'description', 'credit_hours',
            'user_enrollment_status', 'user_can_enroll', 'user_enrollment_message'
        ]
        
        for field in expected_fields:
            assert field in course_data, f"Missing expected field: {field}"
        
        print("  ✅ All expected frontend fields present")
        
        print("\n🎉 ALL FRONTEND INTEGRATION TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        User.objects.filter(username__startswith='frontend_test_').delete()
        Department.objects.filter(code='FRONTEND').delete()

if __name__ == "__main__":
    success = test_frontend_integration()
    sys.exit(0 if success else 1)
