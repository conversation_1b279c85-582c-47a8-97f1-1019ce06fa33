#!/usr/bin/env python
"""
Simple test to verify enrollment system fixes are working
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from courses.models import Course, Enrollment, Department

User = get_user_model()

def simple_test():
    """Simple test to verify the fixes work"""
    print("🧪 Simple Enrollment Test...")
    
    # Clean up
    User.objects.filter(username__startswith='simple_test_').delete()
    Department.objects.filter(code='SIMPLE').delete()
    
    try:
        # Create test data
        department = Department.objects.create(name="Simple Test", code="SIMPLE")
        teacher = User.objects.create_user(
            username="simple_test_teacher", email="<EMAIL>", 
            password="password123", role="teacher"
        )
        student = User.objects.create_user(
            username="simple_test_student", email="<EMAIL>", 
            password="password123", role="student"
        )
        course = Course.objects.create(
            title="Simple Test Course", code="SIMPLE101", description="Test",
            department=department, instructor=teacher, level="undergraduate", 
            credit_hours=3, semester="spring", year=2025, max_students=30, 
            is_active=True, is_published=True,
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=90)
        )
        
        print("✅ Test data created")
        
        # Test 1: Enroll student
        enrollment1, _ = course.enroll_student(student)
        print(f"✅ Test 1 PASSED: Student enrolled (attempt {enrollment1.attempt_number})")
        
        # Test 2: Check only one active enrollment
        active_count = Enrollment.objects.filter(
            student=student, course=course, is_active=True
        ).count()
        assert active_count == 1, f"Expected 1 active enrollment, got {active_count}"
        print("✅ Test 2 PASSED: Only one active enrollment exists")
        
        # Test 3: Simulate failure and retake
        enrollment1.status = 'failed'
        enrollment1.is_active = False
        enrollment1.save()
        
        enrollment2, _ = course.enroll_student(student)
        print(f"✅ Test 3 PASSED: Student retook course (attempt {enrollment2.attempt_number})")
        
        # Test 4: Check enrollment lifecycle
        active_count = Enrollment.objects.filter(
            student=student, course=course, is_active=True
        ).count()
        assert active_count == 1, f"Expected 1 active enrollment after retake, got {active_count}"
        print("✅ Test 4 PASSED: Enrollment lifecycle managed correctly")
        
        # Test 5: Test retake limit (create 3 failed attempts total)
        enrollment2.status = 'failed'
        enrollment2.is_active = False
        enrollment2.save()
        
        enrollment3, _ = course.enroll_student(student)
        enrollment3.status = 'failed'
        enrollment3.is_active = False
        enrollment3.save()
        
        # Now we have 3 failed attempts, 4th should be blocked
        can_enroll, message = course.can_enroll(student)
        assert not can_enroll, "Should not be able to enroll after 3 failed attempts"
        assert "Maximum retake attempts exceeded" in message
        print("✅ Test 5 PASSED: Retake limit enforced correctly")
        
        print("\n🎉 ALL TESTS PASSED! Enrollment system fixes are working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        User.objects.filter(username__startswith='simple_test_').delete()
        Department.objects.filter(code='SIMPLE').delete()

if __name__ == "__main__":
    success = simple_test()
    sys.exit(0 if success else 1)
